// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

declare global {
  namespace Cypress {
    interface Chainable {
      // Add custom commands here
      login(email: string, password: string): Chainable<void>;
      logout(): Chainable<void>;
      // Add more custom commands as needed
    }
  }
}

// Prevent TypeScript from reading file as legacy script
export {};
