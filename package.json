{"name": "a14-browser", "version": "1.0.0", "description": "Advanced web browser with enhanced security and performance", "main": "dist/main.js", "scripts": {"start": "electron .", "dev": "vite", "dev:debug": "cross-env NODE_ENV=development DEBUG=* vite", "build": "npm run clean && npm run type-check && vite build", "build:prod": "cross-env NODE_ENV=production npm run build", "build:analyze": "npm run build && npm run analyze", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:ci": "jest --ci --coverage --watchAll=false --maxWorkers=2", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:performance": "jest --testPathPattern=performance", "test:security": "jest --testPathPattern=security", "test:accessibility": "jest --testPathPattern=accessibility", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:staged": "lint-staged", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md,css,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md,css,scss}\"", "prepare": "husky install", "commit": "git-cz", "release": "standard-version", "release:major": "standard-version --release-as major", "release:minor": "standard-version --release-as minor", "release:patch": "standard-version --release-as patch", "docs": "typedoc --out docs src", "docs:serve": "http-server docs -p 8080", "analyze": "source-map-explorer 'dist/assets/*.js'", "security-audit": "npm audit && npm audit --audit-level high", "security-scan": "npm run security-audit && snyk test", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "clean": "rimraf dist coverage .nyc_output", "clean:all": "npm run clean && rimraf node_modules package-lock.json", "prebuild": "npm run clean && npm run type-check", "postbuild": "npm run analyze", "precommit": "npm run lint:staged && npm run type-check", "prepush": "npm run test:ci", "validate": "npm run type-check && npm run lint && npm run test:ci", "docker:build": "docker build -t a14-browser .", "docker:run": "docker run -p 3000:3000 a14-browser", "docker:dev": "docker-compose up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build", "benchmark": "node scripts/benchmark.js", "profile": "node --prof scripts/profile.js", "update-deps": "ncu -u && npm install", "check-updates": "ncu"}, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.9", "@reduxjs/toolkit": "^2.0.0", "@tanstack/react-query": "^5.62.7", "axios": "^1.10.0", "date-fns": "^4.1.0", "electron": "^37.1.0", "electron-store": "^10.1.0", "framer-motion": "^11.15.0", "globals": "^16.2.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^2.7.0", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^4.1.2", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-i18next": "^15.1.3", "react-redux": "^9.0.0", "react-router-dom": "^7.6.3", "react-virtualized": "^9.22.5", "recharts": "^2.13.3", "uuid": "^11.0.3", "winston": "^3.11.0", "zustand": "^5.0.2", "zod": "^3.25.67"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@opentelemetry/api": "^1.4.1", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@testing-library/cypress": "^10.0.2", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@types/archiver": "^6.0.3", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.13", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.0", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "cypress": "^14.5.0", "cypress-axe": "^1.5.0", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^28.14.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^2.0.4", "http-server": "^14.1.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^4.0.2", "joi": "^17.9.2", "lint-staged": "^16.1.2", "lru-cache": "^7.14.1", "npm-check-updates": "^17.1.11", "postcss": "^8.5.6", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "snyk": "^1.1296.0", "source-map-explorer": "^2.5.0", "standard-version": "^9.5.0", "storybook": "^8.4.7", "tailwindcss": "^4.1.11", "ts-jest": "^29.1.0", "typedoc": "^0.28.6", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0", "vite-plugin-checker": "^0.8.0", "vite-plugin-compression2": "^1.3.0", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-pwa": "^0.21.1"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,scss,html}": ["prettier --write"], "*.{ts,tsx}": ["tsc --noEmit"]}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/a14-browser.git"}, "keywords": ["browser", "electron", "security", "performance", "accessibility", "privacy", "web-browser", "typescript", "react", "cross-platform", "open-source", "modern-browser", "enterprise", "professional", "world-class"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/yourusername/a14-browser/issues"}, "homepage": "https://github.com/yourusername/a14-browser#readme"}