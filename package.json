{"name": "a14-browser", "version": "1.0.0", "description": "Advanced web browser with enhanced security and performance", "main": "dist/main.js", "scripts": {"start": "electron .", "dev": "vite", "build": "tsc && vite build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "node --max-old-space-size=8192 ./node_modules/eslint/bin/eslint.js . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "prepare": "husky install", "commit": "git-cz", "release": "standard-version", "docs": "typedoc --out docs src", "analyze": "source-map-explorer 'dist/assets/*.js'", "security-audit": "npm audit", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "npm run analyze"}, "dependencies": {"@reduxjs/toolkit": "^2.0.0", "axios": "^1.10.0", "electron": "^37.1.0", "electron-store": "^10.1.0", "globals": "^16.2.0", "i18next": "^25.2.1", "jwt-decode": "^4.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.0.0", "react-router-dom": "^7.6.3", "winston": "^3.11.0", "zod": "^3.25.67"}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@opentelemetry/api": "^1.4.1", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^16.3.0", "@types/archiver": "^6.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.0", "commitizen": "^4.3.0", "cypress": "^14.5.0", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^28.14.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "joi": "^17.9.2", "lint-staged": "^16.1.2", "lru-cache": "^7.14.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "rimraf": "^6.0.1", "source-map-explorer": "^2.5.0", "standard-version": "^9.5.0", "tailwindcss": "^4.1.11", "ts-jest": "^29.1.0", "typedoc": "^0.28.6", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^7.0.0", "vite-plugin-electron": "^0.29.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/a14-browser.git"}, "keywords": ["browser", "electron", "security", "performance", "accessibility"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/yourusername/a14-browser/issues"}, "homepage": "https://github.com/yourusername/a14-browser#readme"}