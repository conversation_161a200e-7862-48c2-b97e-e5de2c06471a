# 📐 A14 Browser Architecture

This document describes the application architecture, tech stack, and key design principles for A14 Browser. It serves as the **single source of truth** for all developers.

## 1. Core Principles

- **Security First:** All architectural decisions are made with a "secure by default" mindset.
- **Modularity and Separation of Concerns (SoC):** The code is organized into independent, reusable modules with clearly defined responsibilities.
- **Performance:** The application must be fast, responsive, and use resources efficiently.
- **Scalability:** The architecture must allow for the easy addition of new features without breaking existing ones.

## 2. Electron Process Model

A14 Browser uses the standard multi-process architecture of Electron to ensure security and stability.

```mermaid
graph TD
    subgraph "Operating System"
        MainProcess["Main Process (Node.js)"]
    end

    subgraph "Песочница Chromium"
    subgraph "Chromium Sandbox"
        RendererProcess["Renderer Process (UI)"]
        PreloadScript["Preload Script"]
    end

    MainProcess -- Secure IPC --> PreloadScript
    PreloadScript -- contextBridge.exposeInMainWorld --> RendererProcess

    style MainProcess fill:#333,stroke:#fff,stroke-width:2px,color:#fff
    style RendererProcess fill:#007acc,stroke:#fff,stroke-width:2px,color:#fff
    style PreloadScript fill:#f0db4f,stroke:#333,stroke-width:2px,color:#333
```

### Main Process (`src/main`)
- **Ответственность:** Управляет жизненным циклом приложения, окнами (`BrowserWindow`), нативными меню, диалогами и всеми взаимодействиями с ОС. Здесь выполняется вся "тяжелая" логика, требующая доступа к Node.js API (например, работа с файловой системой, управление сессиями, сетевые запросы).
- **Окружение:** Полное окружение Node.js.
- **Ключевые компоненты:**
  - `Service Container`: Управляет жизненным циклом сервисов.
  - `SettingsManager`: Работает с конфигурационными файлами.
  - `SessionManager`: Управляет сессиями браузера.
  - `UpdateManager`: Проверяет и устанавливает обновления.

### Renderer Process (`src/renderer`)
- **Ответственность:** Отображение пользовательского интерфейса (UI). Здесь работает React-приложение.
- **Окружение:** Стандартное окружение веб-браузера. **Нет прямого доступа к Node.js API.**
- **Ключевые компоненты:**
  - React-компоненты (UI).
  - Хранилища состояния (Zustand).
  - Менеджер запросов к API (TanStack Query).

### Preload Script (`src/preload`)
- **Ответственность:** Безопасный мост (`Context Bridge`) между Main и Renderer процессами. Он выборочно предоставляет функции из Main процесса в Renderer, не раскрывая глобальные объекты `window` или `node`.
- **Окружение:** Имеет доступ к `window`, `document` и ограниченному набору Node.js API.

## 3. Структура директорий

Структура проекта отражает разделение процессов:

```
src/
├── main/          # Код для Main процесса (Node.js)
│   ├── services/  # Изолированные сервисы (SettingsManager, etc.)
│   └── index.ts   # Точка входа Main процесса
│
├── renderer/      # Код для Renderer процесса (React)
│   ├── api/       # Обертки для вызова IPC-методов
│   ├── assets/    # Статические ресурсы (иконки, шрифты)
│   ├── components/# Переиспользуемые UI-компоненты
│   ├── hooks/     # Переиспользуемые React-хуки
│   ├── pages/     # Компоненты-страницы (Настройки, История)
│   ├── stores/    # Хранилища состояния (Zustand)
│   └── index.tsx  # Точка входа React-приложения
│
├── preload/       # Preload-скрипты
│   └── index.ts   # Основной preload-скрипт
│
└── shared/        # Общий код и типы для всех процессов
    └── types/     # TypeScript-типы и интерфейсы
```

## 4. Управление состоянием

Мы используем комбинацию двух библиотек для эффективного управления состоянием:

- **Zustand:** Для локального и глобального UI-состояния (например, открыта ли боковая панель, текущая тема оформления). Он прост, быстр и требует минимум бойлерплейта.
- **TanStack Query (React Query):** Для управления серверным состоянием (данные, получаемые из Main процесса или внешних API). Он автоматически обрабатывает кэширование, фоновое обновление, инвалидацию данных и оптимистичные обновления.

```mermaid
sequenceDiagram
    participant Component as React Component
    participant TQuery as TanStack Query
    participant Zustand as Zustand Store
    participant Preload as Preload API
    participant Main as Main Process

    Component->>Zustand: toggleSidebar()
    Zustand-->>Component: new UI state

    Component->>TQuery: useQuery(['settings'], api.getSettings)
    TQuery->>Preload: api.getSettings()
    Preload->>Main: ipcRenderer.invoke('get-settings')
    Main-->>Preload: settings data
    Preload-->>TQuery: settings data
    TQuery-->>Component: return { data, isLoading, ... }
```

## 5. Технологический стек

- **Платформа:** **Electron**
- **Язык:** **TypeScript** (strict mode)
- **Frontend:** **React 18**
- **Сборка:** **Vite**
- **UI-библиотека:** **Material-UI (MUI)**
- **Управление состоянием:** **Zustand** & **TanStack Query**
- **Тестирование:** **Vitest** (Unit/Integration), **Playwright** (E2E)
- **Качество кода:** **ESLint** & **Prettier**

Этот стек выбран за его производительность, надежность и превосходный опыт разработки (Developer Experience).

## 6. Безопасность

- **Context Isolation & Sandbox:** Включены по умолчанию для всех `BrowserWindow`.
- **Content Security Policy (CSP):** Строгая политика для ограничения загрузки ресурсов (скрипты, стили) только из доверенных источников.
- **IPC:** Все взаимодействия между процессами происходят через `contextBridge` и `ipcRenderer.invoke/on`, что предотвращает утечку привилегий в Renderer.

---