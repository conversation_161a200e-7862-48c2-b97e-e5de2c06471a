# NovaBrowser Internationalization (i18n)

## Overview

NovaBrowser's internationalization system provides a comprehensive solution for supporting multiple languages and regional formats. The system is built on top of i18next and includes utilities for translations, date formatting, number formatting, and more.

## Supported Languages

### Core Languages
- English (en) - Default

## Implementation

### Basic Usage

```typescript
import { t } from '../utils/i18n';

// Simple translation
const message = t('common.welcome');

// Translation with variables
const welcomeMessage = t('common.welcomeUser', { name: 'John' });

// Translation with fallback
const message = tWithFallback('common.hello', 'Hello');
```

### Date Formatting

```typescript
import { formatDate } from '../utils/i18n';

// Format current date
const today = formatDate(new Date());

// Format with custom options
const options = {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
};
const formattedDate = formatDate(new Date(), options);
```

### Number Formatting

```typescript
import { formatNumber, formatCurrency } from '../utils/i18n';

// Format number
const number = formatNumber(1234.56);

// Format currency
const price = formatCurrency(1234.56, 'USD');
```

### Pluralization

```typescript
import { pluralize } from '../utils/i18n';

// Handle different plural forms
const items = pluralize('items', 5);
```

## Translation Files

Translation files are stored in JSON format in the `src/i18n/locales` directory. Each language has its own file (e.g., `en.json`, `ru.json`).

Example structure:
```json
{
  "common": {
    "welcome": "Welcome",
    "welcomeUser": "Welcome, {{name}}!",
    "items": "{{count}} items",
    "items_0": "No items",
    "items_1": "1 item",
    "items_other": "{{count}} items"
  }
}
```

## Language Switching

### Using the Language Switcher Component

```typescript
import { LanguageSwitcher } from '../shared/components/LanguageSwitcher';

function App() {
  return (
    <div>
      <LanguageSwitcher />
      {/* Your app content */}
    </div>
  );
}
```

### Programmatic Language Switching

```typescript
import { setLanguage } from '../utils/i18n';

// Change language
await setLanguage('en');
```

## RTL Support

The system automatically handles right-to-left (RTL) languages. When switching to an RTL language, the document direction is updated accordingly.

```typescript
import { isRTL } from '../utils/i18n';

// Check if current language is RTL
const isRTL = isRTL();
```

## Translation Management

### Updating Translations

To update missing translations in all language files:

```bash
npm run i18n:update
```

### Validating Translations

To validate translation files and check for missing keys:

```bash
npm run i18n:validate
```

## Best Practices

### 1. Translation Keys

- Use hierarchical keys for better organization
- Keep keys descriptive and consistent
- Use namespaces for different features

Example:
```json
{
  "common": {
    "buttons": {
      "save": "Save",
      "cancel": "Cancel"
    }
  }
}
```

### 2. Variables

- Use meaningful variable names
- Provide default values when possible
- Document expected variables

Example:
```typescript
t('welcome', { name: 'John', role: 'user' });
```

### 3. Pluralization

- Use the correct plural forms for each language
- Test with different numbers
- Consider edge cases (zero, one, many)

Example:
```json
{
  "items": {
    "zero": "No items",
    "one": "1 item",
    "other": "{{count}} items"
  }
}
```

### 4. Date and Number Formatting

- Use appropriate formats for each locale
- Consider cultural differences
- Test with different values

Example:
```typescript
formatDate(date, { 
  year: 'numeric',
  month: 'long',
  day: 'numeric'
});
```

## Development Tools

### Translation Extraction

The system includes tools for:
- Extracting translation keys from code
- Validating translation files
- Managing missing translations
- Formatting translation files

### Debug Mode

Enable debug mode to see missing translations:

```typescript
import { enableTranslationDebug } from '../utils/i18n';

enableTranslationDebug();
```

## Testing

### Unit Tests

Run tests for i18n utilities:

```bash
npm test
```

### Translation Tests

Test translation coverage:

```bash
npm run i18n:validate
```

## Resources

### Translation Files
- `src/i18n/locales/en.json` - English translations

### Components
- `src/shared/components/LanguageSwitcher.tsx` - Language switcher component
- `src/providers/I18nProvider.tsx` - i18n provider component

### Utilities
- `src/utils/i18n.ts` - i18n utility functions
- `scripts/update-translations.js` - Translation management script

## Contributing

### Adding New Languages

1. Create a new translation file in `src/i18n/locales`
2. Add the language to the supported languages list
3. Update the language configuration
4. Add translations for all keys
5. Test the translations
6. Submit a pull request

### Translation Guidelines

1. Maintain consistent terminology
2. Use proper grammar and punctuation
3. Consider cultural context
4. Test with native speakers
5. Keep translations up to date

## Troubleshooting

### Common Issues

1. Missing Translations
   - Run `npm run i18n:validate` to check for missing keys
   - Add missing translations to the language files
   - Use fallback translations when necessary

2. Formatting Issues
   - Check date and number formats
   - Verify pluralization rules
   - Test with different locales

3. RTL Problems
   - Verify RTL support in components
   - Test with RTL languages
   - Check CSS direction properties

## Version History

### 1.0.0
- Initial release
- Basic translation support
- English and Russian languages
- Date and number formatting
- Pluralization support
- RTL support
- Translation management tools

## Support

For questions and issues:
- GitHub Issues: [NovaBrowser Issues](https://github.com/novabrowser/novabrowser/issues)
- Documentation: [NovaBrowser Docs](https://novabrowser.org/docs)
- Community: [NovaBrowser Community](https://community.novabrowser.org) 