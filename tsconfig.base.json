{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"], "@config/*": ["src/config/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"], "@constants/*": ["src/constants/*"], "@locales/*": ["src/locales/*"], "@validation/*": ["src/validation/*"], "@security/*": ["src/security/*"], "@analytics/*": ["src/analytics/*"], "@performance/*": ["src/performance/*"], "@accessibility/*": ["src/accessibility/*"], "@testing/*": ["src/testing/*"], "@monitoring/*": ["src/monitoring/*"], "@logging/*": ["src/logging/*"], "@documentation/*": ["src/documentation/*"], "@core/*": ["src/core/*"], "@features/*": ["src/features/*"], "@shared/*": ["src/shared/*"], "@providers/*": ["src/providers/*"], "@api/*": ["src/api/*"], "@schemas/*": ["src/schemas/*"]}}}