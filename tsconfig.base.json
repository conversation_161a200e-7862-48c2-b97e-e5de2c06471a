{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "useUnknownInCatchVariables": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "alwaysStrict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@utils/*": ["src/utils/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@types/*": ["src/types/*"], "@config/*": ["src/config/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"], "@constants/*": ["src/constants/*"], "@locales/*": ["src/locales/*"], "@validation/*": ["src/validation/*"], "@security/*": ["src/security/*"], "@analytics/*": ["src/analytics/*"], "@performance/*": ["src/performance/*"], "@accessibility/*": ["src/accessibility/*"], "@testing/*": ["src/testing/*"], "@monitoring/*": ["src/monitoring/*"], "@logging/*": ["src/logging/*"], "@documentation/*": ["src/documentation/*"], "@core/*": ["src/core/*"], "@features/*": ["src/features/*"], "@shared/*": ["src/shared/*"], "@providers/*": ["src/providers/*"], "@api/*": ["src/api/*"], "@schemas/*": ["src/schemas/*"], "@extensions/*": ["src/extensions/*"], "@i18n/*": ["src/i18n/*"], "@network/*": ["src/network/*"], "@storage/*": ["src/storage/*"], "@sync/*": ["src/sync/*"], "@ui/*": ["src/ui/*"], "@workers/*": ["src/workers/*"], "@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@preload/*": ["src/preload/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "jest", "cypress", "@testing-library/jest-dom"]}, "include": ["src/**/*", "types/**/*", "**/*.d.ts"], "exclude": ["node_modules", "dist", "build", "coverage", ".nyc_output", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "cypress/downloads", "storybook-static"]}