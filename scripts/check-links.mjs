import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';

// --- Configuration ---
const ROOT_DIR = process.cwd();
const IGNORED_PATTERNS = ['**/node_modules/**'];
const TIMEOUT = 10000; // 10-second timeout for network requests

// Regex to find markdown links [text](url) and bare URLs.
const LINK_REGEX = /\[[^\]]+\]\(([^)]+)\)|(https?:\/\/[^\s`'"]+)/g;

// A cache to avoid re-checking the same URL multiple times.
const checkedUrls = new Map();

/**
 * Checks an external URL using a HEAD request, with a GET fallback.
 * @param {string} url The URL to check.
 * @returns {Promise<{status: 'ok' | 'broken', code?: number, error?: string}>}
 */
async function checkExternalUrl(url) {
  if (checkedUrls.has(url)) {
    return checkedUrls.get(url);
  }

  console.log(`Checking: ${url}`);
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT);

    const response = await fetch(url, {
      method: 'HEAD', // HEAD is faster as it doesn't download the body.
      signal: controller.signal,
      headers: { 'User-Agent': 'A14-Browser-Link-Checker/1.0' },
    });

    clearTimeout(timeoutId);

    // Some servers don't support HEAD, so we retry with GET on failure.
    if (response.status >= 400 && (response.status === 405 || response.status === 501)) {
      console.log(`  -> HEAD failed (${response.status}), retrying with GET...`);
      const getResponse = await fetch(url, { signal: controller.signal, headers: { 'User-Agent': 'A14-Browser-Link-Checker/1.0' } });
      const result = getResponse.ok ? { status: 'ok' } : { status: 'broken', code: getResponse.status };
      checkedUrls.set(url, result);
      return result;
    }

    const result = response.ok ? { status: 'ok' } : { status: 'broken', code: response.status };
    checkedUrls.set(url, result);
    return result;
  } catch (error) {
    const result = { status: 'broken', error: error.message };
    checkedUrls.set(url, result);
    return result;
  }
}

/**
 * Checks if an internal file link points to an existing file.
 * @param {string} linkPath The relative path from the markdown file.
 * @param {string} sourceFile The markdown file containing the link.
 * @returns {Promise<{status: 'ok' | 'broken', error?: string}>}
 */
async function checkInternalLink(linkPath, sourceFile) {
  try {
    const absolutePath = path.resolve(path.dirname(sourceFile), linkPath);
    await fs.access(absolutePath);
    return { status: 'ok' };
  } catch {
    return { status: 'broken', error: 'File not found' };
  }
}

async function main() {
  const files = await glob('**/*.md', { cwd: ROOT_DIR, ignore: IGNORED_PATTERNS });
  const brokenLinks = [];
  let totalLinks = 0;

  for (const file of files) {
    const content = await fs.readFile(file, 'utf-8');
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      for (const match of line.matchAll(LINK_REGEX)) {
        const url = match[1] || match[2];
        if (!url || url.startsWith('#')) continue; // Ignore anchor links

        totalLinks++;
        const result = url.startsWith('http') ? await checkExternalUrl(url) : await checkInternalLink(url, file);

        if (result.status === 'broken') {
          brokenLinks.push({ file, line: i + 1, url, reason: result.code ? `HTTP ${result.code}` : result.error });
        }
      }
    }
  }

  console.log(`\n--- Link Check Complete ---\nChecked ${totalLinks} links in ${files.length} files.`);

  if (brokenLinks.length > 0) {
    console.error(`\n❌ Found ${brokenLinks.length} broken links:`);
    brokenLinks.forEach(link => {
      console.error(`\n- In "${link.file}" (line ${link.line}):\n  Link: ${link.url}\n  Reason: ${link.reason}`);
    });
    process.exit(1);
  } else {
    console.log('\n✅ All links are working!');
    process.exit(0);
  }
}

main().catch(err => {
  console.error('An unexpected error occurred:', err);
  process.exit(1);
});