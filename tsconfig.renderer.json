{"extends": "./tsconfig.base.json", "compilerOptions": {"composite": true, "outDir": "./dist/renderer", "rootDir": "./src/renderer", "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo-renderer"}, "include": ["src/renderer/**/*", "src/shared/**/*", "src/types/**/*", "src/components/**/*", "src/hooks/**/*", "src/providers/**/*", "src/store/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/main", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}