import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import React from 'react';

import { BrowserSettings } from '../../stores/useSettingsStore';

interface AppearanceSettingsProps {
  settings: BrowserSettings;
  onSettingChange: <K extends keyof BrowserSettings>(key: K, value: BrowserSettings[K]) => void;
}

const AppearanceSettings: React.FC<AppearanceSettingsProps> = ({ settings, onSettingChange }) => {
  return (
    <FormGroup sx={{ gap: 2, mt: 2 }}>
      <FormControlLabel control={<Checkbox checked={settings.darkMode} onChange={e => onSettingChange('darkMode', e.target.checked)} />} label="Тёмная тема" />
    </FormGroup>
  );
};

export default AppearanceSettings;