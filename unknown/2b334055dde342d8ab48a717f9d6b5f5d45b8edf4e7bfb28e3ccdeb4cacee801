import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import { Box, IconButton, InputAdornment, ListItem, ListItemText, TextField, Typography } from '@mui/material';
import React, { useMemo, useState } from 'react';
import { FixedSizeList as List } from 'react-window';

import { getService } from '../../serviceContainer';
import { useDebounce } from '../../hooks/useDebounce';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import SidePanel from '../Layout/SidePanel';

const BookmarksPanel: React.FC = () => {
  const { bookmarks, isPanelOpen, togglePanel } = useBookmarkStore();
  const bookmarkManager = getService<any>('bookmarkManager');
  const navigationManager = getService<any>('navigationManager');

  if (!isPanelOpen) {
    return null;
  }

  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const filteredBookmarks = useMemo(() => {
    if (!debouncedSearchTerm) {
      return bookmarks;
    }
    const lowercasedFilter = debouncedSearchTerm.toLowerCase();
    return bookmarks.filter(bookmark => bookmark.title.toLowerCase().includes(lowercasedFilter) || bookmark.url.toLowerCase().includes(lowercasedFilter));
  }, [bookmarks, debouncedSearchTerm]);

  const handleNavigate = (url: string) => {
    navigationManager.navigate(url);
    togglePanel(); // Close panel on navigation
  };

  return (
    <SidePanel title="Закладки" isOpen={isPanelOpen} onClose={togglePanel}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <TextField
          fullWidth
          variant="outlined"
          size="small"
          placeholder="Поиск по закладкам..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start"><SearchIcon /></InputAdornment>,
          }}
        />
      </Box>
      <Box sx={{ flex: 1 }}>
        {filteredBookmarks.length > 0 ? (
          <List height={400} itemCount={filteredBookmarks.length} itemSize={56} width="100%">
            {({ index, style }) => {
              const bookmark = filteredBookmarks[index];
              return (
                <ListItem style={style} key={bookmark.id} button secondaryAction={<IconButton edge="end" aria-label="delete" onClick={() => bookmarkManager.removeBookmark(bookmark.id)}><DeleteIcon /></IconButton>}>
                  <ListItemText
                    primary={bookmark.title}
                    secondary={bookmark.url}
                    onClick={() => handleNavigate(bookmark.url)}
                    sx={{ '& .MuiListItemText-secondary': { whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' } }}
                  />
                </ListItem>
              );
            }}
          </List>
        ) : (
          <Typography sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
            {searchTerm ? 'Ничего не найдено' : 'Нет сохраненных закладок'}
          </Typography>
        )}
      </Box>
    </SidePanel>
  );
};

export default BookmarksPanel;