/**
 * Advanced Internationalization Engine
 * Comprehensive i18n system with AI-powered translation and cultural adaptation
 */

import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';

export interface Language {
  code: string; // ISO 639-1 code (e.g., 'en', 'ru', 'zh')
  name: string;
  nativeName: string;
  region?: string; // ISO 3166-1 alpha-2 (e.g., 'US', 'RU', 'CN')
  direction: 'ltr' | 'rtl';
  pluralRules: PluralRule[];
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: string;
  };
  enabled: boolean;
  fallback?: string;
}

export interface PluralRule {
  rule: 'zero' | 'one' | 'two' | 'few' | 'many' | 'other';
  condition: string; // Mathematical condition
}

export interface TranslationKey {
  key: string;
  namespace: string;
  defaultValue: string;
  description?: string;
  context?: string;
  pluralizable?: boolean;
  interpolation?: string[];
  tags?: string[];
}

export interface Translation {
  key: string;
  language: string;
  value: string;
  pluralForms?: Record<string, string>;
  context?: string;
  quality: number; // 0-1 translation quality score
  source: 'manual' | 'ai' | 'crowdsource' | 'machine';
  lastUpdated: Date;
  verified: boolean;
  translator?: string;
}

export interface CulturalAdaptation {
  language: string;
  region: string;
  adaptations: {
    colors: Record<string, string>; // Color preferences
    icons: Record<string, string>; // Icon preferences
    layouts: Record<string, any>; // Layout adjustments
    content: Record<string, string>; // Content modifications
    etiquette: string[]; // Cultural etiquette rules
  };
}

export interface TranslationContext {
  user?: {
    language: string;
    region: string;
    preferences: Record<string, any>;
  };
  page?: {
    type: string;
    category: string;
    audience: string;
  };
  device?: {
    type: 'desktop' | 'mobile' | 'tablet';
    screenSize: string;
  };
  time?: {
    timezone: string;
    locale: string;
  };
}

export class InternationalizationEngine extends EventEmitter {
  private languages = new Map<string, Language>();
  private translations = new Map<string, Map<string, Translation>>();
  private translationKeys = new Map<string, TranslationKey>();
  private culturalAdaptations = new Map<string, CulturalAdaptation>();
  private currentLanguage = 'en';
  private fallbackLanguage = 'en';
  private aiTranslationEnabled = true;
  private cacheEnabled = true;
  private translationCache = new Map<string, string>();

  constructor() {
    super();
    this.initializeDefaultLanguages();
    this.loadTranslations();
  }

  /**
   * Add a new language
   */
  addLanguage(language: Language): void {
    this.languages.set(language.code, language);
    
    if (!this.translations.has(language.code)) {
      this.translations.set(language.code, new Map());
    }

    logger.info(`Added language: ${language.name} (${language.code})`);
    this.emit('language-added', language);
  }

  /**
   * Set current language
   */
  setLanguage(languageCode: string): boolean {
    const language = this.languages.get(languageCode);
    if (!language || !language.enabled) {
      logger.warn(`Language ${languageCode} not found or disabled`);
      return false;
    }

    const previousLanguage = this.currentLanguage;
    this.currentLanguage = languageCode;
    
    // Clear cache when language changes
    if (this.cacheEnabled) {
      this.translationCache.clear();
    }

    logger.info(`Language changed from ${previousLanguage} to ${languageCode}`);
    this.emit('language-changed', { from: previousLanguage, to: languageCode });
    
    return true;
  }

  /**
   * Register translation key
   */
  registerKey(
    key: string,
    namespace: string,
    defaultValue: string,
    options: {
      description?: string;
      context?: string;
      pluralizable?: boolean;
      interpolation?: string[];
      tags?: string[];
    } = {}
  ): void {
    const translationKey: TranslationKey = {
      key,
      namespace,
      defaultValue,
      description: options.description,
      context: options.context,
      pluralizable: options.pluralizable || false,
      interpolation: options.interpolation || [],
      tags: options.tags || [],
    };

    const fullKey = `${namespace}.${key}`;
    this.translationKeys.set(fullKey, translationKey);

    logger.debug(`Registered translation key: ${fullKey}`);
    this.emit('key-registered', translationKey);
  }

  /**
   * Add translation
   */
  addTranslation(
    key: string,
    language: string,
    value: string,
    options: {
      pluralForms?: Record<string, string>;
      context?: string;
      quality?: number;
      source?: Translation['source'];
      translator?: string;
    } = {}
  ): void {
    const translation: Translation = {
      key,
      language,
      value,
      pluralForms: options.pluralForms,
      context: options.context,
      quality: options.quality || 1.0,
      source: options.source || 'manual',
      lastUpdated: new Date(),
      verified: options.source === 'manual',
      translator: options.translator,
    };

    let languageTranslations = this.translations.get(language);
    if (!languageTranslations) {
      languageTranslations = new Map();
      this.translations.set(language, languageTranslations);
    }

    languageTranslations.set(key, translation);

    logger.debug(`Added translation for ${key} in ${language}`);
    this.emit('translation-added', translation);
  }

  /**
   * Translate text
   */
  translate(
    key: string,
    options: {
      language?: string;
      context?: TranslationContext;
      interpolation?: Record<string, any>;
      count?: number;
      fallback?: string;
    } = {}
  ): string {
    const language = options.language || this.currentLanguage;
    const cacheKey = this.generateCacheKey(key, language, options);

    // Check cache first
    if (this.cacheEnabled && this.translationCache.has(cacheKey)) {
      return this.translationCache.get(cacheKey)!;
    }

    let result = this.getTranslation(key, language, options);

    // Apply cultural adaptations
    if (options.context) {
      result = this.applyCulturalAdaptations(result, language, options.context);
    }

    // Apply interpolation
    if (options.interpolation) {
      result = this.interpolateString(result, options.interpolation);
    }

    // Cache result
    if (this.cacheEnabled) {
      this.translationCache.set(cacheKey, result);
    }

    return result;
  }

  /**
   * Translate with pluralization
   */
  translatePlural(
    key: string,
    count: number,
    options: {
      language?: string;
      context?: TranslationContext;
      interpolation?: Record<string, any>;
      fallback?: string;
    } = {}
  ): string {
    const language = options.language || this.currentLanguage;
    const languageObj = this.languages.get(language);
    
    if (!languageObj) {
      return this.translate(key, { ...options, count });
    }

    const pluralRule = this.getPluralRule(count, languageObj);
    const pluralKey = `${key}.${pluralRule}`;

    // Try to get plural form
    const languageTranslations = this.translations.get(language);
    const translation = languageTranslations?.get(key);

    if (translation?.pluralForms?.[pluralRule]) {
      let result = translation.pluralForms[pluralRule];
      
      if (options.interpolation) {
        result = this.interpolateString(result, { ...options.interpolation, count });
      }
      
      return result;
    }

    // Fallback to regular translation
    return this.translate(key, { ...options, count });
  }

  /**
   * Get available languages
   */
  getAvailableLanguages(): Language[] {
    return Array.from(this.languages.values()).filter(lang => lang.enabled);
  }

  /**
   * Get translation coverage for a language
   */
  getTranslationCoverage(language: string): {
    total: number;
    translated: number;
    coverage: number;
    missing: string[];
  } {
    const totalKeys = this.translationKeys.size;
    const languageTranslations = this.translations.get(language);
    const translated = languageTranslations?.size || 0;
    const coverage = totalKeys > 0 ? (translated / totalKeys) * 100 : 0;

    const missing: string[] = [];
    for (const key of this.translationKeys.keys()) {
      if (!languageTranslations?.has(key)) {
        missing.push(key);
      }
    }

    return { total: totalKeys, translated, coverage, missing };
  }

  /**
   * Auto-translate missing keys using AI
   */
  async autoTranslate(
    targetLanguage: string,
    options: {
      sourceLanguage?: string;
      quality?: 'fast' | 'balanced' | 'high';
      context?: string;
      maxKeys?: number;
    } = {}
  ): Promise<number> {
    if (!this.aiTranslationEnabled) {
      throw new Error('AI translation is disabled');
    }

    const sourceLanguage = options.sourceLanguage || this.fallbackLanguage;
    const coverage = this.getTranslationCoverage(targetLanguage);
    const keysToTranslate = coverage.missing.slice(0, options.maxKeys || 100);

    let translatedCount = 0;

    for (const key of keysToTranslate) {
      try {
        const sourceTranslation = this.getTranslation(key, sourceLanguage);
        if (!sourceTranslation) continue;

        const translatedText = await this.performAITranslation(
          sourceTranslation,
          sourceLanguage,
          targetLanguage,
          options.quality || 'balanced',
          options.context
        );

        this.addTranslation(key, targetLanguage, translatedText, {
          source: 'ai',
          quality: this.calculateTranslationQuality(translatedText, options.quality),
        });

        translatedCount++;
        
        logger.debug(`AI translated ${key} to ${targetLanguage}`);
      } catch (error) {
        logger.error(`Failed to AI translate ${key}`, error);
      }
    }

    logger.info(`AI translated ${translatedCount} keys to ${targetLanguage}`);
    this.emit('auto-translation-completed', { targetLanguage, translatedCount });

    return translatedCount;
  }

  /**
   * Add cultural adaptation
   */
  addCulturalAdaptation(adaptation: CulturalAdaptation): void {
    const key = `${adaptation.language}_${adaptation.region}`;
    this.culturalAdaptations.set(key, adaptation);
    
    logger.info(`Added cultural adaptation for ${adaptation.language}-${adaptation.region}`);
    this.emit('cultural-adaptation-added', adaptation);
  }

  /**
   * Format date according to language settings
   */
  formatDate(date: Date, language?: string): string {
    const lang = language || this.currentLanguage;
    const languageObj = this.languages.get(lang);
    
    if (!languageObj) {
      return date.toLocaleDateString();
    }

    try {
      const locale = languageObj.region ? `${lang}-${languageObj.region}` : lang;
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      return date.toLocaleDateString();
    }
  }

  /**
   * Format number according to language settings
   */
  formatNumber(number: number, language?: string): string {
    const lang = language || this.currentLanguage;
    const languageObj = this.languages.get(lang);
    
    if (!languageObj) {
      return number.toLocaleString();
    }

    try {
      const locale = languageObj.region ? `${lang}-${languageObj.region}` : lang;
      return number.toLocaleString(locale);
    } catch (error) {
      return number.toLocaleString();
    }
  }

  /**
   * Format currency according to language settings
   */
  formatCurrency(amount: number, currency: string, language?: string): string {
    const lang = language || this.currentLanguage;
    const languageObj = this.languages.get(lang);
    
    try {
      const locale = languageObj?.region ? `${lang}-${languageObj.region}` : lang;
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
      }).format(amount);
    } catch (error) {
      return `${currency} ${amount}`;
    }
  }

  /**
   * Get text direction for language
   */
  getTextDirection(language?: string): 'ltr' | 'rtl' {
    const lang = language || this.currentLanguage;
    const languageObj = this.languages.get(lang);
    return languageObj?.direction || 'ltr';
  }

  /**
   * Initialize default languages
   */
  private initializeDefaultLanguages(): void {
    const defaultLanguages: Language[] = [
      {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        region: 'US',
        direction: 'ltr',
        pluralRules: [
          { rule: 'one', condition: 'n == 1' },
          { rule: 'other', condition: 'true' },
        ],
        dateFormat: 'MM/dd/yyyy',
        timeFormat: 'h:mm a',
        numberFormat: { decimal: '.', thousands: ',', currency: '$' },
        enabled: true,
      },
      {
        code: 'ru',
        name: 'Russian',
        nativeName: 'Русский',
        region: 'RU',
        direction: 'ltr',
        pluralRules: [
          { rule: 'one', condition: 'n % 10 == 1 && n % 100 != 11' },
          { rule: 'few', condition: 'n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20)' },
          { rule: 'many', condition: 'n % 10 == 0 || (n % 10 >= 5 && n % 10 <= 9) || (n % 100 >= 11 && n % 100 <= 14)' },
          { rule: 'other', condition: 'true' },
        ],
        dateFormat: 'dd.MM.yyyy',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: ',', thousands: ' ', currency: '₽' },
        enabled: true,
        fallback: 'en',
      },
      {
        code: 'zh',
        name: 'Chinese',
        nativeName: '中文',
        region: 'CN',
        direction: 'ltr',
        pluralRules: [
          { rule: 'other', condition: 'true' },
        ],
        dateFormat: 'yyyy年MM月dd日',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: '.', thousands: ',', currency: '¥' },
        enabled: true,
        fallback: 'en',
      },
      {
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        region: 'ES',
        direction: 'ltr',
        pluralRules: [
          { rule: 'one', condition: 'n == 1' },
          { rule: 'other', condition: 'true' },
        ],
        dateFormat: 'dd/MM/yyyy',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: ',', thousands: '.', currency: '€' },
        enabled: true,
        fallback: 'en',
      },
      {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        region: 'SA',
        direction: 'rtl',
        pluralRules: [
          { rule: 'zero', condition: 'n == 0' },
          { rule: 'one', condition: 'n == 1' },
          { rule: 'two', condition: 'n == 2' },
          { rule: 'few', condition: 'n % 100 >= 3 && n % 100 <= 10' },
          { rule: 'many', condition: 'n % 100 >= 11 && n % 100 <= 99' },
          { rule: 'other', condition: 'true' },
        ],
        dateFormat: 'dd/MM/yyyy',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: '.', thousands: ',', currency: 'ر.س' },
        enabled: true,
        fallback: 'en',
      },
    ];

    defaultLanguages.forEach(lang => this.addLanguage(lang));
    logger.info(`Initialized ${defaultLanguages.length} default languages`);
  }

  /**
   * Load translations from storage
   */
  private async loadTranslations(): Promise<void> {
    // In a real implementation, this would load from files or database
    logger.info('Loading translations from storage');
    
    // Register some default keys
    this.registerKey('common.ok', 'common', 'OK');
    this.registerKey('common.cancel', 'common', 'Cancel');
    this.registerKey('common.save', 'common', 'Save');
    this.registerKey('common.delete', 'common', 'Delete');
    this.registerKey('common.edit', 'common', 'Edit');
    this.registerKey('common.loading', 'common', 'Loading...');
    this.registerKey('common.error', 'common', 'Error');
    this.registerKey('common.success', 'common', 'Success');

    // Add some translations
    this.addTranslation('common.ok', 'ru', 'ОК');
    this.addTranslation('common.cancel', 'ru', 'Отмена');
    this.addTranslation('common.save', 'ru', 'Сохранить');
    this.addTranslation('common.delete', 'ru', 'Удалить');
    this.addTranslation('common.edit', 'ru', 'Редактировать');
    this.addTranslation('common.loading', 'ru', 'Загрузка...');
    this.addTranslation('common.error', 'ru', 'Ошибка');
    this.addTranslation('common.success', 'ru', 'Успех');

    this.addTranslation('common.ok', 'zh', '确定');
    this.addTranslation('common.cancel', 'zh', '取消');
    this.addTranslation('common.save', 'zh', '保存');
    this.addTranslation('common.delete', 'zh', '删除');
    this.addTranslation('common.edit', 'zh', '编辑');
    this.addTranslation('common.loading', 'zh', '加载中...');
    this.addTranslation('common.error', 'zh', '错误');
    this.addTranslation('common.success', 'zh', '成功');

    this.emit('translations-loaded');
  }

  /**
   * Get translation for key and language
   */
  private getTranslation(
    key: string,
    language: string,
    options: { fallback?: string; count?: number } = {}
  ): string {
    const languageTranslations = this.translations.get(language);
    const translation = languageTranslations?.get(key);

    if (translation) {
      return translation.value;
    }

    // Try fallback language
    const languageObj = this.languages.get(language);
    if (languageObj?.fallback && languageObj.fallback !== language) {
      const fallbackTranslation = this.getTranslation(key, languageObj.fallback, options);
      if (fallbackTranslation) {
        return fallbackTranslation;
      }
    }

    // Try global fallback
    if (language !== this.fallbackLanguage) {
      const fallbackTranslation = this.getTranslation(key, this.fallbackLanguage, options);
      if (fallbackTranslation) {
        return fallbackTranslation;
      }
    }

    // Use provided fallback
    if (options.fallback) {
      return options.fallback;
    }

    // Use default value from key registration
    const translationKey = this.translationKeys.get(key);
    if (translationKey) {
      return translationKey.defaultValue;
    }

    // Last resort: return the key itself
    return key;
  }

  /**
   * Apply cultural adaptations
   */
  private applyCulturalAdaptations(
    text: string,
    language: string,
    context: TranslationContext
  ): string {
    const region = context.user?.region || this.languages.get(language)?.region;
    if (!region) return text;

    const adaptationKey = `${language}_${region}`;
    const adaptation = this.culturalAdaptations.get(adaptationKey);
    
    if (!adaptation) return text;

    // Apply content adaptations
    let adaptedText = text;
    for (const [pattern, replacement] of Object.entries(adaptation.adaptations.content)) {
      adaptedText = adaptedText.replace(new RegExp(pattern, 'g'), replacement);
    }

    return adaptedText;
  }

  /**
   * Interpolate string with variables
   */
  private interpolateString(text: string, variables: Record<string, any>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] !== undefined ? String(variables[key]) : match;
    });
  }

  /**
   * Get plural rule for count and language
   */
  private getPluralRule(count: number, language: Language): string {
    for (const rule of language.pluralRules) {
      if (this.evaluatePluralCondition(rule.condition, count)) {
        return rule.rule;
      }
    }
    return 'other';
  }

  /**
   * Evaluate plural condition
   */
  private evaluatePluralCondition(condition: string, n: number): boolean {
    try {
      // Simple condition evaluation (in production, use a proper parser)
      const evalCondition = condition
        .replace(/n/g, n.toString())
        .replace(/==/g, '===')
        .replace(/!=/g, '!==');
      
      return eval(evalCondition);
    } catch (error) {
      logger.error('Error evaluating plural condition', { condition, n, error });
      return false;
    }
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(
    key: string,
    language: string,
    options: Record<string, any>
  ): string {
    const optionsStr = JSON.stringify(options);
    return `${key}_${language}_${Buffer.from(optionsStr).toString('base64')}`;
  }

  /**
   * Perform AI translation (simulated)
   */
  private async performAITranslation(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
    quality: 'fast' | 'balanced' | 'high',
    context?: string
  ): Promise<string> {
    // Simulate AI translation delay
    await new Promise(resolve => setTimeout(resolve, quality === 'fast' ? 100 : quality === 'balanced' ? 300 : 500));
    
    // In a real implementation, this would call an AI translation service
    logger.debug(`AI translating "${text}" from ${sourceLanguage} to ${targetLanguage}`);
    
    // Return simulated translation
    return `[AI-${targetLanguage.toUpperCase()}] ${text}`;
  }

  /**
   * Calculate translation quality score
   */
  private calculateTranslationQuality(text: string, quality: 'fast' | 'balanced' | 'high'): number {
    const baseQuality = quality === 'fast' ? 0.7 : quality === 'balanced' ? 0.8 : 0.9;
    const lengthFactor = Math.min(text.length / 100, 1); // Longer texts might have lower quality
    return Math.max(0.5, baseQuality - (lengthFactor * 0.1));
  }

  /**
   * Get engine statistics
   */
  getStats() {
    return {
      languages: this.languages.size,
      translationKeys: this.translationKeys.size,
      translations: Array.from(this.translations.values()).reduce((sum, map) => sum + map.size, 0),
      culturalAdaptations: this.culturalAdaptations.size,
      currentLanguage: this.currentLanguage,
      fallbackLanguage: this.fallbackLanguage,
      cacheSize: this.translationCache.size,
      aiTranslationEnabled: this.aiTranslationEnabled,
      cacheEnabled: this.cacheEnabled,
    };
  }

  /**
   * Clear translation cache
   */
  clearCache(): void {
    this.translationCache.clear();
    logger.info('Translation cache cleared');
    this.emit('cache-cleared');
  }

  /**
   * Enable/disable AI translation
   */
  setAITranslation(enabled: boolean): void {
    this.aiTranslationEnabled = enabled;
    logger.info(`AI translation ${enabled ? 'enabled' : 'disabled'}`);
    this.emit('ai-translation-changed', enabled);
  }

  /**
   * Enable/disable caching
   */
  setCaching(enabled: boolean): void {
    this.cacheEnabled = enabled;
    if (!enabled) {
      this.clearCache();
    }
    logger.info(`Translation caching ${enabled ? 'enabled' : 'disabled'}`);
    this.emit('caching-changed', enabled);
  }

  /**
   * Export translations for a language
   */
  exportTranslations(language: string): Record<string, any> {
    const languageTranslations = this.translations.get(language);
    if (!languageTranslations) {
      return {};
    }

    const exported: Record<string, any> = {};
    for (const [key, translation] of languageTranslations) {
      const parts = key.split('.');
      let current = exported;
      
      for (let i = 0; i < parts.length - 1; i++) {
        if (!current[parts[i]]) {
          current[parts[i]] = {};
        }
        current = current[parts[i]];
      }
      
      current[parts[parts.length - 1]] = translation.value;
    }

    return exported;
  }

  /**
   * Import translations for a language
   */
  importTranslations(language: string, translations: Record<string, any>): number {
    let importedCount = 0;

    const flattenTranslations = (obj: Record<string, any>, prefix = ''): void => {
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof value === 'string') {
          this.addTranslation(fullKey, language, value, { source: 'manual' });
          importedCount++;
        } else if (typeof value === 'object' && value !== null) {
          flattenTranslations(value, fullKey);
        }
      }
    };

    flattenTranslations(translations);
    
    logger.info(`Imported ${importedCount} translations for ${language}`);
    this.emit('translations-imported', { language, count: importedCount });
    
    return importedCount;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.languages.clear();
    this.translations.clear();
    this.translationKeys.clear();
    this.culturalAdaptations.clear();
    this.translationCache.clear();
    this.removeAllListeners();
  }
}

// Global internationalization engine instance
export const i18nEngine = new InternationalizationEngine();

// Convenience functions
export const t = (key: string, options?: any) => i18nEngine.translate(key, options);
export const tPlural = (key: string, count: number, options?: any) => 
  i18nEngine.translatePlural(key, count, options);

export default i18nEngine;
