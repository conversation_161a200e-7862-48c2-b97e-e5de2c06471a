const { strict } = require('assert');
const { performance } = require('perf_hooks');

const { describe, it, before } = require('mocha');

const { SessionManager } = require('../src/main/session/sessionManager');

describe('Performance Tests', () => {
  let manager;
  const largeSession = {
    version: '1.0',
    tabs: Array.from({ length: 1000 }, (_, i) => ({
      url: `https://site${i}.com`,
      title: `Tab ${i}`,
      favicon: `https://site${i}/favicon.ico`,
    })),
  };

  before(() => {
    manager = new SessionManager({
      store: { get: () => null, set: () => {} },
      encryptionKey: 'a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6',
    });
  });

  it('Должен обрабатывать 1000 вкладок за приемлемое время', async () => {
    const start = performance.now();
    const encrypted = await manager.saveSession(largeSession);
    const decryptStart = performance.now();
    await manager.restoreSession(encrypted);
    const duration = performance.now() - start;

    strict.ok(duration < 5000, `Общее время обработки ${duration.toFixed(1)}ms превышает 5 секунд`);
    console.log(
      `\nПерформанс 1000 вкладок:\n` +
        `Шифрование: ${(decryptStart - start).toFixed(1)}ms\n` +
        `Дешифровка: ${(performance.now() - decryptStart).toFixed(1)}ms`
    );
  });

  it('Должен эффективно использовать память', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    const encrypted = await manager.saveSession(largeSession);
    const afterEncrypt = process.memoryUsage().heapUsed;

    await manager.restoreSession(encrypted);
    const finalMemory = process.memoryUsage().heapUsed;

    strict.ok(
      afterEncrypt - initialMemory < 50 * 1024 * 1024,
      `Потребление памяти при шифровании превышает 50MB`
    );
    strict.ok(
      finalMemory - initialMemory < 10 * 1024 * 1024,
      `Память не освобождается после операций`
    );
  });
});
