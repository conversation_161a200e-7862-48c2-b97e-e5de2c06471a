import { CacheManager } from '../../cache/CacheManager';
import { ErrorManager } from '../../error/ErrorManager';
import { PerformanceMonitor } from '../../performance/PerformanceMonitor';
import { NetworkManager } from '../NetworkManager';

jest.mock('../../cache/CacheManager');
jest.mock('../../error/ErrorManager');
jest.mock('../../performance/PerformanceMonitor');

describe('NetworkManager', () => {
  let networkManager: NetworkManager;
  let mockCacheManager: jest.Mocked<CacheManager>;
  let mockErrorManager: jest.Mocked<ErrorManager>;
  let mockPerformanceMonitor: jest.Mocked<PerformanceMonitor>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Get singleton instance
    networkManager = NetworkManager.getInstance();

    // Get mocked instances
    mockCacheManager = CacheManager.getInstance() as jest.Mocked<CacheManager>;
    mockErrorManager = ErrorManager.getInstance() as jest.Mocked<ErrorManager>;
    mockPerformanceMonitor = PerformanceMonitor.getInstance() as jest.Mocked<PerformanceMonitor>;

    // Reset fetch mock
    global.fetch = jest.fn();
  });

  describe('Initialization', () => {
    test('should create singleton instance', () => {
      const instance1 = NetworkManager.getInstance();
      const instance2 = NetworkManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('should initialize with default config', () => {
      const config = networkManager.getConfig();
      expect(config).toEqual({
        baseUrl: '',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        cacheEnabled: true,
        offlineMode: false,
        requestQueue: true,
        compression: true,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });
    });
  });

  describe('Configuration', () => {
    test('should update config', () => {
      const newConfig = {
        baseUrl: 'https://api.example.com',
        timeout: 5000,
      };

      networkManager.setConfig(newConfig);
      const config = networkManager.getConfig();

      expect(config.baseUrl).toBe(newConfig.baseUrl);
      expect(config.timeout).toBe(newConfig.timeout);
    });

    test('should emit config update event', () => {
      const listener = jest.fn();
      networkManager.on('configUpdated', listener);

      networkManager.setConfig({ timeout: 5000 });
      expect(listener).toHaveBeenCalled();
    });
  });

  describe('Network Requests', () => {
    const mockRequest = {
      id: 'test-request',
      method: 'GET',
      url: '/api/test',
      headers: {},
    };

    const mockResponse = {
      id: 'test-request',
      status: 200,
      headers: {},
      data: { test: 'data' },
      timestamp: Date.now(),
      duration: 100,
    };

    test('should make successful request', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        status: 200,
        headers: new Headers(),
        json: () => Promise.resolve({ test: 'data' }),
      });

      const response = await networkManager.request(mockRequest);
      expect(response.status).toBe(200);
      expect(response.data).toEqual({ test: 'data' });
    });

    test('should handle request errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(networkManager.request(mockRequest)).rejects.toThrow('Network error');
      expect(mockErrorManager.handleError).toHaveBeenCalled();
    });

    test('should retry failed requests', async () => {
      (global.fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          status: 200,
          headers: new Headers(),
          json: () => Promise.resolve({ test: 'data' }),
        });

      const response = await networkManager.request({
        ...mockRequest,
        retry: 1,
      });

      expect(response.status).toBe(200);
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    test('should not retry on certain status codes', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        status: 404,
        headers: new Headers(),
        json: () => Promise.resolve({ error: 'Not found' }),
      });

      const response = await networkManager.request({
        ...mockRequest,
        retry: 1,
      });

      expect(response.status).toBe(404);
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });
  });

  describe('Caching', () => {
    const mockRequest = {
      id: 'test-request',
      method: 'GET',
      url: '/api/test',
      headers: {},
      cache: true,
    };

    const mockCachedResponse = {
      id: 'test-request',
      status: 200,
      headers: {},
      data: { test: 'cached' },
      timestamp: Date.now(),
      duration: 0,
    };

    test('should return cached response when available', async () => {
      mockCacheManager.get.mockResolvedValueOnce(mockCachedResponse);

      const response = await networkManager.request(mockRequest);
      expect(response).toEqual(mockCachedResponse);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    test('should cache successful responses', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        status: 200,
        headers: new Headers(),
        json: () => Promise.resolve({ test: 'data' }),
      });

      await networkManager.request(mockRequest);
      expect(mockCacheManager.set).toHaveBeenCalled();
    });

    test('should not cache when disabled', async () => {
      networkManager.setConfig({ cacheEnabled: false });

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        status: 200,
        headers: new Headers(),
        json: () => Promise.resolve({ test: 'data' }),
      });

      await networkManager.request(mockRequest);
      expect(mockCacheManager.set).not.toHaveBeenCalled();
    });
  });

  describe('Offline Mode', () => {
    const mockRequest = {
      id: 'test-request',
      method: 'GET',
      url: '/api/test',
      headers: {},
    };

    test('should queue requests in offline mode', async () => {
      networkManager.setConfig({ offlineMode: true });

      await expect(networkManager.request(mockRequest)).rejects.toThrow(
        'Request queued due to offline mode'
      );
      expect(networkManager.getQueueLength()).toBe(1);
    });

    test('should process queued requests when coming online', async () => {
      networkManager.setConfig({ offlineMode: true });
      await networkManager.request(mockRequest);

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        status: 200,
        headers: new Headers(),
        json: () => Promise.resolve({ test: 'data' }),
      });

      networkManager.setConfig({ offlineMode: false });
      expect(networkManager.getQueueLength()).toBe(0);
    });
  });

  describe('Event Handling', () => {
    test('should emit request events', async () => {
      const requestListener = jest.fn();
      const responseListener = jest.fn();
      const errorListener = jest.fn();

      networkManager.on('requestQueued', requestListener);
      networkManager.on('requestError', errorListener);

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        status: 200,
        headers: new Headers(),
        json: () => Promise.resolve({ test: 'data' }),
      });

      await networkManager.request({
        id: 'test-request',
        method: 'GET',
        url: '/api/test',
        headers: {},
      });

      expect(requestListener).not.toHaveBeenCalled();
      expect(errorListener).not.toHaveBeenCalled();
    });

    test('should handle event unsubscription', () => {
      const listener = jest.fn();
      networkManager.on('configUpdated', listener);
      networkManager.off('configUpdated', listener);

      networkManager.setConfig({ timeout: 5000 });
      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('Cleanup', () => {
    test('should clear request queue', () => {
      networkManager.setConfig({ offlineMode: true });
      networkManager.request({
        id: 'test-request',
        method: 'GET',
        url: '/api/test',
        headers: {},
      });

      networkManager.clearQueue();
      expect(networkManager.getQueueLength()).toBe(0);
    });

    test('should cleanup resources', () => {
      const listener = jest.fn();
      networkManager.on('configUpdated', listener);

      networkManager.cleanup();
      networkManager.setConfig({ timeout: 5000 });
      expect(listener).not.toHaveBeenCalled();
    });
  });
});
