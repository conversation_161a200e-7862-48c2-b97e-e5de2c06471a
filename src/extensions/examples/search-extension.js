/**
 * Расширение для A11 Browser: Улучшенная поддержка поиска
 *
 * Это расширение добавляет расширенные возможности поиска в адресной строке:
 * - Поддержка нескольких поисковых систем
 * - Быстрые команды для поиска
 * - Автодополнение поисковых запросов
 * - История поисковых запросов
 */

// Регистрация расширения
window.browserAPI
  .registerExtension({
    id: 'enhanced-search',
    name: 'Улучшенная поддержка поиска',
    version: '1.0.0',
    capabilities: ['storage'],
  })
  .then(result => {
    if (result.success) {
      initSearchExtension();
    } else {
      console.error('Не удалось зарегистрировать расширение:', result.error);
    }
  });

// Глобальные переменные
let searchEngines = {
  default: 'google',
  engines: {
    google: {
      name: 'Google',
      url: 'https://www.google.com/search?q={query}',
      shortcut: 'g',
    },
    yandex: {
      name: 'Яндекс',
      url: 'https://yandex.ru/search/?text={query}',
      shortcut: 'y',
    },
    bing: {
      name: 'Bing',
      url: 'https://www.bing.com/search?q={query}',
      shortcut: 'b',
    },
    duckduckgo: {
      name: 'DuckDuckGo',
      url: 'https://duckduckgo.com/?q={query}',
      shortcut: 'd',
    },
    wikipedia: {
      name: 'Википедия',
      url: 'https://ru.wikipedia.org/wiki/Special:Search?search={query}',
      shortcut: 'w',
    },
  },
};

let searchHistory = [];
const maxHistoryItems = 100;

// Инициализация расширения
function initSearchExtension() {
  // Загружаем настройки поисковых систем
  loadSearchSettings();

  // Загружаем историю поисковых запросов
  loadSearchHistory();

  // Добавляем обработчик для адресной строки
  setupAddressBarHandler();

  // Добавляем интерфейс для настроек поиска
  addSearchSettingsUI();

  // Добавляем стили для автодополнения
  addSearchStyles();

  console.log('Расширение "Улучшенная поддержка поиска" активировано');
}

// Загрузка настроек поисковых систем
async function loadSearchSettings() {
  try {
    const settings = await window.browserAPI.getSettings();
    if (settings && settings.searchEngines) {
      searchEngines = settings.searchEngines;
    }
  } catch (error) {
    console.error('Ошибка при загрузке настроек поиска:', error);
  }
}

// Загрузка истории поисковых запросов
async function loadSearchHistory() {
  try {
    const settings = await window.browserAPI.getSettings();
    if (settings && settings.searchHistory) {
      searchHistory = settings.searchHistory;
    }
  } catch (error) {
    console.error('Ошибка при загрузке истории поиска:', error);
  }
}

// Сохранение настроек поисковых систем
async function saveSearchSettings() {
  try {
    const settings = (await window.browserAPI.getSettings()) || {};
    settings.searchEngines = searchEngines;
    await window.browserAPI.saveSettings(settings);
  } catch (error) {
    console.error('Ошибка при сохранении настроек поиска:', error);
  }
}

// Сохранение истории поисковых запросов
async function saveSearchHistory() {
  try {
    const settings = (await window.browserAPI.getSettings()) || {};
    settings.searchHistory = searchHistory;
    await window.browserAPI.saveSettings(settings);
  } catch (error) {
    console.error('Ошибка при сохранении истории поиска:', error);
  }
}

// Добавление запроса в историю поиска
function addToSearchHistory(query) {
  // Удаляем дубликаты
  searchHistory = searchHistory.filter(item => item.query !== query);

  // Добавляем новый запрос в начало списка
  searchHistory.unshift({
    query: query,
    timestamp: Date.now(),
  });

  // Ограничиваем размер истории
  if (searchHistory.length > maxHistoryItems) {
    searchHistory = searchHistory.slice(0, maxHistoryItems);
  }

  // Сохраняем историю
  saveSearchHistory();
}

// Настройка обработчика для адресной строки
function setupAddressBarHandler() {
  const urlInput = document.getElementById('url-input');
  const goButton = document.getElementById('go-button');

  if (!urlInput || !goButton) return;

  // Создаем и добавляем выпадающий список для автодополнения
  const autocompleteList = document.createElement('div');
  autocompleteList.className = 'autocomplete-list';
  autocompleteList.style.display = 'none';
  urlInput.parentNode.appendChild(autocompleteList);

  // Обработчик ввода в адресную строку
  urlInput.addEventListener('input', () => {
    const query = urlInput.value.trim();

    // Показываем автодополнение только если есть текст
    if (query.length > 0) {
      showAutocomplete(query, autocompleteList);
    } else {
      autocompleteList.style.display = 'none';
    }
  });

  // Обработчик клика по кнопке перехода
  goButton.addEventListener('click', () => {
    handleSearch(urlInput.value);
  });

  // Обработчик нажатия Enter в адресной строке
  urlInput.addEventListener('keydown', e => {
    if (e.key === 'Enter') {
      handleSearch(urlInput.value);
    }
  });

  // Скрываем автодополнение при клике вне его
  document.addEventListener('click', e => {
    if (e.target !== urlInput && e.target !== autocompleteList) {
      autocompleteList.style.display = 'none';
    }
  });
}

// Показ автодополнения
function showAutocomplete(query, autocompleteList) {
  // Очищаем список
  autocompleteList.innerHTML = '';

  // Проверяем, является ли запрос командой поисковой системы
  const engineCommand = checkForSearchEngineCommand(query);

  // Получаем подходящие элементы из истории поиска
  const historyMatches = searchHistory
    .filter(item => item.query.toLowerCase().includes(query.toLowerCase()))
    .slice(0, 5);

  // Если есть команда поисковой системы, добавляем подсказку
  if (engineCommand) {
    const engineItem = document.createElement('div');
    engineItem.className = 'autocomplete-item';
    engineItem.innerHTML = `<strong>${searchEngines.engines[engineCommand.engine].name}</strong>: ${engineCommand.query}`;
    engineItem.addEventListener('click', () => {
      const urlInput = document.getElementById('url-input');
      urlInput.value = query;
      handleSearch(query);
      autocompleteList.style.display = 'none';
    });
    autocompleteList.appendChild(engineItem);
  }

  // Добавляем элементы из истории
  historyMatches.forEach(item => {
    const historyItem = document.createElement('div');
    historyItem.className = 'autocomplete-item';
    historyItem.textContent = item.query;
    historyItem.addEventListener('click', () => {
      const urlInput = document.getElementById('url-input');
      urlInput.value = item.query;
      handleSearch(item.query);
      autocompleteList.style.display = 'none';
    });
    autocompleteList.appendChild(historyItem);
  });

  // Показываем список, если есть элементы
  if (autocompleteList.children.length > 0) {
    autocompleteList.style.display = 'block';
  } else {
    autocompleteList.style.display = 'none';
  }
}

// Проверка на команду поисковой системы
function checkForSearchEngineCommand(query) {
  // Формат команды: [shortcut]:query
  // Например: g:погода москва
  const match = query.match(/^([a-z]):(.*)/i);

  if (match) {
    const shortcut = match[1].toLowerCase();
    const searchQuery = match[2].trim();

    // Находим поисковую систему по сокращению
    for (const [engine, config] of Object.entries(searchEngines.engines)) {
      if (config.shortcut === shortcut) {
        return {
          engine: engine,
          query: searchQuery,
        };
      }
    }
  }

  return null;
}

// Обработка поискового запроса
function handleSearch(input) {
  const query = input.trim();

  // Если пустой запрос, ничего не делаем
  if (!query) return;

  // Проверяем, является ли ввод URL
  if (isValidUrl(query)) {
    // Если это URL, переходим по нему напрямую
    navigateToUrl(query);
    return;
  }

  // Проверяем, является ли запрос командой поисковой системы
  const engineCommand = checkForSearchEngineCommand(query);

  if (engineCommand) {
    // Если это команда поисковой системы, используем указанную систему
    const searchUrl = searchEngines.engines[engineCommand.engine].url.replace(
      '{query}',
      encodeURIComponent(engineCommand.query)
    );
    navigateToUrl(searchUrl);
    addToSearchHistory(query);
  } else {
    // Иначе используем поисковую систему по умолчанию
    const defaultEngine = searchEngines.engines[searchEngines.default];
    const searchUrl = defaultEngine.url.replace('{query}', encodeURIComponent(query));
    navigateToUrl(searchUrl);
    addToSearchHistory(query);
  }
}

// Проверка, является ли строка URL
function isValidUrl(string) {
  try {
    // Проверяем, содержит ли строка протокол
    if (string.startsWith('http://') || string.startsWith('https://')) {
      new URL(string);
      return true;
    }

    // Проверяем, является ли строка доменным именем
    if (string.includes('.') && !string.includes(' ')) {
      new URL(`http://${string}`);
      return true;
    }

    return false;
  } catch (e) {
    return false;
  }
}

// Переход по URL
function navigateToUrl(url) {
  // Получаем активную вкладку
  const activeWebview = document.querySelector('webview.active');

  if (activeWebview) {
    // Если есть активная вкладка, загружаем URL в ней
    activeWebview.src = url;

    // Обновляем адресную строку
    const urlInput = document.getElementById('url-input');
    if (urlInput) {
      urlInput.value = url;
    }
  } else {
    // Если нет активной вкладки, создаем новую
    window.browserAPI.createTab(url);
  }
}

// Добавление интерфейса для настроек поиска
function addSearchSettingsUI() {
  // Проверяем, существует ли панель настроек
  const settingsPanel = document.getElementById('settings-panel');
  if (!settingsPanel) return;

  // Создаем секцию настроек поиска
  const searchSettingsSection = document.createElement('div');
  searchSettingsSection.className = 'settings-section';
  searchSettingsSection.innerHTML = `
    <h3>Настройки поиска</h3>
    <div class="settings-option">
      <label for="default-search-engine">Поисковая система по умолчанию:</label>
      <select id="default-search-engine"></select>
    </div>
    <div class="settings-option">
      <button id="clear-search-history">Очистить историю поиска</button>
    </div>
  `;

  // Добавляем секцию в панель настроек
  settingsPanel.appendChild(searchSettingsSection);

  // Заполняем выпадающий список поисковыми системами
  const selectElement = document.getElementById('default-search-engine');
  if (selectElement) {
    for (const [engine, config] of Object.entries(searchEngines.engines)) {
      const option = document.createElement('option');
      option.value = engine;
      option.textContent = `${config.name} (${config.shortcut}:)`;
      option.selected = engine === searchEngines.default;
      selectElement.appendChild(option);
    }

    // Обработчик изменения поисковой системы по умолчанию
    selectElement.addEventListener('change', () => {
      searchEngines.default = selectElement.value;
      saveSearchSettings();
    });
  }

  // Обработчик для кнопки очистки истории поиска
  const clearHistoryButton = document.getElementById('clear-search-history');
  if (clearHistoryButton) {
    clearHistoryButton.addEventListener('click', () => {
      searchHistory = [];
      saveSearchHistory();
      if (window.sendBrowserNotification) {
        window.sendBrowserNotification({
          title: 'История поиска очищена',
          message: 'История поисковых запросов была успешно очищена',
          type: 'info',
        });
      }
    });
  }
}

// Добавляем стили для автодополнения
function addSearchStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .autocomplete-list {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: white;
      border: 1px solid #ccc;
      border-top: none;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .autocomplete-item {
      padding: 8px 12px;
      cursor: pointer;
    }
    
    .autocomplete-item:hover {
      background-color: #f0f0f0;
    }
    
    .dark-mode .autocomplete-list {
      background-color: #333;
      border-color: #555;
    }
    
    .dark-mode .autocomplete-item {
      color: #eee;
    }
    
    .dark-mode .autocomplete-item:hover {
      background-color: #444;
    }
  `;

  document.head.appendChild(style);
}

// Стили добавляются при инициализации расширения
