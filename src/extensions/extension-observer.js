/**
 * Observer system for A11 Browser extensions
 * Implements the observer pattern to notify extensions about browser events
 */

class ExtensionObserver {
  constructor() {
    // Event types that extensions can subscribe to
    this.eventTypes = {
      TAB_CREATED: 'tabCreated',
      TAB_CLOSED: 'tabClosed',
      TAB_UPDATED: 'tabUpdated',
      TAB_ACTIVATED: 'tabActivated',
      NAVIGATION_STARTED: 'navigationStarted',
      NAVIGATION_COMPLETED: 'navigationCompleted',
      DOWNLOAD_STARTED: 'downloadStarted',
      DOWNLOAD_COMPLETED: 'downloadCompleted',
      BOOKMARK_ADDED: 'bookmarkAdded',
      BOOKMARK_REMOVED: 'bookmarkRemoved',
      SECURITY_ALERT: 'securityAlert',
      SETTINGS_CHANGED: 'settingsChanged',
      EXTENSION_INSTALLED: 'extensionInstalled',
      EXTENSION_UNINSTALLED: 'extensionUninstalled',
      EXTENSION_ENABLED: 'extensionEnabled',
      EXTENSION_DISABLED: 'extensionDisabled',
      TAB_GROUP_CREATED: 'tabGroupCreated',
      TAB_GROUP_REMOVED: 'tabGroupRemoved',
      TAB_ADDED_TO_GROUP: 'tabAddedToGroup',
      TAB_REMOVED_FROM_GROUP: 'tabRemovedFromGroup',
    };

    // Map of event subscribers
    this.subscribers = new Map();

    // Initialize map for each event type
    Object.values(this.eventTypes).forEach(eventType => {
      this.subscribers.set(eventType, new Map());
    });

    // Event log for debugging
    this.eventLog = [];
    this.maxLogSize = 100;

    // Flag to enable/disable logging
    this.loggingEnabled = true;
  }

  /**
   * Subscribes an extension to an event
   * @param {string} extensionId - Extension ID
   * @param {string} eventType - Event type
   * @param {Function} callback - Callback function
   * @returns {boolean} - Subscription success
   */
  subscribe(extensionId, eventType, callback) {
    // Check if event type exists
    if (!Object.values(this.eventTypes).includes(eventType)) {
      global.logger.error(`Unknown event type: ${eventType}`);
      return false;
    }

    // Check if callback is a function
    if (typeof callback !== 'function') {
      global.logger.error('Callback must be a function');
      return false;
    }

    // Get the subscriber map for this event type
    const eventSubscribers = this.subscribers.get(eventType);

    // Add subscriber
    eventSubscribers.set(extensionId, callback);

    // Log subscription
    this._logEvent('subscribe', { extensionId, eventType });

    return true;
  }

  /**
   * Unsubscribes an extension from an event
   * @param {string} extensionId - Extension ID
   * @param {string} eventType - Event type
   * @returns {boolean} - Unsubscription success
   */
  unsubscribe(extensionId, eventType) {
    // Check if event type exists
    if (!Object.values(this.eventTypes).includes(eventType)) {
      global.logger.error(`Unknown event type: ${eventType}`);
      return false;
    }

    // Get the subscriber map for this event type
    const eventSubscribers = this.subscribers.get(eventType);

    // Remove subscriber
    const result = eventSubscribers.delete(extensionId);

    // Log unsubscription
    this._logEvent('unsubscribe', { extensionId, eventType, success: result });

    return result;
  }

  /**
   * Unsubscribes an extension from all events
   * @param {string} extensionId - Extension ID
   */
  unsubscribeAll(extensionId) {
    Object.values(this.eventTypes).forEach(eventType => {
      const eventSubscribers = this.subscribers.get(eventType);
      eventSubscribers.delete(extensionId);
    });

    // Log the unsubscription from all events
    this._logEvent('unsubscribeAll', { extensionId });
  }

  /**
   * Notifies subscribers about an event
   * @param {string} eventType - Event type
   * @param {Object} eventData - Event data
   */
  notify(eventType, eventData = {}) {
    // Check if event type exists
    if (!Object.values(this.eventTypes).includes(eventType)) {
      global.logger.error(`Unknown event type: ${eventType}`);
      return;
    }

    // Get the subscriber map for this event type
    const eventSubscribers = this.subscribers.get(eventType);

    // Add timestamp to event data
    const eventWithTimestamp = {
      ...eventData,
      timestamp: Date.now(),
    };

    // Notify all subscribers
    eventSubscribers.forEach((callback, extensionId) => {
      try {
        // Вызываем callback в безопасном контексте
        callback(eventWithTimestamp);
      } catch (error) {
        global.logger.error(
          `Error notifying extension ${extensionId} about event ${eventType}:`,
          error
        );
      }
    });

    // Log event
    this._logEvent('notify', {
      eventType,
      subscribersCount: eventSubscribers.size,
      eventData: eventWithTimestamp,
    });
  }

  /**
   * Gets the list of subscribers for an event
   * @param {string} eventType - Event type
   * @returns {Array} - Array of extension IDs subscribed to the event
   */
  getSubscribers(eventType) {
    // Check if event type exists
    if (!Object.values(this.eventTypes).includes(eventType)) {
      global.logger.error(`Unknown event type: ${eventType}`);
      return [];
    }

    // Get the subscriber map for this event type
    const eventSubscribers = this.subscribers.get(eventType);

    // Return array of extension IDs
    return Array.from(eventSubscribers.keys());
  }

  /**
   * Checks if an extension is subscribed to an event
   * @param {string} extensionId - Extension ID
   * @param {string} eventType - Event type
   * @returns {boolean} - Check result
   */
  isSubscribed(extensionId, eventType) {
    // Check if event type exists
    if (!Object.values(this.eventTypes).includes(eventType)) {
      global.logger.error(`Unknown event type: ${eventType}`);
      return false;
    }

    // Get the subscriber map for this event type
    const eventSubscribers = this.subscribers.get(eventType);

    // Check for subscriber
    return eventSubscribers.has(extensionId);
  }

  /**
   * Logs an event.
   * @param {string} action - The action (subscribe, unsubscribe, notify).
   * @param {Object} data - The event data.
   * @private
   */
  _logEvent(action, data) {
    if (!this.loggingEnabled) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      action: action,
      data: data,
    };

    this.eventLog.push(logEntry);

    // Limit log size
    if (this.eventLog.length > this.maxLogSize) {
      this.eventLog.shift(); // Remove the oldest entry
    }

    // console.log(`[ExtensionObserver] ${action}:`, data);
  }

  /**
   * Enables logging.
   */
  enableLogging() {
    this.loggingEnabled = true;
    console.log('ExtensionObserver logging enabled.');
  }

  /**
   * Disables logging.
   */
  disableLogging() {
    this.loggingEnabled = false;
    console.log('ExtensionObserver logging disabled.');
  }

  /**
   * Gets the event log.
   * @returns {Array} - An array of event log entries.
   */
  getEventLog() {
    return this.eventLog;
  }
}

module.exports = ExtensionObserver;
