/**
 * A11 Browser Extension Sandbox System
 * Provides isolation and secure execution of extension code
 */

const crypto = require('crypto');
const path = require('path');

const { ipcMain } = require('electron');
const { BrowserWindow } = require('electron');

class ExtensionSandbox {
  constructor() {
    this.sandboxedExtensions = new Map();
    this.permissionRegistry = new Map();
    this._registerIpcHandlers();
  }

  /**
   * Creates an isolated environment for an extension.
   * @param {string} extensionId - The ID of the extension.
   * @param {string} extensionPath - The path to the extension's files.
   * @param {Object} manifest - The extension's manifest with requested permissions.
   * @returns {Promise<Object>} - The result of the sandbox creation.
   */
  async createSandbox(extensionId, extensionPath, manifest) {
    try {
      // Generate a unique token for communication with the sandbox.
      const sandboxToken = crypto.randomBytes(32).toString('hex');
      const { v4: uuidv4 } = require('uuid');
      const ThreatMonitor = require('../security/ThreatMonitor');
      const sandboxId = uuidv4();
      const threatMonitoring = new ThreatMonitor(sandboxId);

      // Create a hidden window for isolated execution of extension code.
      const sandboxWindow = new BrowserWindow({
        width: 0,
        height: 0,
        show: false, // The window is invisible to the user.
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          sandbox: true,
          contextIsolation: true,
          nodeIntegration: false,
          enableRemoteModule: false,
          webSecurity: true, // Enable Chromium sandbox mode.
          preload: path.join(__dirname, 'extension-sandbox-preload.js'),
          webSecurity: true,
          allowRunningInsecureContent: false,
          enableRemoteModule: false,
        },
      });

      // Load an empty page to initialize the sandbox.
      await sandboxWindow.loadFile(path.join(__dirname, 'sandbox-container.html'));

      // Register extension permissions.
      this._registerExtensionPermissions(extensionId, manifest.permissions || []);

      // Save sandbox information.
      this.sandboxedExtensions.set(extensionId, {
        window: sandboxWindow,
        token: sandboxToken,
        manifest: manifest,
      });

      // Initialize the extension in the sandbox.
      await sandboxWindow.webContents.executeJavaScript(`
        window.extensionInit({
          id: "${extensionId}",
          token: "${sandboxToken}",
          path: "${extensionPath.replace(/\\/g, '\\\\')}"
        });
      `);

      return { success: true, sandboxId: extensionId };
    } catch (error) {
      global.logger.error(`Error creating sandbox for extension ${extensionId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Executes extension code within the sandbox.
   * @param {string} extensionId - The ID of the extension.
   * @param {string} code - The code to execute.
   * @returns {Promise<any>} - The result of the code execution.
   */
  async executeInSandbox(extensionId, code) {
    const sandbox = this.sandboxedExtensions.get(extensionId);
    if (!sandbox) {
      throw new Error(`Sandbox for extension ${extensionId} not found`);
    }

    try {
      // Execute the code in the sandbox's context.
      return await sandbox.window.webContents.executeJavaScript(code);
    } catch (error) {
      global.logger.error(`Error executing code in sandbox for extension ${extensionId}:`, error);
      throw error;
    }
  }

  /**
   * Checks if the extension has the requested permission.
   * @param {string} extensionId - The ID of the extension.
   * @param {string} permission - The requested permission.
   * @returns {boolean} - The result of the check.
   */
  hasPermission(extensionId, permission) {
    const permissions = this.permissionRegistry.get(extensionId) || [];
    return permissions.includes(permission);
  }

  /**
   * Requests additional permission from the user for the extension.
   * @param {string} extensionId - The ID of the extension.
   * @param {string} permission - The requested permission.
   * @param {BrowserWindow} parentWindow - The parent window for the dialog.
   * @returns {Promise<boolean>} - The result of the permission request.
   */
  async requestPermission(extensionId, permission, parentWindow) {
    const sandbox = this.sandboxedExtensions.get(extensionId);
    if (!sandbox) {
      return false;
    }

    // Send the permission request to the main window.
    const result = await parentWindow.webContents.executeJavaScript(`
      new Promise((resolve) => {
        const extensionNameString = ${JSON.stringify(sandbox.manifest.name || extensionId)};
        const permissionString = ${JSON.stringify(permission)};
        
        const confirmMessage = 'Extension "' + extensionNameString + '" requests permission: ' + permissionString + '. Allow?';
        const confirmed = confirm(confirmMessage);
        resolve(confirmed);
      });
    `);

    if (result) {
      // Add the permission if the user agreed.
      const permissions = this.permissionRegistry.get(extensionId) || [];
      if (!permissions.includes(permission)) {
        permissions.push(permission);
        this.permissionRegistry.set(extensionId, permissions);
      }
    }

    return result;
  }

  /**
   * Destroys the extension sandbox.
   * @param {string} extensionId - The ID of the extension.
   */
  destroySandbox(extensionId) {
    const sandbox = this.sandboxedExtensions.get(extensionId);
    if (sandbox && sandbox.window && !sandbox.window.isDestroyed()) {
      sandbox.window.destroy();
    }
    this.sandboxedExtensions.delete(extensionId);
    this.permissionRegistry.delete(extensionId);
  }

  /**
   * Registers extension permissions.
   * @param {string} extensionId - The ID of the extension.
   * @param {Array<string>} permissions - The list of permissions.
   * @private
   */
  _registerExtensionPermissions(extensionId, permissions) {
    // Validate and filter permissions.
    const validPermissions = this._validatePermissions(permissions);
    this.permissionRegistry.set(extensionId, validPermissions);
  }

  /**
   * Validates a list of permissions.
   * @param {Array<string>} permissions - The list of permissions to check.
   * @returns {Array<string>} - The list of valid permissions.
   * @private
   */
  _validatePermissions(permissions) {
    const validPermissionTypes = [
      'tabs',
      'storage',
      'cookies',
      'history',
      'bookmarks',
      'downloads',
      'notifications',
      'webRequest',
      'contextMenus',
      'clipboardRead',
      'clipboardWrite',
      'geolocation',
      'activeTab',
      'alarms',
      'idle',
      'management',
      'privacy',
      'proxy',
      'system.cpu',
      'system.memory',
      'system.storage',
      'topSites',
    ];

    return permissions.filter(permission => {
      // Check basic permissions.
      if (validPermissionTypes.includes(permission)) {
        return true;
      }

      // Check permissions for URL access.
      if (permission.startsWith('https://') || permission.startsWith('http://')) {
        try {
          new URL(permission); // Check if it's a valid URL.
          return true;
        } catch (e) {
          return false;
        }
      }

      return false;
    });
  }

  /**
   * Регистрирует обработчики IPC для коммуникации с песочницей
   * @private
   */
  _registerIpcHandlers() {
    // Обработчик запросов от расширений в песочнице
    ipcMain.handle(
      'extension-sandbox-request',
      async (event, { extensionId, token, action, data }) => {
        const sandbox = this.sandboxedExtensions.get(extensionId);

        // Проверяем валидность запроса
        if (!sandbox || sandbox.token !== token) {
          return { success: false, error: 'Недействительный токен песочницы' };
        }

        // Обрабатываем различные типы запросов
        switch (action) {
          case 'checkPermission':
            return {
              success: true,
              hasPermission: this.hasPermission(extensionId, data.permission),
            };

          case 'executeAPI':
            // Проверяем разрешение для API
            if (!this.hasPermission(extensionId, data.apiNamespace)) {
              return {
                success: false,
                error: `Отсутствует разрешение для ${data.apiNamespace}`,
              };
            }

            // Здесь будет логика выполнения API-запросов
            // ...

            return { success: true, result: 'API выполнен' };

          default:
            return { success: false, error: 'Неизвестное действие' };
        }
      }
    );
  }
}

module.exports = ExtensionSandbox;
