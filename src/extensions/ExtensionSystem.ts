/**
 * Advanced Extension System
 * Secure, sandboxed extension architecture with marketplace integration
 */

import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';

export interface ExtensionManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: {
    name: string;
    email: string;
    url?: string;
  };
  homepage?: string;
  repository?: string;
  license: string;
  keywords: string[];
  category: 'productivity' | 'security' | 'developer' | 'entertainment' | 'utility' | 'accessibility';

  // Technical specifications
  engine: {
    minimum: string; // Minimum browser engine version
    maximum?: string; // Maximum browser engine version
  };
  permissions: ExtensionPermission[];
  contentScripts?: ContentScript[];
  backgroundScripts?: string[];
  webAccessibleResources?: string[];

  // UI integration
  browserAction?: {
    defaultTitle: string;
    defaultIcon: string;
    defaultPopup?: string;
  };
  pageAction?: {
    defaultTitle: string;
    defaultIcon: string;
    defaultPopup?: string;
  };
  contextMenus?: ContextMenuItem[];

  // Advanced features
  sandbox: SandboxConfig;
  security: SecurityConfig;
  resources: ResourceLimits;

  // Metadata
  created: Date;
  updated: Date;
  verified: boolean;
  rating: number;
  downloads: number;
}

export interface ExtensionPermission {
  name: string;
  description: string;
  required: boolean;
  dangerous: boolean;
  scope: 'host' | 'api' | 'storage' | 'network' | 'system';
}

export interface ContentScript {
  matches: string[];
  excludeMatches?: string[];
  js?: string[];
  css?: string[];
  runAt: 'document_start' | 'document_end' | 'document_idle';
  allFrames?: boolean;
  matchAboutBlank?: boolean;
}

export interface ContextMenuItem {
  id: string;
  title: string;
  contexts: ('page' | 'selection' | 'link' | 'image' | 'video' | 'audio')[];
  onclick?: string;
  parentId?: string;
  enabled?: boolean;
  visible?: boolean;
}

export interface SandboxConfig {
  enabled: boolean;
  isolatedWorld: boolean;
  csp: string;
  allowedOrigins: string[];
  blockedAPIs: string[];
  resourceLimits: {
    memory: number; // bytes
    cpu: number; // percentage
    network: number; // bytes/sec
    storage: number; // bytes
  };
}

export interface SecurityConfig {
  codeSigningRequired: boolean;
  trustedPublisher: boolean;
  malwareScanned: boolean;
  privacyCompliant: boolean;
  encryptedStorage: boolean;
  secureMessaging: boolean;
}

export interface ResourceLimits {
  maxMemory: number; // bytes
  maxCPU: number; // percentage
  maxNetworkRequests: number; // per minute
  maxStorageSize: number; // bytes
  maxFileSize: number; // bytes
  timeout: number; // milliseconds
}

export interface Extension {
  manifest: ExtensionManifest;
  state: 'installed' | 'enabled' | 'disabled' | 'suspended' | 'error';
  runtime: ExtensionRuntime;
  permissions: {
    granted: string[];
    requested: string[];
    revoked: string[];
  };
  statistics: {
    installDate: Date;
    lastUsed: Date;
    usageCount: number;
    errorCount: number;
    performanceScore: number;
  };
  sandbox: ExtensionSandbox;
}

export interface ExtensionRuntime {
  id: string;
  context: Worker | null;
  messagePort: MessagePort | null;
  apis: Map<string, any>;
  storage: Map<string, any>;
  timers: Set<NodeJS.Timeout>;
  eventListeners: Map<string, Function[]>;
}

export interface ExtensionSandbox {
  worker: Worker | null;
  iframe: HTMLIFrameElement | null;
  csp: string;
  allowedOrigins: Set<string>;
  blockedAPIs: Set<string>;
  resourceMonitor: ResourceMonitor;
}

export interface ResourceMonitor {
  memory: {
    current: number;
    peak: number;
    limit: number;
  };
  cpu: {
    current: number;
    average: number;
    limit: number;
  };
  network: {
    requests: number;
    bytesTransferred: number;
    limit: number;
  };
  storage: {
    used: number;
    limit: number;
  };
}

export interface ExtensionMessage {
  id: string;
  type: 'request' | 'response' | 'event' | 'error';
  source: string;
  target: string;
  action: string;
  data: any;
  timestamp: Date;
  encrypted: boolean;
}

export interface MarketplaceEntry {
  id: string;
  manifest: ExtensionManifest;
  packageUrl: string;
  checksum: string;
  signature: string;
  reviews: ExtensionReview[];
  compatibility: {
    browserVersions: string[];
    platforms: string[];
    tested: boolean;
  };
  security: {
    scanned: boolean;
    threats: string[];
    score: number;
  };
  featured: boolean;
  trending: boolean;
}

export interface ExtensionReview {
  id: string;
  userId: string;
  rating: number;
  title: string;
  comment: string;
  version: string;
  helpful: number;
  timestamp: Date;
  verified: boolean;
}

export class ExtensionSystem extends EventEmitter {
  private extensions = new Map<string, Extension>();
  private marketplace = new Map<string, MarketplaceEntry>();
  private messageQueue: ExtensionMessage[] = [];
  private securityScanner: SecurityScanner;
  private sandboxManager: SandboxManager;

  private maxExtensions = 100;
  private globalResourceLimits: ResourceLimits = {
    maxMemory: 512 * 1024 * 1024, // 512MB
    maxCPU: 10, // 10%
    maxNetworkRequests: 1000, // per minute
    maxStorageSize: 100 * 1024 * 1024, // 100MB
    maxFileSize: 10 * 1024 * 1024, // 10MB
    timeout: 30000, // 30 seconds
  };

  constructor() {
    super();
    this.securityScanner = new SecurityScanner();
    this.sandboxManager = new SandboxManager();
    this.initializeExtensionSystem();
  }

  /**
   * Install extension from package
   */
  async installExtension(
    packageData: ArrayBuffer | string,
    options: {
      force?: boolean;
      skipSecurity?: boolean;
      grantPermissions?: boolean;
    } = {}
  ): Promise<Extension> {
    try {
      // Parse extension package
      const manifest = await this.parseExtensionPackage(packageData);

      // Validate manifest
      this.validateManifest(manifest);

      // Check if already installed
      if (this.extensions.has(manifest.id) && !options.force) {
        throw new Error(`Extension ${manifest.id} is already installed`);
      }

      // Security scan
      if (!options.skipSecurity) {
        const securityResult = await this.securityScanner.scanExtension(packageData, manifest);
        if (!securityResult.safe) {
          throw new Error(`Security scan failed: ${securityResult.threats.join(', ')}`);
        }
      }

      // Check resource limits
      this.checkResourceLimits(manifest);

      // Create extension runtime
      const runtime = await this.createExtensionRuntime(manifest);

      // Create sandbox
      const sandbox = await this.sandboxManager.createSandbox(manifest.sandbox);

      // Create extension instance
      const extension: Extension = {
        manifest,
        state: 'installed',
        runtime,
        permissions: {
          granted: options.grantPermissions ? manifest.permissions.map(p => p.name) : [],
          requested: manifest.permissions.map(p => p.name),
          revoked: [],
        },
        statistics: {
          installDate: new Date(),
          lastUsed: new Date(),
          usageCount: 0,
          errorCount: 0,
          performanceScore: 100,
        },
        sandbox,
      };

      this.extensions.set(manifest.id, extension);

      logger.info(`Extension installed: ${manifest.name} (${manifest.id})`);
      this.emit('extension-installed', extension);

      return extension;
    } catch (error) {
      logger.error('Failed to install extension', error);
      throw error;
    }
  }

  /**
   * Enable extension
   */
  async enableExtension(extensionId: string): Promise<boolean> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (extension.state === 'enabled') {
      return true;
    }

    try {
      // Check permissions
      if (extension.permissions.granted.length === 0) {
        throw new Error('Extension has no granted permissions');
      }

      // Initialize runtime
      await this.initializeExtensionRuntime(extension);

      // Load content scripts
      await this.loadContentScripts(extension);

      // Load background scripts
      await this.loadBackgroundScripts(extension);

      // Register context menus
      this.registerContextMenus(extension);

      // Update state
      extension.state = 'enabled';
      extension.statistics.lastUsed = new Date();

      logger.info(`Extension enabled: ${extension.manifest.name}`);
      this.emit('extension-enabled', extension);

      return true;
    } catch (error) {
      extension.state = 'error';
      extension.statistics.errorCount++;

      logger.error(`Failed to enable extension ${extensionId}`, error);
      this.emit('extension-error', { extension, error });

      return false;
    }
  }

  /**
   * Disable extension
   */
  async disableExtension(extensionId: string): Promise<boolean> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (extension.state === 'disabled') {
      return true;
    }

    try {
      // Cleanup runtime
      await this.cleanupExtensionRuntime(extension);

      // Remove content scripts
      this.removeContentScripts(extension);

      // Unregister context menus
      this.unregisterContextMenus(extension);

      // Update state
      extension.state = 'disabled';

      logger.info(`Extension disabled: ${extension.manifest.name}`);
      this.emit('extension-disabled', extension);

      return true;
    } catch (error) {
      logger.error(`Failed to disable extension ${extensionId}`, error);
      return false;
    }
  }

  /**
   * Uninstall extension
   */
  async uninstallExtension(extensionId: string): Promise<boolean> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    try {
      // Disable first
      if (extension.state === 'enabled') {
        await this.disableExtension(extensionId);
      }

      // Cleanup sandbox
      await this.sandboxManager.destroySandbox(extension.sandbox);

      // Clear storage
      extension.runtime.storage.clear();

      // Remove from registry
      this.extensions.delete(extensionId);

      logger.info(`Extension uninstalled: ${extension.manifest.name}`);
      this.emit('extension-uninstalled', extension);

      return true;
    } catch (error) {
      logger.error(`Failed to uninstall extension ${extensionId}`, error);
      return false;
    }
  }

  /**
   * Send message to extension
   */
  async sendMessage(
    targetExtensionId: string,
    action: string,
    data: any,
    options: {
      timeout?: number;
      encrypted?: boolean;
    } = {}
  ): Promise<any> {
    const extension = this.extensions.get(targetExtensionId);
    if (!extension || extension.state !== 'enabled') {
      throw new Error(`Extension ${targetExtensionId} not available`);
    }

    const message: ExtensionMessage = {
      id: this.generateMessageId(),
      type: 'request',
      source: 'system',
      target: targetExtensionId,
      action,
      data,
      timestamp: new Date(),
      encrypted: options.encrypted || false,
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Message timeout'));
      }, options.timeout || 5000);

      // Send message to extension
      this.sendMessageToExtension(extension, message)
        .then(response => {
          clearTimeout(timeout);
          resolve(response);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  /**
   * Grant permission to extension
   */
  grantPermission(extensionId: string, permission: string): boolean {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (!extension.permissions.requested.includes(permission)) {
      throw new Error(`Permission ${permission} not requested by extension`);
    }

    if (extension.permissions.granted.includes(permission)) {
      return true; // Already granted
    }

    extension.permissions.granted.push(permission);
    extension.permissions.revoked = extension.permissions.revoked.filter(p => p !== permission);

    logger.info(`Granted permission ${permission} to extension ${extension.manifest.name}`);
    this.emit('permission-granted', { extension, permission });

    return true;
  }

  /**
   * Revoke permission from extension
   */
  revokePermission(extensionId: string, permission: string): boolean {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (!extension.permissions.granted.includes(permission)) {
      return true; // Already revoked
    }

    extension.permissions.granted = extension.permissions.granted.filter(p => p !== permission);
    extension.permissions.revoked.push(permission);

    logger.info(`Revoked permission ${permission} from extension ${extension.manifest.name}`);
    this.emit('permission-revoked', { extension, permission });

    return true;
  }

  /**
   * Get extension statistics
   */
  getExtensionStats(): {
    total: number;
    enabled: number;
    disabled: number;
    suspended: number;
    errors: number;
    memoryUsage: number;
    cpuUsage: number;
    networkRequests: number;
  } {
    const extensions = Array.from(this.extensions.values());

    return {
      total: extensions.length,
      enabled: extensions.filter(e => e.state === 'enabled').length,
      disabled: extensions.filter(e => e.state === 'disabled').length,
      suspended: extensions.filter(e => e.state === 'suspended').length,
      errors: extensions.filter(e => e.state === 'error').length,
      memoryUsage: extensions.reduce((sum, e) => sum + e.sandbox.resourceMonitor.memory.current, 0),
      cpuUsage: extensions.reduce((sum, e) => sum + e.sandbox.resourceMonitor.cpu.current, 0),
      networkRequests: extensions.reduce((sum, e) => sum + e.sandbox.resourceMonitor.network.requests, 0),
    };
  }

  /**
   * Get installed extensions
   */
  getInstalledExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  /**
   * Get extension by ID
   */
  getExtension(extensionId: string): Extension | undefined {
    return this.extensions.get(extensionId);
  }

  /**
   * Search marketplace
   */
  async searchMarketplace(
    query: string,
    filters: {
      category?: string;
      rating?: number;
      verified?: boolean;
      free?: boolean;
    } = {}
  ): Promise<MarketplaceEntry[]> {
    // Simulate marketplace search
    const results = Array.from(this.marketplace.values())
      .filter(entry => {
        if (query && !entry.manifest.name.toLowerCase().includes(query.toLowerCase()) &&
            !entry.manifest.description.toLowerCase().includes(query.toLowerCase())) {
          return false;
        }

        if (filters.category && entry.manifest.category !== filters.category) {
          return false;
        }

        if (filters.rating && entry.manifest.rating < filters.rating) {
          return false;
        }

        if (filters.verified && !entry.manifest.verified) {
          return false;
        }

        return true;
      })
      .sort((a, b) => b.manifest.rating - a.manifest.rating);

    return results;
  }

  /**
   * Initialize extension system
   */
  private initializeExtensionSystem(): void {
    logger.info('Initializing extension system');

    // Setup message handling
    this.setupMessageHandling();

    // Setup resource monitoring
    this.setupResourceMonitoring();

    // Load marketplace data
    this.loadMarketplaceData();

    this.emit('system-initialized');
  }

  /**
   * Parse extension package
   */
  private async parseExtensionPackage(packageData: ArrayBuffer | string): Promise<ExtensionManifest> {
    // In a real implementation, this would parse ZIP files, validate signatures, etc.
    // For now, simulate parsing
    const manifest: ExtensionManifest = {
      id: `ext_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: 'Sample Extension',
      version: '1.0.0',
      description: 'A sample extension',
      author: {
        name: 'Developer',
        email: '<EMAIL>',
      },
      license: 'MIT',
      keywords: ['sample', 'extension'],
      category: 'utility',
      engine: {
        minimum: '1.0.0',
      },
      permissions: [
        {
          name: 'tabs',
          description: 'Access browser tabs',
          required: true,
          dangerous: false,
          scope: 'api',
        },
      ],
      sandbox: {
        enabled: true,
        isolatedWorld: true,
        csp: "default-src 'self'",
        allowedOrigins: [],
        blockedAPIs: [],
        resourceLimits: {
          memory: 50 * 1024 * 1024, // 50MB
          cpu: 5, // 5%
          network: 1024 * 1024, // 1MB/sec
          storage: 10 * 1024 * 1024, // 10MB
        },
      },
      security: {
        codeSigningRequired: false,
        trustedPublisher: false,
        malwareScanned: true,
        privacyCompliant: true,
        encryptedStorage: false,
        secureMessaging: false,
      },
      resources: {
        maxMemory: 100 * 1024 * 1024, // 100MB
        maxCPU: 10, // 10%
        maxNetworkRequests: 100, // per minute
        maxStorageSize: 50 * 1024 * 1024, // 50MB
        maxFileSize: 5 * 1024 * 1024, // 5MB
        timeout: 10000, // 10 seconds
      },
      created: new Date(),
      updated: new Date(),
      verified: false,
      rating: 0,
      downloads: 0,
    };

    return manifest;
  }

  /**
   * Validate extension manifest
   */
  private validateManifest(manifest: ExtensionManifest): void {
    if (!manifest.id || !manifest.name || !manifest.version) {
      throw new Error('Invalid manifest: missing required fields');
    }

    if (manifest.permissions.length === 0) {
      throw new Error('Extension must request at least one permission');
    }

    // Additional validation logic...
  }

  /**
   * Check resource limits
   */
  private checkResourceLimits(manifest: ExtensionManifest): void {
    if (this.extensions.size >= this.maxExtensions) {
      throw new Error('Maximum number of extensions reached');
    }

    // Check global resource limits
    const currentMemoryUsage = Array.from(this.extensions.values())
      .reduce((sum, e) => sum + e.sandbox.resourceMonitor.memory.current, 0);

    if (currentMemoryUsage + manifest.resources.maxMemory > this.globalResourceLimits.maxMemory) {
      throw new Error('Global memory limit would be exceeded');
    }
  }

  /**
   * Create extension runtime
   */
  private async createExtensionRuntime(manifest: ExtensionManifest): Promise<ExtensionRuntime> {
    return {
      id: manifest.id,
      context: null,
      messagePort: null,
      apis: new Map(),
      storage: new Map(),
      timers: new Set(),
      eventListeners: new Map(),
    };
  }

  // Additional private methods would continue here...
  // Due to length constraints, I'll add placeholder implementations

  private async initializeExtensionRuntime(extension: Extension): Promise<void> {}
  private async loadContentScripts(extension: Extension): Promise<void> {}
  private async loadBackgroundScripts(extension: Extension): Promise<void> {}
  private registerContextMenus(extension: Extension): void {}
  private async cleanupExtensionRuntime(extension: Extension): Promise<void> {}
  private removeContentScripts(extension: Extension): void {}
  private unregisterContextMenus(extension: Extension): void {}
  private async sendMessageToExtension(extension: Extension, message: ExtensionMessage): Promise<any> {}
  private setupMessageHandling(): void {}
  private setupResourceMonitoring(): void {}
  private loadMarketplaceData(): void {}
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    // Disable all extensions
    for (const [id] of this.extensions) {
      this.disableExtension(id).catch(error => {
        logger.error(`Error disabling extension ${id} during cleanup`, error);
      });
    }

    this.extensions.clear();
    this.marketplace.clear();
    this.messageQueue.length = 0;
    this.removeAllListeners();
  }
}

// Placeholder classes for security and sandbox management
class SecurityScanner {
  async scanExtension(packageData: ArrayBuffer | string, manifest: ExtensionManifest): Promise<{
    safe: boolean;
    threats: string[];
    score: number;
  }> {
    // Simulate security scan
    return { safe: true, threats: [], score: 100 };
  }
}

class SandboxManager {
  async createSandbox(config: SandboxConfig): Promise<ExtensionSandbox> {
    return {
      worker: null,
      iframe: null,
      csp: config.csp,
      allowedOrigins: new Set(config.allowedOrigins),
      blockedAPIs: new Set(config.blockedAPIs),
      resourceMonitor: {
        memory: { current: 0, peak: 0, limit: config.resourceLimits.memory },
        cpu: { current: 0, average: 0, limit: config.resourceLimits.cpu },
        network: { requests: 0, bytesTransferred: 0, limit: config.resourceLimits.network },
        storage: { used: 0, limit: config.resourceLimits.storage },
      },
    };
  }

  async destroySandbox(sandbox: ExtensionSandbox): Promise<void> {
    // Cleanup sandbox resources
  }
}

// Global extension system instance
export const extensionSystem = new ExtensionSystem();

export default extensionSystem;