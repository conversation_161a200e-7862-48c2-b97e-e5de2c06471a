import crypto from 'crypto';

import jwt from 'jsonwebtoken';

import { logger } from '../logging/Logger';
import { SecurityConfig } from '../types';

export class SecurityService {
  private static instance: SecurityService;
  private config: SecurityConfig;
  private encryptionKey: Buffer;

  private constructor(config: SecurityConfig) {
    this.config = config;
    this.initialize();
  }

  public static getInstance(config: SecurityConfig): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService(config);
    }
    return SecurityService.instance;
  }

  private initialize(): void {
    this.encryptionKey = this.generateEncryptionKey();
  }

  private generateEncryptionKey(): Buffer {
    return crypto.pbkdf2Sync(
      this.config.jwt.secret,
      'salt',
      this.config.encryption.iterations,
      this.config.encryption.keySize,
      'sha512'
    );
  }

  public encrypt(data: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(
        this.config.encryption.algorithm,
        this.encryptionKey,
        iv
      );
      const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
      const authTag = cipher.getAuthTag();
      return Buffer.concat([iv, authTag, encrypted]).toString('base64');
    } catch (error) {
      logger.error('Encryption failed:', error);
      throw new Error('Encryption failed');
    }
  }

  public decrypt(encryptedData: string): string {
    try {
      const buffer = Buffer.from(encryptedData, 'base64');
      const iv = buffer.slice(0, 16);
      const authTag = buffer.slice(16, 32);
      const encrypted = buffer.slice(32);
      const decipher = crypto.createDecipheriv(
        this.config.encryption.algorithm,
        this.encryptionKey,
        iv
      );
      decipher.setAuthTag(authTag);
      return decipher.update(encrypted) + decipher.final('utf8');
    } catch (error) {
      logger.error('Decryption failed:', error);
      throw new Error('Decryption failed');
    }
  }

  public generateToken(payload: any): string {
    try {
      return jwt.sign(payload, this.config.jwt.secret, {
        expiresIn: this.config.jwt.expiresIn,
      });
    } catch (error) {
      logger.error('Token generation failed:', error);
      throw new Error('Token generation failed');
    }
  }

  public verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.config.jwt.secret);
    } catch (error) {
      logger.error('Token verification failed:', error);
      throw new Error('Token verification failed');
    }
  }

  public generateRefreshToken(payload: any): string {
    try {
      return jwt.sign(payload, this.config.jwt.secret, {
        expiresIn: this.config.jwt.refreshExpiresIn,
      });
    } catch (error) {
      logger.error('Refresh token generation failed:', error);
      throw new Error('Refresh token generation failed');
    }
  }

  public hashPassword(password: string): string {
    try {
      return crypto
        .pbkdf2Sync(
          password,
          'salt',
          this.config.encryption.iterations,
          this.config.encryption.keySize,
          'sha512'
        )
        .toString('hex');
    } catch (error) {
      logger.error('Password hashing failed:', error);
      throw new Error('Password hashing failed');
    }
  }

  public verifyPassword(password: string, hash: string): boolean {
    try {
      const hashedPassword = this.hashPassword(password);
      return crypto.timingSafeEqual(Buffer.from(hashedPassword), Buffer.from(hash));
    } catch (error) {
      logger.error('Password verification failed:', error);
      throw new Error('Password verification failed');
    }
  }
}

export const securityService = SecurityService.getInstance({
  encryption: {
    algorithm: 'aes-256-gcm',
    keySize: 32,
    iterations: 100000,
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: '1h',
    refreshExpiresIn: '7d',
  },
});
