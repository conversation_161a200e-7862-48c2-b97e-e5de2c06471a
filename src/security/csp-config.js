// Strict CSP policies for extensions sandboxing
export const cspPolicies = {
  default: {
    'default-src': ['none'],
    'script-src': ['self', 'unsafe-eval'],
    'style-src': ['self', 'unsafe-inline'],
    'connect-src': ['self', 'https://api.browser-core.com'],
    'img-src': ['self', 'data:'],
    'frame-src': ['none'],
    'worker-src': ['none'],
  },
  development: {
    'script-src': ['self', 'unsafe-eval', 'http://localhost:*', 'ws://localhost:*'],
    'connect-src': ['self', 'ws://localhost:*'],
  },
  production: {
    'default-src': ['none'],
    'script-src': ['self'],
    'style-src': ['self', 'unsafe-inline'],
    'img-src': ['self', 'data:'],
    'connect-src': ['self'],
  },
};

// Policy validation and injection mechanism
export function applySecurityPolicy(policyName = 'default') {
  const metaTag = document.createElement('meta');
  metaTag.httpEquiv = 'Content-Security-Policy';
  metaTag.content = Object.entries(cspPolicies[policyName])
    .map(([key, values]) => `${key} ${values.join(' ')}`)
    .join('; ');

  document.head.appendChild(metaTag);
}
