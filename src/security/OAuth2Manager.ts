/**
 * OAuth 2.0 и JWT Manager для современной аутентификации
 */

import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

export interface OAuth2Config {
  issuer: string;
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
  tokenEndpoint: string;
  authorizationEndpoint: string;
  userInfoEndpoint: string;
  jwksUri: string;
}

export interface JWTConfig {
  accessTokenSecret: string;
  refreshTokenSecret: string;
  accessTokenExpiry: string;
  refreshTokenExpiry: string;
  issuer: string;
  audience: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
  scope: string;
}

export interface JWTPayload {
  sub: string; // Subject (user ID)
  iss: string; // Issuer
  aud: string; // Audience
  exp: number; // Expiration time
  iat: number; // Issued at
  jti: string; // JWT ID
  scope: string;
  roles: string[];
  permissions: string[];
  metadata?: Record<string, any>;
}

export interface AuthorizationCode {
  code: string;
  clientId: string;
  userId: string;
  redirectUri: string;
  scope: string;
  expiresAt: Date;
  codeChallenge?: string;
  codeChallengeMethod?: 'S256' | 'plain';
}

export interface RefreshTokenData {
  tokenId: string;
  userId: string;
  clientId: string;
  scope: string;
  expiresAt: Date;
  isRevoked: boolean;
}

export class OAuth2Manager {
  private jwtConfig: JWTConfig;
  private authorizationCodes = new Map<string, AuthorizationCode>();
  private refreshTokens = new Map<string, RefreshTokenData>();
  private revokedTokens = new Set<string>();

  constructor(jwtConfig: JWTConfig) {
    this.jwtConfig = jwtConfig;
    this.startTokenCleanup();
  }

  /**
   * Генерирует authorization code для OAuth 2.0 flow
   */
  generateAuthorizationCode(
    clientId: string,
    userId: string,
    redirectUri: string,
    scope: string,
    codeChallenge?: string,
    codeChallengeMethod?: 'S256' | 'plain'
  ): string {
    const code = this.generateSecureCode();
    const authCode: AuthorizationCode = {
      code,
      clientId,
      userId,
      redirectUri,
      scope,
      expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
      codeChallenge,
      codeChallengeMethod,
    };

    this.authorizationCodes.set(code, authCode);
    return code;
  }

  /**
   * Обменивает authorization code на токены
   */
  async exchangeCodeForTokens(
    code: string,
    clientId: string,
    clientSecret: string,
    redirectUri: string,
    codeVerifier?: string
  ): Promise<TokenPair | null> {
    const authCode = this.authorizationCodes.get(code);

    if (!authCode) {
      throw new Error('Invalid authorization code');
    }

    if (authCode.expiresAt < new Date()) {
      this.authorizationCodes.delete(code);
      throw new Error('Authorization code expired');
    }

    if (authCode.clientId !== clientId) {
      throw new Error('Client ID mismatch');
    }

    if (authCode.redirectUri !== redirectUri) {
      throw new Error('Redirect URI mismatch');
    }

    // Проверяем PKCE если используется
    if (authCode.codeChallenge) {
      if (!codeVerifier) {
        throw new Error('Code verifier required');
      }

      const isValidChallenge = await this.verifyPKCE(
        codeVerifier,
        authCode.codeChallenge,
        authCode.codeChallengeMethod || 'S256'
      );

      if (!isValidChallenge) {
        throw new Error('Invalid code verifier');
      }
    }

    // Удаляем использованный код
    this.authorizationCodes.delete(code);

    // Генерируем токены
    return this.generateTokenPair(authCode.userId, clientId, authCode.scope);
  }

  /**
   * Генерирует пару токенов (access + refresh)
   */
  generateTokenPair(userId: string, clientId: string, scope: string): TokenPair {
    const jti = uuidv4();
    const now = Math.floor(Date.now() / 1000);

    // Access Token payload
    const accessPayload: JWTPayload = {
      sub: userId,
      iss: this.jwtConfig.issuer,
      aud: this.jwtConfig.audience,
      exp: now + this.parseExpiry(this.jwtConfig.accessTokenExpiry),
      iat: now,
      jti,
      scope,
      roles: [], // Загружается из базы данных
      permissions: [], // Загружается из базы данных
    };

    // Refresh Token payload
    const refreshPayload = {
      sub: userId,
      iss: this.jwtConfig.issuer,
      aud: this.jwtConfig.audience,
      exp: now + this.parseExpiry(this.jwtConfig.refreshTokenExpiry),
      iat: now,
      jti: uuidv4(),
      type: 'refresh',
      scope,
    };

    const accessToken = jwt.sign(accessPayload, this.jwtConfig.accessTokenSecret, {
      algorithm: 'HS256',
    });

    const refreshToken = jwt.sign(refreshPayload, this.jwtConfig.refreshTokenSecret, {
      algorithm: 'HS256',
    });

    // Сохраняем refresh token
    this.refreshTokens.set(refreshPayload.jti, {
      tokenId: refreshPayload.jti,
      userId,
      clientId,
      scope,
      expiresAt: new Date(refreshPayload.exp * 1000),
      isRevoked: false,
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: this.parseExpiry(this.jwtConfig.accessTokenExpiry),
      tokenType: 'Bearer',
      scope,
    };
  }

  /**
   * Обновляет токены используя refresh token
   */
  async refreshTokens(refreshToken: string, clientId: string): Promise<TokenPair | null> {
    try {
      const decoded = jwt.verify(refreshToken, this.jwtConfig.refreshTokenSecret) as any;

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      const tokenData = this.refreshTokens.get(decoded.jti);
      if (!tokenData || tokenData.isRevoked) {
        throw new Error('Refresh token revoked or not found');
      }

      if (tokenData.clientId !== clientId) {
        throw new Error('Client ID mismatch');
      }

      if (tokenData.expiresAt < new Date()) {
        this.refreshTokens.delete(decoded.jti);
        throw new Error('Refresh token expired');
      }

      // Отзываем старый refresh token
      tokenData.isRevoked = true;

      // Генерируем новую пару токенов
      return this.generateTokenPair(decoded.sub, clientId, decoded.scope);
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * Верифицирует access token
   */
  async verifyAccessToken(token: string): Promise<JWTPayload | null> {
    try {
      const decoded = jwt.verify(token, this.jwtConfig.accessTokenSecret) as JWTPayload;

      // Проверяем, не отозван ли токен
      if (this.revokedTokens.has(decoded.jti)) {
        throw new Error('Token revoked');
      }

      return decoded;
    } catch (error) {
      return null;
    }
  }

  /**
   * Отзывает токен
   */
  revokeToken(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded?.jti) {
        this.revokedTokens.add(decoded.jti);

        // Если это refresh token, помечаем его как отозванный
        const refreshTokenData = this.refreshTokens.get(decoded.jti);
        if (refreshTokenData) {
          refreshTokenData.isRevoked = true;
        }

        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Отзывает все токены пользователя
   */
  revokeAllUserTokens(userId: string): void {
    // Отзываем все refresh токены пользователя
    for (const [tokenId, tokenData] of this.refreshTokens) {
      if (tokenData.userId === userId) {
        tokenData.isRevoked = true;
      }
    }
  }

  /**
   * Генерирует PKCE challenge
   */
  generatePKCEChallenge(): { codeVerifier: string; codeChallenge: string } {
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = this.generateCodeChallenge(codeVerifier);

    return { codeVerifier, codeChallenge };
  }

  /**
   * Создает URL для авторизации
   */
  createAuthorizationUrl(
    clientId: string,
    redirectUri: string,
    scope: string,
    state?: string,
    codeChallenge?: string,
    codeChallengeMethod: 'S256' | 'plain' = 'S256'
  ): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: redirectUri,
      scope,
      ...(state && { state }),
      ...(codeChallenge && {
        code_challenge: codeChallenge,
        code_challenge_method: codeChallengeMethod,
      }),
    });

    return `${this.jwtConfig.issuer}/oauth/authorize?${params.toString()}`;
  }

  /**
   * Извлекает токен из заголовка Authorization
   */
  extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  /**
   * Проверяет права доступа
   */
  hasScope(tokenPayload: JWTPayload, requiredScope: string): boolean {
    const tokenScopes = tokenPayload.scope.split(' ');
    return tokenScopes.includes(requiredScope) || tokenScopes.includes('*');
  }

  /**
   * Проверяет роль пользователя
   */
  hasRole(tokenPayload: JWTPayload, requiredRole: string): boolean {
    return tokenPayload.roles.includes(requiredRole) || tokenPayload.roles.includes('admin');
  }

  /**
   * Проверяет разрешение
   */
  hasPermission(tokenPayload: JWTPayload, requiredPermission: string): boolean {
    return (
      tokenPayload.permissions.includes(requiredPermission) ||
      tokenPayload.permissions.includes('*')
    );
  }

  private generateSecureCode(): string {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  private generateCodeVerifier(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  private generateCodeChallenge(verifier: string): string {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    return crypto.subtle.digest('SHA-256', data).then(hash => {
      return btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(hash))))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
    }) as any; // Упрощение для примера
  }

  private async verifyPKCE(
    verifier: string,
    challenge: string,
    method: 'S256' | 'plain'
  ): Promise<boolean> {
    if (method === 'plain') {
      return verifier === challenge;
    }

    const generatedChallenge = await this.generateCodeChallenge(verifier);
    return generatedChallenge === challenge;
  }

  private parseExpiry(expiry: string): number {
    const match = expiry.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new Error('Invalid expiry format');
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'h':
        return value * 60 * 60;
      case 'd':
        return value * 24 * 60 * 60;
      default:
        throw new Error('Invalid expiry unit');
    }
  }

  private startTokenCleanup(): void {
    // Очистка истекших токенов каждый час
    setInterval(
      () => {
        const now = new Date();

        // Очищаем истекшие authorization codes
        for (const [code, authCode] of this.authorizationCodes) {
          if (authCode.expiresAt < now) {
            this.authorizationCodes.delete(code);
          }
        }

        // Очищаем истекшие refresh tokens
        for (const [tokenId, tokenData] of this.refreshTokens) {
          if (tokenData.expiresAt < now) {
            this.refreshTokens.delete(tokenId);
          }
        }
      },
      60 * 60 * 1000
    ); // Каждый час
  }
}

// Middleware для Express.js
export function createJWTMiddleware(oauth2Manager: OAuth2Manager) {
  return (req: any, res: any, next: any) => {
    const authHeader = req.headers.authorization;
    const token = oauth2Manager.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    oauth2Manager
      .verifyAccessToken(token)
      .then(payload => {
        if (!payload) {
          return res.status(401).json({ error: 'Invalid access token' });
        }

        req.user = payload;
        next();
      })
      .catch(error => {
        return res.status(401).json({ error: 'Token verification failed' });
      });
  };
}

// Middleware для проверки scope
export function requireScope(scope: string) {
  return (req: any, res: any, next: any) => {
    if (!req.user || !req.user.scope) {
      return res.status(403).json({ error: 'Insufficient scope' });
    }

    const tokenScopes = req.user.scope.split(' ');
    if (!tokenScopes.includes(scope) && !tokenScopes.includes('*')) {
      return res.status(403).json({ error: `Required scope: ${scope}` });
    }

    next();
  };
}

// Фабрика для создания OAuth2 Manager
export class OAuth2ManagerFactory {
  static create(): OAuth2Manager {
    const jwtConfig: JWTConfig = {
      accessTokenSecret: process.env.JWT_ACCESS_SECRET || 'access-secret-change-in-production',
      refreshTokenSecret: process.env.JWT_REFRESH_SECRET || 'refresh-secret-change-in-production',
      accessTokenExpiry: process.env.JWT_ACCESS_EXPIRY || '15m',
      refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRY || '7d',
      issuer: process.env.JWT_ISSUER || 'https://a14-browser.com',
      audience: process.env.JWT_AUDIENCE || 'a14-browser-api',
    };

    return new OAuth2Manager(jwtConfig);
  }
}

// Глобальный экземпляр
export const oauth2Manager = OAuth2ManagerFactory.create();
