import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface PrivacySettings {
  // Tracking Protection
  blockTrackers: boolean;
  blockCrossSiteTrackers: boolean;
  blockSocialMediaTrackers: boolean;
  blockAdvertisingTrackers: boolean;
  blockAnalyticsTrackers: boolean;
  blockFingerprintingScripts: boolean;
  blockCryptominers: boolean;

  // Cookies and Storage
  cookiePolicy: 'accept_all' | 'block_third_party' | 'block_all' | 'ask_always';
  clearCookiesOnExit: boolean;
  clearStorageOnExit: boolean;
  enableSameSiteCookies: boolean;
  enableSecureCookies: boolean;

  // Network Privacy
  enableDoNotTrack: boolean;
  enableGlobalPrivacyControl: boolean;
  disableWebRTC: boolean;
  disableWebGL: boolean;
  spoofUserAgent: boolean;
  customUserAgent?: string;
  enableTorMode: boolean;

  // Content Privacy
  blockMixedContent: boolean;
  blockInsecureContent: boolean;
  disableRefererHeader: boolean;
  stripTrackingParameters: boolean;
  blockWebFonts: boolean;
  blockImages: boolean;

  // Location and Permissions
  blockLocationRequests: boolean;
  blockCameraRequests: boolean;
  blockMicrophoneRequests: boolean;
  blockNotificationRequests: boolean;
  blockGeolocationAPI: boolean;

  // Data Protection
  enableDataEncryption: boolean;
  enableSecureDNS: boolean;
  enableHTTPSEverywhere: boolean;
  enableCertificatePinning: boolean;

  // Compliance
  enableGDPRMode: boolean;
  enableCCPAMode: boolean;
  enableCOPPAMode: boolean;
  dataRetentionDays: number;
}

export interface TrackerInfo {
  domain: string;
  category: 'advertising' | 'analytics' | 'social' | 'fingerprinting' | 'cryptomining' | 'other';
  blocked: boolean;
  requests: number;
  lastSeen: number;
  risk: 'low' | 'medium' | 'high' | 'critical';
}

export interface PrivacyReport {
  id: string;
  timestamp: number;
  duration: number;
  url: string;
  trackersBlocked: number;
  trackersAllowed: number;
  cookiesBlocked: number;
  cookiesAllowed: number;
  requestsBlocked: number;
  requestsAllowed: number;
  trackers: TrackerInfo[];
  privacyScore: number; // 0-100
}

export interface DataCollectionEvent {
  id: string;
  timestamp: number;
  type: 'cookie' | 'storage' | 'fingerprint' | 'location' | 'camera' | 'microphone' | 'other';
  source: string;
  data: any;
  blocked: boolean;
  reason?: string;
}

export class PrivacyManager extends EventEmitter {
  private static instance: PrivacyManager;
  private settings: PrivacySettings;
  private trackerDatabase: Map<string, TrackerInfo> = new Map();
  private privacyReports: Map<string, PrivacyReport> = new Map();
  private dataCollectionEvents: Map<string, DataCollectionEvent> = new Map();
  private blockedDomains: Set<string> = new Set();

  private constructor() {
    super();
    this.settings = this.getDefaultSettings();
    this.initializePrivacyManager();
  }

  public static getInstance(): PrivacyManager {
    if (!PrivacyManager.instance) {
      PrivacyManager.instance = new PrivacyManager();
    }
    return PrivacyManager.instance;
  }

  private getDefaultSettings(): PrivacySettings {
    return {
      // Tracking Protection
      blockTrackers: true,
      blockCrossSiteTrackers: true,
      blockSocialMediaTrackers: true,
      blockAdvertisingTrackers: true,
      blockAnalyticsTrackers: false,
      blockFingerprintingScripts: true,
      blockCryptominers: true,

      // Cookies and Storage
      cookiePolicy: 'block_third_party',
      clearCookiesOnExit: false,
      clearStorageOnExit: false,
      enableSameSiteCookies: true,
      enableSecureCookies: true,

      // Network Privacy
      enableDoNotTrack: true,
      enableGlobalPrivacyControl: true,
      disableWebRTC: false,
      disableWebGL: false,
      spoofUserAgent: false,
      enableTorMode: false,

      // Content Privacy
      blockMixedContent: true,
      blockInsecureContent: true,
      disableRefererHeader: false,
      stripTrackingParameters: true,
      blockWebFonts: false,
      blockImages: false,

      // Location and Permissions
      blockLocationRequests: false,
      blockCameraRequests: false,
      blockMicrophoneRequests: false,
      blockNotificationRequests: false,
      blockGeolocationAPI: false,

      // Data Protection
      enableDataEncryption: true,
      enableSecureDNS: true,
      enableHTTPSEverywhere: true,
      enableCertificatePinning: false,

      // Compliance
      enableGDPRMode: false,
      enableCCPAMode: false,
      enableCOPPAMode: false,
      dataRetentionDays: 30,
    };
  }

  private async initializePrivacyManager(): Promise<void> {
    // Load settings
    const savedSettings = configManager.get('privacy', {});
    this.settings = { ...this.settings, ...savedSettings };

    // Load tracker database
    await this.loadTrackerDatabase();

    // Setup request interception
    this.setupRequestInterception();

    // Setup privacy monitoring
    this.setupPrivacyMonitoring();

    // Apply current settings
    await this.applyPrivacySettings();

    logger.info('Privacy manager initialized', {
      trackersInDatabase: this.trackerDatabase.size,
      privacyScore: await this.calculatePrivacyScore(),
    });
  }

  public async updateSettings(newSettings: Partial<PrivacySettings>): Promise<void> {
    const oldSettings = { ...this.settings };
    this.settings = { ...this.settings, ...newSettings };

    // Save settings
    configManager.set('privacy', this.settings);

    // Apply new settings
    await this.applyPrivacySettings();

    this.emit('settings_updated', { oldSettings, newSettings: this.settings });
    logger.info('Privacy settings updated', { changes: newSettings });
  }

  private async applyPrivacySettings(): Promise<void> {
    // Apply Do Not Track
    if (this.settings.enableDoNotTrack && typeof navigator !== 'undefined') {
      // Set DNT header (would be handled by browser engine)
      logger.debug('Do Not Track enabled');
    }

    // Apply Global Privacy Control
    if (this.settings.enableGlobalPrivacyControl && typeof navigator !== 'undefined') {
      // Set GPC signal (would be handled by browser engine)
      logger.debug('Global Privacy Control enabled');
    }

    // Apply cookie settings
    await this.applyCookieSettings();

    // Apply content blocking
    await this.applyContentBlocking();

    // Apply network privacy settings
    await this.applyNetworkPrivacy();
  }

  private async applyCookieSettings(): Promise<void> {
    // In a real implementation, this would configure cookie handling
    logger.debug('Cookie settings applied', {
      policy: this.settings.cookiePolicy,
      sameSite: this.settings.enableSameSiteCookies,
      secure: this.settings.enableSecureCookies,
    });
  }

  private async applyContentBlocking(): Promise<void> {
    // Update blocked domains based on settings
    this.blockedDomains.clear();

    if (this.settings.blockAdvertisingTrackers) {
      this.addBlockedDomains(this.getAdvertisingTrackers());
    }

    if (this.settings.blockAnalyticsTrackers) {
      this.addBlockedDomains(this.getAnalyticsTrackers());
    }

    if (this.settings.blockSocialMediaTrackers) {
      this.addBlockedDomains(this.getSocialMediaTrackers());
    }

    if (this.settings.blockFingerprintingScripts) {
      this.addBlockedDomains(this.getFingerprintingDomains());
    }

    if (this.settings.blockCryptominers) {
      this.addBlockedDomains(this.getCryptominingDomains());
    }

    logger.debug('Content blocking applied', {
      blockedDomains: this.blockedDomains.size,
    });
  }

  private async applyNetworkPrivacy(): Promise<void> {
    // Apply network-level privacy settings
    if (this.settings.disableWebRTC) {
      // Disable WebRTC (would be handled by browser engine)
      logger.debug('WebRTC disabled for privacy');
    }

    if (this.settings.disableWebGL) {
      // Disable WebGL (would be handled by browser engine)
      logger.debug('WebGL disabled for privacy');
    }

    if (this.settings.spoofUserAgent) {
      // Apply custom user agent (would be handled by browser engine)
      logger.debug('User agent spoofing enabled');
    }
  }

  public shouldBlockRequest(
    url: string,
    type: string,
    origin?: string
  ): {
    blocked: boolean;
    reason?: string;
    category?: string;
  } {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname;

      // Check if domain is in blocked list
      if (this.blockedDomains.has(domain)) {
        const tracker = this.trackerDatabase.get(domain);
        return {
          blocked: true,
          reason: 'Blocked tracker domain',
          category: tracker?.category || 'unknown',
        };
      }

      // Check for cross-site tracking
      if (this.settings.blockCrossSiteTrackers && origin) {
        try {
          const originObj = new URL(origin);
          if (originObj.hostname !== domain && this.isKnownTracker(domain)) {
            return {
              blocked: true,
              reason: 'Cross-site tracker',
              category: 'tracking',
            };
          }
        } catch {
          // Invalid origin, continue
        }
      }

      // Check for mixed content
      if (this.settings.blockMixedContent && origin) {
        try {
          const originObj = new URL(origin);
          if (originObj.protocol === 'https:' && urlObj.protocol === 'http:') {
            return {
              blocked: true,
              reason: 'Mixed content',
              category: 'security',
            };
          }
        } catch {
          // Invalid origin, continue
        }
      }

      // Check for insecure content
      if (this.settings.blockInsecureContent && urlObj.protocol === 'http:') {
        return {
          blocked: true,
          reason: 'Insecure content',
          category: 'security',
        };
      }

      // Check for web fonts
      if (this.settings.blockWebFonts && type === 'font') {
        return {
          blocked: true,
          reason: 'Web fonts blocked',
          category: 'privacy',
        };
      }

      // Check for images
      if (this.settings.blockImages && type === 'image') {
        return {
          blocked: true,
          reason: 'Images blocked',
          category: 'privacy',
        };
      }

      return { blocked: false };
    } catch (error) {
      logger.warn('Error checking request blocking', { url, error });
      return { blocked: false };
    }
  }

  public recordDataCollection(event: {
    type: DataCollectionEvent['type'];
    source: string;
    data: any;
    blocked: boolean;
    reason?: string;
  }): DataCollectionEvent {
    const eventId = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const dataEvent: DataCollectionEvent = {
      id: eventId,
      timestamp: Date.now(),
      ...event,
    };

    this.dataCollectionEvents.set(eventId, dataEvent);

    // Limit event storage
    if (this.dataCollectionEvents.size > 10000) {
      const oldestEvents = Array.from(this.dataCollectionEvents.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, 1000);

      oldestEvents.forEach(([id]) => {
        this.dataCollectionEvents.delete(id);
      });
    }

    this.emit('data_collection_event', dataEvent);

    if (dataEvent.blocked) {
      logger.debug('Data collection blocked', {
        type: dataEvent.type,
        source: dataEvent.source,
        reason: dataEvent.reason,
      });
    }

    return dataEvent;
  }

  public generatePrivacyReport(url: string, duration: number): PrivacyReport {
    const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Analyze recent events for this URL
    const recentEvents = Array.from(this.dataCollectionEvents.values()).filter(
      event => event.timestamp > Date.now() - duration
    );

    const trackersBlocked = recentEvents.filter(e => e.blocked && e.type === 'cookie').length;
    const trackersAllowed = recentEvents.filter(e => !e.blocked && e.type === 'cookie').length;
    const cookiesBlocked = recentEvents.filter(e => e.blocked && e.type === 'cookie').length;
    const cookiesAllowed = recentEvents.filter(e => !e.blocked && e.type === 'cookie').length;
    const requestsBlocked = recentEvents.filter(e => e.blocked).length;
    const requestsAllowed = recentEvents.filter(e => !e.blocked).length;

    // Get tracker information
    const trackers = Array.from(this.trackerDatabase.values()).filter(
      tracker => tracker.lastSeen > Date.now() - duration
    );

    // Calculate privacy score
    const privacyScore = this.calculatePagePrivacyScore(
      trackersBlocked,
      trackersAllowed,
      requestsBlocked,
      requestsAllowed
    );

    const report: PrivacyReport = {
      id: reportId,
      timestamp: Date.now(),
      duration,
      url,
      trackersBlocked,
      trackersAllowed,
      cookiesBlocked,
      cookiesAllowed,
      requestsBlocked,
      requestsAllowed,
      trackers,
      privacyScore,
    };

    this.privacyReports.set(reportId, report);

    this.emit('privacy_report_generated', report);
    return report;
  }

  private calculatePagePrivacyScore(
    trackersBlocked: number,
    trackersAllowed: number,
    requestsBlocked: number,
    requestsAllowed: number
  ): number {
    let score = 100;

    // Penalize allowed trackers
    score -= trackersAllowed * 10;

    // Reward blocked trackers
    score += Math.min(trackersBlocked * 2, 20);

    // Consider overall blocking ratio
    const totalRequests = requestsBlocked + requestsAllowed;
    if (totalRequests > 0) {
      const blockingRatio = requestsBlocked / totalRequests;
      score += blockingRatio * 20;
    }

    return Math.max(0, Math.min(100, score));
  }

  public async calculatePrivacyScore(): Promise<number> {
    let score = 0;

    // Tracking protection (30 points)
    if (this.settings.blockTrackers) score += 5;
    if (this.settings.blockCrossSiteTrackers) score += 5;
    if (this.settings.blockAdvertisingTrackers) score += 5;
    if (this.settings.blockFingerprintingScripts) score += 10;
    if (this.settings.blockCryptominers) score += 5;

    // Cookie protection (20 points)
    switch (this.settings.cookiePolicy) {
      case 'block_all':
        score += 20;
        break;
      case 'block_third_party':
        score += 15;
        break;
      case 'ask_always':
        score += 10;
        break;
      case 'accept_all':
        score += 0;
        break;
    }

    // Network privacy (20 points)
    if (this.settings.enableDoNotTrack) score += 5;
    if (this.settings.enableGlobalPrivacyControl) score += 5;
    if (this.settings.disableWebRTC) score += 5;
    if (this.settings.enableTorMode) score += 5;

    // Content privacy (15 points)
    if (this.settings.blockMixedContent) score += 5;
    if (this.settings.stripTrackingParameters) score += 5;
    if (this.settings.disableRefererHeader) score += 5;

    // Data protection (15 points)
    if (this.settings.enableDataEncryption) score += 5;
    if (this.settings.enableSecureDNS) score += 5;
    if (this.settings.enableHTTPSEverywhere) score += 5;

    return Math.min(100, score);
  }

  private setupRequestInterception(): void {
    // In a real implementation, this would set up request interception
    // at the browser engine level to block requests based on privacy settings
    logger.debug('Request interception setup completed');
  }

  private setupPrivacyMonitoring(): void {
    // Monitor for privacy-related events
    setInterval(() => {
      this.cleanupOldEvents();
    }, 60000); // Clean up every minute
  }

  private cleanupOldEvents(): void {
    const cutoffTime = Date.now() - this.settings.dataRetentionDays * 24 * 60 * 60 * 1000;

    // Clean up old data collection events
    for (const [id, event] of this.dataCollectionEvents.entries()) {
      if (event.timestamp < cutoffTime) {
        this.dataCollectionEvents.delete(id);
      }
    }

    // Clean up old privacy reports
    for (const [id, report] of this.privacyReports.entries()) {
      if (report.timestamp < cutoffTime) {
        this.privacyReports.delete(id);
      }
    }
  }

  private async loadTrackerDatabase(): Promise<void> {
    // In a real implementation, this would load from a tracker database
    // For now, add some sample trackers
    const sampleTrackers = [
      { domain: 'google-analytics.com', category: 'analytics' as const, risk: 'medium' as const },
      { domain: 'facebook.com', category: 'social' as const, risk: 'high' as const },
      { domain: 'doubleclick.net', category: 'advertising' as const, risk: 'high' as const },
      {
        domain: 'googlesyndication.com',
        category: 'advertising' as const,
        risk: 'medium' as const,
      },
      { domain: 'amazon-adsystem.com', category: 'advertising' as const, risk: 'medium' as const },
    ];

    sampleTrackers.forEach(tracker => {
      this.trackerDatabase.set(tracker.domain, {
        domain: tracker.domain,
        category: tracker.category,
        blocked: false,
        requests: 0,
        lastSeen: 0,
        risk: tracker.risk,
      });
    });
  }

  private addBlockedDomains(domains: string[]): void {
    domains.forEach(domain => this.blockedDomains.add(domain));
  }

  private getAdvertisingTrackers(): string[] {
    return Array.from(this.trackerDatabase.values())
      .filter(tracker => tracker.category === 'advertising')
      .map(tracker => tracker.domain);
  }

  private getAnalyticsTrackers(): string[] {
    return Array.from(this.trackerDatabase.values())
      .filter(tracker => tracker.category === 'analytics')
      .map(tracker => tracker.domain);
  }

  private getSocialMediaTrackers(): string[] {
    return Array.from(this.trackerDatabase.values())
      .filter(tracker => tracker.category === 'social')
      .map(tracker => tracker.domain);
  }

  private getFingerprintingDomains(): string[] {
    return Array.from(this.trackerDatabase.values())
      .filter(tracker => tracker.category === 'fingerprinting')
      .map(tracker => tracker.domain);
  }

  private getCryptominingDomains(): string[] {
    return Array.from(this.trackerDatabase.values())
      .filter(tracker => tracker.category === 'cryptomining')
      .map(tracker => tracker.domain);
  }

  private isKnownTracker(domain: string): boolean {
    return this.trackerDatabase.has(domain);
  }

  // Getters
  public getSettings(): PrivacySettings {
    return { ...this.settings };
  }

  public getPrivacyReports(): PrivacyReport[] {
    return Array.from(this.privacyReports.values());
  }

  public getDataCollectionEvents(): DataCollectionEvent[] {
    return Array.from(this.dataCollectionEvents.values());
  }

  public getTrackerDatabase(): TrackerInfo[] {
    return Array.from(this.trackerDatabase.values());
  }

  public getBlockedDomains(): string[] {
    return Array.from(this.blockedDomains);
  }
}

// Export singleton instance
export const privacyManager = PrivacyManager.getInstance();
