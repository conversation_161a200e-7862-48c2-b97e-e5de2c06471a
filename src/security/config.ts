import { BrowserWindow, app, session } from 'electron';

export const securityConfig = {
  // Content Security Policy
  csp: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", 'data:', 'https:'],
    connectSrc: ["'self'", 'https:'],
    fontSrc: ["'self'", 'data:', 'https:'],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"],
  },

  // Session security
  session: {
    secure: true,
    httpOnly: true,
    sameSite: 'strict',
  },

  // Browser window security
  window: {
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      enableRemoteModule: false,
      spellcheck: true,
    },
  },

  // Network security
  network: {
    maxRedirects: 5,
    timeout: 30000,
    validateSSL: true,
  },

  // File system security
  fileSystem: {
    allowedPaths: [app.getPath('userData')],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedExtensions: ['.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx'],
  },

  // Password security
  password: {
    minLength: 12,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },

  // API security
  api: {
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
    cors: {
      origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    },
  },

  // Update security
  updates: {
    autoDownload: true,
    autoInstall: false,
    checkInterval: 24 * 60 * 60 * 1000, // 24 hours
    requireCodeSigning: true,
  },

  // Logging security
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    sanitize: true,
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
  },
};

// Apply security settings
export const applySecuritySettings = () => {
  // Set CSP
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          Object.entries(securityConfig.csp)
            .map(([key, value]) => `${key} ${value.join(' ')}`)
            .join('; '),
        ],
      },
    });
  });

  // Set secure headers
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'X-Content-Type-Options': ['nosniff'],
        'X-Frame-Options': ['DENY'],
        'X-XSS-Protection': ['1; mode=block'],
        'Strict-Transport-Security': ['max-age=31536000; includeSubDomains'],
        'Referrer-Policy': ['strict-origin-when-cross-origin'],
        'Permissions-Policy': [
          'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()',
        ],
      },
    });
  });
};
