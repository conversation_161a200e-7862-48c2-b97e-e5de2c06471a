/**
 * Продвинутая система мониторинга и аналитики
 * с real-time метриками, алертами и дашбордами
 */

export interface MonitoringConfig {
  enableRealTimeMetrics: boolean;
  enableErrorTracking: boolean;
  enablePerformanceMonitoring: boolean;
  enableUserAnalytics: boolean;
  enableBusinessMetrics: boolean;
  enableAlerts: boolean;
  enableDashboards: boolean;
  retentionPeriod: number; // в днях
  samplingRate: number; // 0-1
  alertThresholds: AlertThresholds;
}

export interface AlertThresholds {
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  userSatisfaction: number;
}

export interface Metric {
  id: string;
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  value: number;
  timestamp: Date;
  tags: Record<string, string>;
  unit?: string;
  description?: string;
}

export interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'critical';
  title: string;
  message: string;
  metric: string;
  threshold: number;
  currentValue: number;
  timestamp: Date;
  acknowledged: boolean;
  resolved: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface Dashboard {
  id: string;
  name: string;
  description: string;
  widgets: DashboardWidget[];
  layout: DashboardLayout;
  refreshInterval: number;
  filters: DashboardFilter[];
}

export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'heatmap' | 'gauge' | 'text';
  title: string;
  query: MetricQuery;
  visualization: VisualizationConfig;
  position: { x: number; y: number; width: number; height: number };
}

export interface MetricQuery {
  metric: string;
  aggregation: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'rate';
  timeRange: string;
  filters: Record<string, string>;
  groupBy?: string[];
}

export interface VisualizationConfig {
  chartType: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'heatmap';
  colors: string[];
  showLegend: boolean;
  showGrid: boolean;
  yAxisLabel?: string;
  xAxisLabel?: string;
}

export interface DashboardLayout {
  columns: number;
  rows: number;
  gap: number;
}

export interface DashboardFilter {
  name: string;
  type: 'select' | 'multiselect' | 'daterange' | 'text';
  options?: string[];
  defaultValue?: any;
}

export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  availability: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  networkLatency: number;
}

export interface UserAnalytics {
  activeUsers: number;
  sessionDuration: number;
  pageViews: number;
  bounceRate: number;
  conversionRate: number;
  userSatisfaction: number;
  featureUsage: Record<string, number>;
  userJourney: UserJourneyStep[];
}

export interface UserJourneyStep {
  step: string;
  timestamp: Date;
  duration: number;
  success: boolean;
  metadata?: Record<string, any>;
}

export interface BusinessMetrics {
  revenue: number;
  activeSubscriptions: number;
  churnRate: number;
  customerLifetimeValue: number;
  acquisitionCost: number;
  monthlyRecurringRevenue: number;
  dailyActiveUsers: number;
  monthlyActiveUsers: number;
}

export class MonitoringSystem {
  private config: MonitoringConfig;
  private metrics = new Map<string, Metric[]>();
  private alerts: Alert[] = [];
  private dashboards = new Map<string, Dashboard>();
  private collectors = new Map<string, MetricCollector>();
  private alertRules = new Map<string, AlertRule>();
  private subscribers = new Map<string, ((data: any) => void)[]>();

  constructor(config: Partial<MonitoringConfig> = {}) {
    this.config = {
      enableRealTimeMetrics: true,
      enableErrorTracking: true,
      enablePerformanceMonitoring: true,
      enableUserAnalytics: true,
      enableBusinessMetrics: true,
      enableAlerts: true,
      enableDashboards: true,
      retentionPeriod: 30,
      samplingRate: 1.0,
      alertThresholds: {
        errorRate: 5, // 5%
        responseTime: 2000, // 2 seconds
        memoryUsage: 80, // 80%
        cpuUsage: 80, // 80%
        diskUsage: 90, // 90%
        userSatisfaction: 3.0, // out of 5
      },
      ...config,
    };

    this.initialize();
  }

  /**
   * Инициализация системы мониторинга
   */
  private initialize(): void {
    console.log('📊 Initializing Monitoring System...');

    this.setupMetricCollectors();
    this.setupAlertRules();
    this.setupDefaultDashboards();
    this.startRealTimeMonitoring();

    console.log('✅ Monitoring System initialized');
  }

  /**
   * Настройка сборщиков метрик
   */
  private setupMetricCollectors(): void {
    // Performance Collector
    this.collectors.set('performance', new PerformanceCollector());

    // Error Collector
    this.collectors.set('errors', new ErrorCollector());

    // User Analytics Collector
    this.collectors.set('analytics', new AnalyticsCollector());

    // Business Metrics Collector
    this.collectors.set('business', new BusinessCollector());

    // Запускаем сборщики
    this.collectors.forEach((collector, name) => {
      collector.start(metric => this.recordMetric(metric));
      console.log(`📈 Started ${name} collector`);
    });
  }

  /**
   * Записывает метрику
   */
  recordMetric(metric: Metric): void {
    // Применяем sampling
    if (Math.random() > this.config.samplingRate) {
      return;
    }

    const metricName = metric.name;
    if (!this.metrics.has(metricName)) {
      this.metrics.set(metricName, []);
    }

    const metricHistory = this.metrics.get(metricName)!;
    metricHistory.push(metric);

    // Ограничиваем историю метрик
    const maxEntries = this.config.retentionPeriod * 24 * 60; // Одна запись в минуту
    if (metricHistory.length > maxEntries) {
      metricHistory.splice(0, metricHistory.length - maxEntries);
    }

    // Проверяем алерты
    this.checkAlerts(metric);

    // Уведомляем подписчиков
    this.notifySubscribers('metric', metric);
  }

  /**
   * Настройка правил алертов
   */
  private setupAlertRules(): void {
    // Error Rate Alert
    this.alertRules.set('error_rate', {
      metric: 'error_rate',
      condition: 'greater_than',
      threshold: this.config.alertThresholds.errorRate,
      severity: 'high',
      message: 'Error rate is above threshold',
    });

    // Response Time Alert
    this.alertRules.set('response_time', {
      metric: 'response_time',
      condition: 'greater_than',
      threshold: this.config.alertThresholds.responseTime,
      severity: 'medium',
      message: 'Response time is above threshold',
    });

    // Memory Usage Alert
    this.alertRules.set('memory_usage', {
      metric: 'memory_usage',
      condition: 'greater_than',
      threshold: this.config.alertThresholds.memoryUsage,
      severity: 'high',
      message: 'Memory usage is above threshold',
    });
  }

  /**
   * Проверяет алерты для метрики
   */
  private checkAlerts(metric: Metric): void {
    if (!this.config.enableAlerts) return;

    const rule = this.alertRules.get(metric.name);
    if (!rule) return;

    const shouldAlert = this.evaluateAlertCondition(metric.value, rule.condition, rule.threshold);

    if (shouldAlert) {
      const alert: Alert = {
        id: this.generateAlertId(),
        type: this.mapSeverityToType(rule.severity),
        title: `${metric.name} Alert`,
        message: rule.message,
        metric: metric.name,
        threshold: rule.threshold,
        currentValue: metric.value,
        timestamp: new Date(),
        acknowledged: false,
        resolved: false,
        severity: rule.severity,
      };

      this.alerts.push(alert);
      this.notifySubscribers('alert', alert);

      console.warn(`🚨 Alert triggered: ${alert.title} - ${alert.message}`);
    }
  }

  /**
   * Создает дашборд
   */
  createDashboard(dashboard: Dashboard): void {
    this.dashboards.set(dashboard.id, dashboard);
    console.log(`📊 Created dashboard: ${dashboard.name}`);
  }

  /**
   * Получает данные для дашборда
   */
  getDashboardData(dashboardId: string): any {
    const dashboard = this.dashboards.get(dashboardId);
    if (!dashboard) {
      throw new Error(`Dashboard ${dashboardId} not found`);
    }

    const data: any = {
      dashboard,
      widgets: {},
    };

    dashboard.widgets.forEach(widget => {
      data.widgets[widget.id] = this.executeQuery(widget.query);
    });

    return data;
  }

  /**
   * Выполняет запрос метрик
   */
  private executeQuery(query: MetricQuery): any {
    const metricHistory = this.metrics.get(query.metric) || [];

    // Фильтруем по времени
    const timeRange = this.parseTimeRange(query.timeRange);
    const filteredMetrics = metricHistory.filter(
      metric => metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
    );

    // Применяем фильтры
    const filtered = filteredMetrics.filter(metric => {
      return Object.entries(query.filters).every(([key, value]) => metric.tags[key] === value);
    });

    // Группируем данные
    if (query.groupBy && query.groupBy.length > 0) {
      return this.groupMetrics(filtered, query.groupBy, query.aggregation);
    }

    // Агрегируем данные
    return this.aggregateMetrics(filtered, query.aggregation);
  }

  /**
   * Группирует метрики
   */
  private groupMetrics(metrics: Metric[], groupBy: string[], aggregation: string): any {
    const groups = new Map<string, Metric[]>();

    metrics.forEach(metric => {
      const groupKey = groupBy.map(field => metric.tags[field] || 'unknown').join(':');
      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }
      groups.get(groupKey)!.push(metric);
    });

    const result: any = {};
    groups.forEach((groupMetrics, groupKey) => {
      result[groupKey] = this.aggregateMetrics(groupMetrics, aggregation);
    });

    return result;
  }

  /**
   * Агрегирует метрики
   */
  private aggregateMetrics(metrics: Metric[], aggregation: string): number {
    if (metrics.length === 0) return 0;

    const values = metrics.map(m => m.value);

    switch (aggregation) {
      case 'sum':
        return values.reduce((sum, val) => sum + val, 0);
      case 'avg':
        return values.reduce((sum, val) => sum + val, 0) / values.length;
      case 'min':
        return Math.min(...values);
      case 'max':
        return Math.max(...values);
      case 'count':
        return values.length;
      case 'rate':
        // Вычисляем скорость изменения
        if (values.length < 2) return 0;
        const timeSpan =
          metrics[metrics.length - 1].timestamp.getTime() - metrics[0].timestamp.getTime();
        const valueChange = values[values.length - 1] - values[0];
        return (valueChange / timeSpan) * 1000; // per second
      default:
        return 0;
    }
  }

  /**
   * Настройка дашбордов по умолчанию
   */
  private setupDefaultDashboards(): void {
    // Performance Dashboard
    const performanceDashboard: Dashboard = {
      id: 'performance',
      name: 'Performance Overview',
      description: 'System performance metrics',
      refreshInterval: 30000,
      layout: { columns: 12, rows: 8, gap: 16 },
      filters: [
        {
          name: 'timeRange',
          type: 'select',
          options: ['1h', '6h', '24h', '7d'],
          defaultValue: '1h',
        },
      ],
      widgets: [
        {
          id: 'response-time',
          type: 'chart',
          title: 'Response Time',
          position: { x: 0, y: 0, width: 6, height: 4 },
          query: {
            metric: 'response_time',
            aggregation: 'avg',
            timeRange: '1h',
            filters: {},
          },
          visualization: {
            chartType: 'line',
            colors: ['#3b82f6'],
            showLegend: true,
            showGrid: true,
            yAxisLabel: 'Time (ms)',
          },
        },
        {
          id: 'error-rate',
          type: 'gauge',
          title: 'Error Rate',
          position: { x: 6, y: 0, width: 3, height: 4 },
          query: {
            metric: 'error_rate',
            aggregation: 'avg',
            timeRange: '1h',
            filters: {},
          },
          visualization: {
            chartType: 'gauge',
            colors: ['#10b981', '#f59e0b', '#ef4444'],
            showLegend: false,
            showGrid: false,
          },
        },
      ],
    };

    this.createDashboard(performanceDashboard);

    // User Analytics Dashboard
    const analyticsDashboard: Dashboard = {
      id: 'analytics',
      name: 'User Analytics',
      description: 'User behavior and engagement metrics',
      refreshInterval: 60000,
      layout: { columns: 12, rows: 8, gap: 16 },
      filters: [],
      widgets: [
        {
          id: 'active-users',
          type: 'metric',
          title: 'Active Users',
          position: { x: 0, y: 0, width: 3, height: 2 },
          query: {
            metric: 'active_users',
            aggregation: 'sum',
            timeRange: '24h',
            filters: {},
          },
          visualization: {
            chartType: 'line',
            colors: ['#8b5cf6'],
            showLegend: false,
            showGrid: false,
          },
        },
      ],
    };

    this.createDashboard(analyticsDashboard);
  }

  /**
   * Запускает real-time мониторинг
   */
  private startRealTimeMonitoring(): void {
    if (!this.config.enableRealTimeMetrics) return;

    // Отправляем метрики каждые 10 секунд
    setInterval(() => {
      this.collectSystemMetrics();
    }, 10000);

    // Очищаем старые алерты каждый час
    setInterval(() => {
      this.cleanupOldAlerts();
    }, 3600000);
  }

  /**
   * Собирает системные метрики
   */
  private collectSystemMetrics(): void {
    // Memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.recordMetric({
        id: this.generateMetricId(),
        name: 'memory_usage',
        type: 'gauge',
        value: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        timestamp: new Date(),
        tags: { source: 'system' },
        unit: 'percent',
      });
    }

    // Performance timing
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      this.recordMetric({
        id: this.generateMetricId(),
        name: 'page_load_time',
        type: 'histogram',
        value: navigation.loadEventEnd - navigation.fetchStart,
        timestamp: new Date(),
        tags: { source: 'navigation' },
        unit: 'milliseconds',
      });
    }
  }

  /**
   * Подписывается на события
   */
  subscribe(event: string, callback: (data: any) => void): void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, []);
    }
    this.subscribers.get(event)!.push(callback);
  }

  /**
   * Уведомляет подписчиков
   */
  private notifySubscribers(event: string, data: any): void {
    const callbacks = this.subscribers.get(event) || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in monitoring subscriber:', error);
      }
    });
  }

  /**
   * Получает метрики
   */
  getMetrics(metricName?: string, timeRange?: string): Metric[] {
    if (metricName) {
      return this.metrics.get(metricName) || [];
    }

    // Возвращаем все метрики
    const allMetrics: Metric[] = [];
    this.metrics.forEach(metrics => {
      allMetrics.push(...metrics);
    });

    return allMetrics;
  }

  /**
   * Получает алерты
   */
  getAlerts(unacknowledgedOnly = false): Alert[] {
    if (unacknowledgedOnly) {
      return this.alerts.filter(alert => !alert.acknowledged);
    }
    return [...this.alerts];
  }

  /**
   * Подтверждает алерт
   */
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.notifySubscribers('alert_acknowledged', alert);
    }
  }

  /**
   * Разрешает алерт
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.notifySubscribers('alert_resolved', alert);
    }
  }

  // Вспомогательные методы

  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private parseTimeRange(timeRange: string): { start: Date; end: Date } {
    const end = new Date();
    const start = new Date();

    const match = timeRange.match(/^(\d+)([smhd])$/);
    if (!match) {
      start.setHours(start.getHours() - 1); // Default to 1 hour
      return { start, end };
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's':
        start.setSeconds(start.getSeconds() - value);
        break;
      case 'm':
        start.setMinutes(start.getMinutes() - value);
        break;
      case 'h':
        start.setHours(start.getHours() - value);
        break;
      case 'd':
        start.setDate(start.getDate() - value);
        break;
    }

    return { start, end };
  }

  private evaluateAlertCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'greater_than':
        return value > threshold;
      case 'less_than':
        return value < threshold;
      case 'equals':
        return value === threshold;
      default:
        return false;
    }
  }

  private mapSeverityToType(severity: string): Alert['type'] {
    switch (severity) {
      case 'critical':
        return 'critical';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'info';
    }
  }

  private cleanupOldAlerts(): void {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - 7); // Удаляем алерты старше 7 дней

    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoff);
  }
}

// Интерфейсы для сборщиков метрик
interface MetricCollector {
  start(callback: (metric: Metric) => void): void;
  stop(): void;
}

interface AlertRule {
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals';
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
}

// Реализации сборщиков метрик
class PerformanceCollector implements MetricCollector {
  private interval?: NodeJS.Timeout;

  start(callback: (metric: Metric) => void): void {
    this.interval = setInterval(() => {
      // Собираем метрики производительности
      const entries = performance.getEntriesByType('measure');
      entries.forEach(entry => {
        callback({
          id: `perf_${Date.now()}`,
          name: 'performance_measure',
          type: 'histogram',
          value: entry.duration,
          timestamp: new Date(),
          tags: { name: entry.name },
          unit: 'milliseconds',
        });
      });
    }, 5000);
  }

  stop(): void {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }
}

class ErrorCollector implements MetricCollector {
  private callback?: (metric: Metric) => void;

  start(callback: (metric: Metric) => void): void {
    this.callback = callback;

    window.addEventListener('error', this.handleError.bind(this));
    window.addEventListener('unhandledrejection', this.handleRejection.bind(this));
  }

  stop(): void {
    window.removeEventListener('error', this.handleError.bind(this));
    window.removeEventListener('unhandledrejection', this.handleRejection.bind(this));
  }

  private handleError(event: ErrorEvent): void {
    if (this.callback) {
      this.callback({
        id: `error_${Date.now()}`,
        name: 'javascript_error',
        type: 'counter',
        value: 1,
        timestamp: new Date(),
        tags: {
          message: event.message,
          filename: event.filename || 'unknown',
          line: event.lineno?.toString() || 'unknown',
        },
      });
    }
  }

  private handleRejection(event: PromiseRejectionEvent): void {
    if (this.callback) {
      this.callback({
        id: `rejection_${Date.now()}`,
        name: 'promise_rejection',
        type: 'counter',
        value: 1,
        timestamp: new Date(),
        tags: {
          reason: String(event.reason),
        },
      });
    }
  }
}

class AnalyticsCollector implements MetricCollector {
  private interval?: NodeJS.Timeout;

  start(callback: (metric: Metric) => void): void {
    this.interval = setInterval(() => {
      // Собираем аналитические метрики
      callback({
        id: `analytics_${Date.now()}`,
        name: 'page_view',
        type: 'counter',
        value: 1,
        timestamp: new Date(),
        tags: {
          page: window.location.pathname,
          referrer: document.referrer || 'direct',
        },
      });
    }, 30000);
  }

  stop(): void {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }
}

class BusinessCollector implements MetricCollector {
  start(callback: (metric: Metric) => void): void {
    // Бизнес-метрики собираются по событиям
    // Здесь можно подписаться на кастомные события приложения
  }

  stop(): void {
    // Отписываемся от событий
  }
}

// Глобальный экземпляр
export const monitoringSystem = new MonitoringSystem();
