import { ErrorManager } from '../../error/ErrorManager';
import { NetworkManager } from '../../network/NetworkManager';
import { PerformanceMonitor } from '../../performance/PerformanceMonitor';
import { StorageManager } from '../StorageManager';

jest.mock('../../network/NetworkManager');
jest.mock('../../error/ErrorManager');
jest.mock('../../performance/PerformanceMonitor');

describe('StorageManager', () => {
  let storageManager: StorageManager;
  let mockNetworkManager: jest.Mocked<NetworkManager>;
  let mockErrorManager: jest.Mocked<ErrorManager>;
  let mockPerformanceMonitor: jest.Mocked<PerformanceMonitor>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Get singleton instance
    storageManager = StorageManager.getInstance();

    // Get mocked instances
    mockNetworkManager = NetworkManager.getInstance() as jest.Mocked<NetworkManager>;
    mockErrorManager = ErrorManager.getInstance() as jest.Mocked<ErrorManager>;
    mockPerformanceMonitor = PerformanceMonitor.getInstance() as jest.Mocked<PerformanceMonitor>;

    // Reset localStorage
    localStorage.clear();

    // Reset IndexedDB
    const request = indexedDB.deleteDatabase('NovaBrowserStorage');
    request.onsuccess = () => {
      // Database deleted
    };
  });

  describe('Initialization', () => {
    test('should create singleton instance', () => {
      const instance1 = StorageManager.getInstance();
      const instance2 = StorageManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('should initialize with default config', () => {
      const config = storageManager.getConfig();
      expect(config).toEqual({
        type: 'indexedDB',
        encryption: true,
        compression: true,
        syncInterval: 300000,
        maxSize: 50 * 1024 * 1024,
        backupEnabled: true,
        backupInterval: 3600000,
        version: 1,
        prefix: 'novabrowser_',
      });
    });
  });

  describe('Configuration', () => {
    test('should update config', () => {
      const newConfig = {
        type: 'localStorage',
        encryption: false,
      };

      storageManager.setConfig(newConfig);
      const config = storageManager.getConfig();

      expect(config.type).toBe(newConfig.type);
      expect(config.encryption).toBe(newConfig.encryption);
    });

    test('should emit config update event', () => {
      const listener = jest.fn();
      storageManager.on('configUpdated', listener);

      storageManager.setConfig({ type: 'localStorage' });
      expect(listener).toHaveBeenCalled();
    });
  });

  describe('Storage Operations', () => {
    const testKey = 'test-key';
    const testValue = { test: 'value' };

    test('should store and retrieve data', async () => {
      await storageManager.set(testKey, testValue);
      const retrievedValue = await storageManager.get(testKey);
      expect(retrievedValue).toEqual(testValue);
    });

    test('should remove data', async () => {
      await storageManager.set(testKey, testValue);
      await storageManager.remove(testKey);
      const retrievedValue = await storageManager.get(testKey);
      expect(retrievedValue).toBeNull();
    });

    test('should clear all data', async () => {
      await storageManager.set('key1', 'value1');
      await storageManager.set('key2', 'value2');
      await storageManager.clear();

      const value1 = await storageManager.get('key1');
      const value2 = await storageManager.get('key2');

      expect(value1).toBeNull();
      expect(value2).toBeNull();
    });
  });

  describe('Storage Types', () => {
    const testKey = 'test-key';
    const testValue = { test: 'value' };

    test('should work with localStorage', async () => {
      storageManager.setConfig({ type: 'localStorage' });

      await storageManager.set(testKey, testValue);
      const retrievedValue = await storageManager.get(testKey);

      expect(retrievedValue).toEqual(testValue);
      expect(localStorage.getItem(`novabrowser_${testKey}`)).toBeTruthy();
    });

    test('should work with IndexedDB', async () => {
      storageManager.setConfig({ type: 'indexedDB' });

      await storageManager.set(testKey, testValue);
      const retrievedValue = await storageManager.get(testKey);

      expect(retrievedValue).toEqual(testValue);
    });

    test('should work with memory storage', async () => {
      storageManager.setConfig({ type: 'memory' });

      await storageManager.set(testKey, testValue);
      const retrievedValue = await storageManager.get(testKey);

      expect(retrievedValue).toEqual(testValue);
    });
  });

  describe('Data Protection', () => {
    const testKey = 'test-key';
    const testValue = { test: 'value' };

    test('should encrypt data when enabled', async () => {
      storageManager.setConfig({ encryption: true });

      await storageManager.set(testKey, testValue);
      const rawValue = localStorage.getItem(`novabrowser_${testKey}`);

      expect(rawValue).not.toBe(JSON.stringify(testValue));
    });

    test('should not encrypt data when disabled', async () => {
      storageManager.setConfig({ encryption: false });

      await storageManager.set(testKey, testValue);
      const rawValue = localStorage.getItem(`novabrowser_${testKey}`);

      expect(rawValue).toBe(JSON.stringify(testValue));
    });

    test('should compress data when enabled', async () => {
      storageManager.setConfig({ compression: true });

      const largeValue = 'x'.repeat(1000);
      await storageManager.set(testKey, largeValue);
      const rawValue = localStorage.getItem(`novabrowser_${testKey}`);

      expect(rawValue.length).toBeLessThan(largeValue.length);
    });
  });

  describe('Event Handling', () => {
    test('should emit events for operations', async () => {
      const setListener = jest.fn();
      const getListener = jest.fn();
      const removeListener = jest.fn();
      const clearListener = jest.fn();

      storageManager.on('itemSet', setListener);
      storageManager.on('itemGet', getListener);
      storageManager.on('itemRemoved', removeListener);
      storageManager.on('cleared', clearListener);

      await storageManager.set('test-key', 'value');
      await storageManager.get('test-key');
      await storageManager.remove('test-key');
      await storageManager.clear();

      expect(setListener).toHaveBeenCalled();
      expect(getListener).toHaveBeenCalled();
      expect(removeListener).toHaveBeenCalled();
      expect(clearListener).toHaveBeenCalled();
    });

    test('should handle event unsubscription', () => {
      const listener = jest.fn();
      storageManager.on('configUpdated', listener);
      storageManager.off('configUpdated', listener);

      storageManager.setConfig({ type: 'localStorage' });
      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    test('should handle storage errors', async () => {
      // Simulate storage error
      jest.spyOn(localStorage, 'setItem').mockImplementation(() => {
        throw new Error('Storage error');
      });

      storageManager.setConfig({ type: 'localStorage' });

      await expect(storageManager.set('test-key', 'value')).rejects.toThrow('Storage error');
      expect(mockErrorManager.handleError).toHaveBeenCalled();
    });

    test('should handle retrieval errors', async () => {
      // Simulate retrieval error
      jest.spyOn(localStorage, 'getItem').mockImplementation(() => {
        throw new Error('Retrieval error');
      });

      storageManager.setConfig({ type: 'localStorage' });

      await expect(storageManager.get('test-key')).rejects.toThrow('Retrieval error');
      expect(mockErrorManager.handleError).toHaveBeenCalled();
    });
  });

  describe('Performance Monitoring', () => {
    test('should track set operation performance', async () => {
      await storageManager.set('test-key', 'value');
      expect(mockPerformanceMonitor.trackMetric).toHaveBeenCalledWith(
        'storage_set_duration',
        expect.any(Number),
        expect.any(Object)
      );
    });

    test('should track get operation performance', async () => {
      await storageManager.set('test-key', 'value');
      await storageManager.get('test-key');
      expect(mockPerformanceMonitor.trackMetric).toHaveBeenCalledWith(
        'storage_get_duration',
        expect.any(Number),
        expect.any(Object)
      );
    });
  });

  describe('Cleanup', () => {
    test('should clear timers and listeners', () => {
      const listener = jest.fn();
      storageManager.on('configUpdated', listener);

      storageManager.cleanup();
      storageManager.setConfig({ type: 'localStorage' });

      expect(listener).not.toHaveBeenCalled();
    });
  });
});
