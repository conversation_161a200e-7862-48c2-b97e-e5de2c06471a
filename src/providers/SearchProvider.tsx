import React, { createContext, useContext, useEffect, useState } from 'react';

import { useStorage } from './StorageProvider';

interface SearchEngine {
  id: string;
  name: string;
  url: string;
  icon?: string;
  isDefault: boolean;
  isEnabled: boolean;
  order: number;
}

interface SearchHistory {
  id: string;
  query: string;
  engineId: string;
  timestamp: number;
  resultCount?: number;
}

interface SearchState {
  engines: SearchEngine[];
  history: SearchHistory[];
  recentQueries: string[];
  lastUpdated: number;
  totalSearches: number;
  defaultEngineId: string;
}

interface SearchContextType {
  state: SearchState;
  addEngine: (engine: Omit<SearchEngine, 'id' | 'order'>) => Promise<string>;
  updateEngine: (id: string, updates: Partial<SearchEngine>) => Promise<void>;
  removeEngine: (id: string) => Promise<void>;
  setDefaultEngine: (id: string) => Promise<void>;
  enableEngine: (id: string) => Promise<void>;
  disableEngine: (id: string) => Promise<void>;
  reorderEngines: (id: string, newIndex: number) => Promise<void>;
  addToHistory: (query: string, engineId: string, resultCount?: number) => Promise<void>;
  removeFromHistory: (id: string) => Promise<void>;
  clearHistory: () => Promise<void>;
  getEngine: (id: string) => SearchEngine | undefined;
  getDefaultEngine: () => SearchEngine | undefined;
  getEnabledEngines: () => SearchEngine[];
  getRecentQueries: (limit?: number) => string[];
  performSearch: (query: string, engineId?: string) => Promise<void>;
  getSearchUrl: (query: string, engineId?: string) => string;
}

const defaultSearchState: SearchState = {
  engines: [
    {
      id: 'google',
      name: 'Google',
      url: 'https://www.google.com/search?q={query}',
      icon: 'https://www.google.com/favicon.ico',
      isDefault: true,
      isEnabled: true,
      order: 0,
    },
    {
      id: 'bing',
      name: 'Bing',
      url: 'https://www.bing.com/search?q={query}',
      icon: 'https://www.bing.com/favicon.ico',
      isDefault: false,
      isEnabled: true,
      order: 1,
    },
    {
      id: 'duckduckgo',
      name: 'DuckDuckGo',
      url: 'https://duckduckgo.com/?q={query}',
      icon: 'https://duckduckgo.com/favicon.ico',
      isDefault: false,
      isEnabled: true,
      order: 2,
    },
  ],
  history: [],
  recentQueries: [],
  lastUpdated: Date.now(),
  totalSearches: 0,
  defaultEngineId: 'google',
};

const SearchContext = createContext<SearchContextType | null>(null);

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

interface SearchProviderProps {
  children: React.ReactNode;
}

export const SearchProvider: React.FC<SearchProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [state, setState] = useState<SearchState>(defaultSearchState);

  useEffect(() => {
    const loadSearch = async () => {
      try {
        const savedSearch = await storage.getItem('browserSearch');
        if (savedSearch) {
          setState(JSON.parse(savedSearch));
        }
      } catch (error) {
        console.error('Error loading search settings:', error);
      }
    };

    loadSearch();
  }, [storage]);

  const saveSearch = async (newState: SearchState) => {
    try {
      await storage.setItem('browserSearch', JSON.stringify(newState));
      setState(newState);
    } catch (error) {
      console.error('Error saving search settings:', error);
    }
  };

  const addEngine = async (engine: Omit<SearchEngine, 'id' | 'order'>): Promise<string> => {
    const id = `engine_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const newEngine: SearchEngine = {
      id,
      ...engine,
      order: state.engines.length,
    };

    const newState = {
      ...state,
      engines: [...state.engines, newEngine],
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
    return id;
  };

  const updateEngine = async (id: string, updates: Partial<SearchEngine>) => {
    const newState = {
      ...state,
      engines: state.engines.map(engine => (engine.id === id ? { ...engine, ...updates } : engine)),
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
  };

  const removeEngine = async (id: string) => {
    const engine = state.engines.find(e => e.id === id);
    if (!engine) return;

    const newState = {
      ...state,
      engines: state.engines.filter(e => e.id !== id),
      defaultEngineId: engine.isDefault
        ? state.engines.find(e => e.id !== id)?.id || 'google'
        : state.defaultEngineId,
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
  };

  const setDefaultEngine = async (id: string) => {
    const newState = {
      ...state,
      engines: state.engines.map(engine => ({
        ...engine,
        isDefault: engine.id === id,
      })),
      defaultEngineId: id,
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
  };

  const enableEngine = async (id: string) => {
    await updateEngine(id, { isEnabled: true });
  };

  const disableEngine = async (id: string) => {
    const engine = state.engines.find(e => e.id === id);
    if (!engine || engine.isDefault) return;

    await updateEngine(id, { isEnabled: false });
  };

  const reorderEngines = async (id: string, newIndex: number) => {
    const engine = state.engines.find(e => e.id === id);
    if (!engine) return;

    const oldIndex = state.engines.indexOf(engine);
    const newState = {
      ...state,
      engines: state.engines
        .map((e, index) => {
          if (index === oldIndex) return { ...e, order: newIndex };
          if (index >= newIndex && index < oldIndex) return { ...e, order: e.order + 1 };
          if (index <= newIndex && index > oldIndex) return { ...e, order: e.order - 1 };
          return e;
        })
        .sort((a, b) => a.order - b.order),
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
  };

  const addToHistory = async (query: string, engineId: string, resultCount?: number) => {
    const now = Date.now();
    const id = `search_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const newHistory: SearchHistory = {
      id,
      query,
      engineId,
      timestamp: now,
      resultCount,
    };

    const newState = {
      ...state,
      history: [newHistory, ...state.history].slice(0, 100),
      recentQueries: [query, ...state.recentQueries.filter(q => q !== query)].slice(0, 10),
      totalSearches: state.totalSearches + 1,
      lastUpdated: now,
    };

    await saveSearch(newState);
  };

  const removeFromHistory = async (id: string) => {
    const newState = {
      ...state,
      history: state.history.filter(h => h.id !== id),
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
  };

  const clearHistory = async () => {
    const newState = {
      ...state,
      history: [],
      recentQueries: [],
      lastUpdated: Date.now(),
    };

    await saveSearch(newState);
  };

  const getEngine = (id: string): SearchEngine | undefined => {
    return state.engines.find(engine => engine.id === id);
  };

  const getDefaultEngine = (): SearchEngine | undefined => {
    return state.engines.find(engine => engine.id === state.defaultEngineId);
  };

  const getEnabledEngines = (): SearchEngine[] => {
    return state.engines.filter(engine => engine.isEnabled);
  };

  const getRecentQueries = (limit: number = 10): string[] => {
    return state.recentQueries.slice(0, limit);
  };

  const performSearch = async (query: string, engineId?: string) => {
    const engine = engineId ? getEngine(engineId) : getDefaultEngine();
    if (!engine) return;

    await addToHistory(query, engine.id);
    const searchUrl = getSearchUrl(query, engine.id);
    window.location.href = searchUrl;
  };

  const getSearchUrl = (query: string, engineId?: string): string => {
    const engine = engineId ? getEngine(engineId) : getDefaultEngine();
    if (!engine) return '';

    return engine.url.replace('{query}', encodeURIComponent(query));
  };

  const value = {
    state,
    addEngine,
    updateEngine,
    removeEngine,
    setDefaultEngine,
    enableEngine,
    disableEngine,
    reorderEngines,
    addToHistory,
    removeFromHistory,
    clearHistory,
    getEngine,
    getDefaultEngine,
    getEnabledEngines,
    getRecentQueries,
    performSearch,
    getSearchUrl,
  };

  return <SearchContext.Provider value={value}>{children}</SearchContext.Provider>;
};
