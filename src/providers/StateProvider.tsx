import React, { createContext, useContext, useEffect, useState } from 'react';

import { useStorage } from './StorageProvider';

interface StateContextType {
  state: Record<string, any>;
  setState: (key: string, value: any) => Promise<void>;
  getState: (key: string) => any;
  removeState: (key: string) => Promise<void>;
  clearState: () => Promise<void>;
  subscribe: (key: string, callback: (value: any) => void) => () => void;
}

const StateContext = createContext<StateContextType | null>(null);

export const useAppState = () => {
  const context = useContext(StateContext);
  if (!context) {
    throw new Error('useAppState must be used within a StateProvider');
  }
  return context;
};

interface StateProviderProps {
  children: React.ReactNode;
}

export const StateProvider: React.FC<StateProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [state, setStateValue] = useState<Record<string, any>>({});
  const [subscribers, setSubscribers] = useState<Record<string, Set<(value: any) => void>>>({});

  useEffect(() => {
    const loadState = async () => {
      try {
        const keys = await storage.getAllKeys();
        const loadedState: Record<string, any> = {};

        for (const key of keys) {
          if (key.startsWith('state:')) {
            const value = await storage.getItem(key);
            if (value) {
              try {
                loadedState[key.slice(6)] = JSON.parse(value);
              } catch (error) {
                console.error(`Error parsing state for key ${key}:`, error);
              }
            }
          }
        }

        setStateValue(loadedState);
      } catch (error) {
        console.error('Error loading state:', error);
      }
    };

    loadState();
  }, [storage]);

  const setState = async (key: string, value: any) => {
    try {
      const newState = { ...state, [key]: value };
      setStateValue(newState);
      await storage.setItem(`state:${key}`, JSON.stringify(value));

      // Notify subscribers
      if (subscribers[key]) {
        subscribers[key].forEach(callback => callback(value));
      }
    } catch (error) {
      console.error(`Error setting state for key ${key}:`, error);
    }
  };

  const getState = (key: string) => {
    return state[key];
  };

  const removeState = async (key: string) => {
    try {
      const newState = { ...state };
      delete newState[key];
      setStateValue(newState);
      await storage.removeItem(`state:${key}`);

      // Notify subscribers
      if (subscribers[key]) {
        subscribers[key].forEach(callback => callback(undefined));
      }
    } catch (error) {
      console.error(`Error removing state for key ${key}:`, error);
    }
  };

  const clearState = async () => {
    try {
      setStateValue({});
      const keys = await storage.getAllKeys();
      await Promise.all(
        keys.filter(key => key.startsWith('state:')).map(key => storage.removeItem(key))
      );

      // Notify all subscribers
      Object.values(subscribers).forEach(callbacks => {
        callbacks.forEach(callback => callback(undefined));
      });
    } catch (error) {
      console.error('Error clearing state:', error);
    }
  };

  const subscribe = (key: string, callback: (value: any) => void) => {
    setSubscribers(prev => {
      const newSubscribers = { ...prev };
      if (!newSubscribers[key]) {
        newSubscribers[key] = new Set();
      }
      newSubscribers[key].add(callback);
      return newSubscribers;
    });

    return () => {
      setSubscribers(prev => {
        const newSubscribers = { ...prev };
        if (newSubscribers[key]) {
          newSubscribers[key].delete(callback);
          if (newSubscribers[key].size === 0) {
            delete newSubscribers[key];
          }
        }
        return newSubscribers;
      });
    };
  };

  const value = {
    state,
    setState,
    getState,
    removeState,
    clearState,
    subscribe,
  };

  return <StateContext.Provider value={value}>{children}</StateContext.Provider>;
};
