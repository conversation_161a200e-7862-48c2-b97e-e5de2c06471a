import React, { createContext, useContext, useEffect, useState } from 'react';

import { useStorage } from './StorageProvider';

interface Bookmark {
  id: string;
  url: string;
  title: string;
  description?: string;
  favicon?: string;
  tags: string[];
  folderId: string | null;
  createdAt: number;
  updatedAt: number;
  lastVisited?: number;
  visitCount: number;
}

interface BookmarkFolder {
  id: string;
  name: string;
  description?: string;
  parentId: string | null;
  createdAt: number;
  updatedAt: number;
  order: number;
}

interface BookmarksState {
  bookmarks: Bookmark[];
  folders: BookmarkFolder[];
  lastUpdated: number;
  totalBookmarks: number;
  totalFolders: number;
}

interface BookmarksContextType {
  bookmarks: BookmarksState;
  addBookmark: (
    bookmark: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'visitCount'>
  ) => Promise<string>;
  updateBookmark: (id: string, updates: Partial<Bookmark>) => Promise<void>;
  removeBookmark: (id: string) => Promise<void>;
  addFolder: (
    folder: Omit<BookmarkFolder, 'id' | 'createdAt' | 'updatedAt' | 'order'>
  ) => Promise<string>;
  updateFolder: (id: string, updates: Partial<BookmarkFolder>) => Promise<void>;
  removeFolder: (id: string) => Promise<void>;
  moveBookmark: (bookmarkId: string, folderId: string | null) => Promise<void>;
  moveFolder: (folderId: string, parentId: string | null) => Promise<void>;
  reorderFolder: (folderId: string, newOrder: number) => Promise<void>;
  getBookmark: (id: string) => Bookmark | undefined;
  getFolder: (id: string) => BookmarkFolder | undefined;
  getBookmarksInFolder: (folderId: string | null) => Bookmark[];
  getSubfolders: (parentId: string | null) => BookmarkFolder[];
  searchBookmarks: (query: string) => Bookmark[];
  getBookmarksByTag: (tag: string) => Bookmark[];
  getRecentBookmarks: (limit?: number) => Bookmark[];
  getMostVisitedBookmarks: (limit?: number) => Bookmark[];
  clearAll: () => Promise<void>;
}

const defaultBookmarksState: BookmarksState = {
  bookmarks: [],
  folders: [],
  lastUpdated: Date.now(),
  totalBookmarks: 0,
  totalFolders: 0,
};

const BookmarksContext = createContext<BookmarksContextType | null>(null);

export const useBookmarks = () => {
  const context = useContext(BookmarksContext);
  if (!context) {
    throw new Error('useBookmarks must be used within a BookmarksProvider');
  }
  return context;
};

interface BookmarksProviderProps {
  children: React.ReactNode;
}

export const BookmarksProvider: React.FC<BookmarksProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [bookmarks, setBookmarks] = useState<BookmarksState>(defaultBookmarksState);

  useEffect(() => {
    const loadBookmarks = async () => {
      try {
        const savedBookmarks = await storage.getItem('browserBookmarks');
        if (savedBookmarks) {
          setBookmarks(JSON.parse(savedBookmarks));
        }
      } catch (error) {
        console.error('Error loading bookmarks:', error);
      }
    };

    loadBookmarks();
  }, [storage]);

  const saveBookmarks = async (newBookmarks: BookmarksState) => {
    try {
      await storage.setItem('browserBookmarks', JSON.stringify(newBookmarks));
      setBookmarks(newBookmarks);
    } catch (error) {
      console.error('Error saving bookmarks:', error);
    }
  };

  const addBookmark = async (
    bookmark: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'visitCount'>
  ): Promise<string> => {
    const now = Date.now();
    const id = `bm_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const newBookmark: Bookmark = {
      ...bookmark,
      id,
      createdAt: now,
      updatedAt: now,
      visitCount: 0,
    };

    const newBookmarks = {
      ...bookmarks,
      bookmarks: [...bookmarks.bookmarks, newBookmark],
      totalBookmarks: bookmarks.totalBookmarks + 1,
      lastUpdated: now,
    };

    await saveBookmarks(newBookmarks);
    return id;
  };

  const updateBookmark = async (id: string, updates: Partial<Bookmark>) => {
    const newBookmarks = {
      ...bookmarks,
      bookmarks: bookmarks.bookmarks.map(bookmark =>
        bookmark.id === id ? { ...bookmark, ...updates, updatedAt: Date.now() } : bookmark
      ),
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const removeBookmark = async (id: string) => {
    const newBookmarks = {
      ...bookmarks,
      bookmarks: bookmarks.bookmarks.filter(bookmark => bookmark.id !== id),
      totalBookmarks: bookmarks.totalBookmarks - 1,
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const addFolder = async (
    folder: Omit<BookmarkFolder, 'id' | 'createdAt' | 'updatedAt' | 'order'>
  ): Promise<string> => {
    const now = Date.now();
    const id = `fld_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const newFolder: BookmarkFolder = {
      ...folder,
      id,
      createdAt: now,
      updatedAt: now,
      order: bookmarks.folders.length,
    };

    const newBookmarks = {
      ...bookmarks,
      folders: [...bookmarks.folders, newFolder],
      totalFolders: bookmarks.totalFolders + 1,
      lastUpdated: now,
    };

    await saveBookmarks(newBookmarks);
    return id;
  };

  const updateFolder = async (id: string, updates: Partial<BookmarkFolder>) => {
    const newBookmarks = {
      ...bookmarks,
      folders: bookmarks.folders.map(folder =>
        folder.id === id ? { ...folder, ...updates, updatedAt: Date.now() } : folder
      ),
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const removeFolder = async (id: string) => {
    // Move all bookmarks in this folder to the root level
    const newBookmarks = {
      ...bookmarks,
      bookmarks: bookmarks.bookmarks.map(bookmark =>
        bookmark.folderId === id ? { ...bookmark, folderId: null, updatedAt: Date.now() } : bookmark
      ),
      folders: bookmarks.folders.filter(folder => folder.id !== id),
      totalFolders: bookmarks.totalFolders - 1,
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const moveBookmark = async (bookmarkId: string, folderId: string | null) => {
    const newBookmarks = {
      ...bookmarks,
      bookmarks: bookmarks.bookmarks.map(bookmark =>
        bookmark.id === bookmarkId ? { ...bookmark, folderId, updatedAt: Date.now() } : bookmark
      ),
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const moveFolder = async (folderId: string, parentId: string | null) => {
    const newBookmarks = {
      ...bookmarks,
      folders: bookmarks.folders.map(folder =>
        folder.id === folderId ? { ...folder, parentId, updatedAt: Date.now() } : folder
      ),
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const reorderFolder = async (folderId: string, newOrder: number) => {
    const folder = bookmarks.folders.find(f => f.id === folderId);
    if (!folder) return;

    const oldOrder = folder.order;
    const newBookmarks = {
      ...bookmarks,
      folders: bookmarks.folders.map(f =>
        f.id === folderId
          ? { ...f, order: newOrder, updatedAt: Date.now() }
          : f.order >= newOrder && f.order < oldOrder
            ? { ...f, order: f.order + 1, updatedAt: Date.now() }
            : f.order <= newOrder && f.order > oldOrder
              ? { ...f, order: f.order - 1, updatedAt: Date.now() }
              : f
      ),
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const getBookmark = (id: string): Bookmark | undefined => {
    return bookmarks.bookmarks.find(bookmark => bookmark.id === id);
  };

  const getFolder = (id: string): BookmarkFolder | undefined => {
    return bookmarks.folders.find(folder => folder.id === id);
  };

  const getBookmarksInFolder = (folderId: string | null): Bookmark[] => {
    return bookmarks.bookmarks.filter(bookmark => bookmark.folderId === folderId);
  };

  const getSubfolders = (parentId: string | null): BookmarkFolder[] => {
    return bookmarks.folders
      .filter(folder => folder.parentId === parentId)
      .sort((a, b) => a.order - b.order);
  };

  const searchBookmarks = (query: string): Bookmark[] => {
    const searchTerm = query.toLowerCase();
    return bookmarks.bookmarks.filter(
      bookmark =>
        bookmark.title.toLowerCase().includes(searchTerm) ||
        bookmark.url.toLowerCase().includes(searchTerm) ||
        bookmark.description?.toLowerCase().includes(searchTerm) ||
        bookmark.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  };

  const getBookmarksByTag = (tag: string): Bookmark[] => {
    return bookmarks.bookmarks.filter(bookmark =>
      bookmark.tags.some(t => t.toLowerCase() === tag.toLowerCase())
    );
  };

  const getRecentBookmarks = (limit: number = 10): Bookmark[] => {
    return [...bookmarks.bookmarks]
      .sort((a, b) => (b.lastVisited || 0) - (a.lastVisited || 0))
      .slice(0, limit);
  };

  const getMostVisitedBookmarks = (limit: number = 10): Bookmark[] => {
    return [...bookmarks.bookmarks].sort((a, b) => b.visitCount - a.visitCount).slice(0, limit);
  };

  const clearAll = async () => {
    const newBookmarks = {
      ...defaultBookmarksState,
      lastUpdated: Date.now(),
    };

    await saveBookmarks(newBookmarks);
  };

  const value = {
    bookmarks,
    addBookmark,
    updateBookmark,
    removeBookmark,
    addFolder,
    updateFolder,
    removeFolder,
    moveBookmark,
    moveFolder,
    reorderFolder,
    getBookmark,
    getFolder,
    getBookmarksInFolder,
    getSubfolders,
    searchBookmarks,
    getBookmarksByTag,
    getRecentBookmarks,
    getMostVisitedBookmarks,
    clearAll,
  };

  return <BookmarksContext.Provider value={value}>{children}</BookmarksContext.Provider>;
};
