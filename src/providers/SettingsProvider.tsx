import React, { createContext, useContext, useEffect, useState } from 'react';

import { useStorage } from './StorageProvider';

interface BrowserSettings {
  general: {
    homepage: string;
    searchEngine: string;
    defaultDownloadLocation: string;
    language: string;
    timezone: string;
  };
  privacy: {
    cookies: boolean;
    history: boolean;
    cache: boolean;
    doNotTrack: boolean;
    thirdPartyCookies: boolean;
    privateBrowsing: boolean;
  };
  security: {
    safeBrowsing: boolean;
    passwordManager: boolean;
    autoFill: boolean;
    notifications: boolean;
    location: boolean;
    camera: boolean;
    microphone: boolean;
  };
  appearance: {
    showBookmarksBar: boolean;
    showStatusBar: boolean;
    showTabBar: boolean;
    showToolbar: boolean;
    showSidebar: boolean;
    sidebarPosition: 'left' | 'right';
  };
  performance: {
    hardwareAcceleration: boolean;
    backgroundTabs: boolean;
    tabDiscarding: boolean;
    memoryLimit: number;
    cacheSize: number;
  };
  advanced: {
    developerTools: boolean;
    remoteDebugging: boolean;
    experimentalFeatures: boolean;
    customUserAgent: string;
    proxySettings: {
      enabled: boolean;
      host: string;
      port: number;
      type: 'http' | 'socks4' | 'socks5';
    };
  };
}

interface SettingsContextType {
  settings: BrowserSettings;
  updateSettings: (
    category: keyof BrowserSettings,
    settings: Partial<BrowserSettings[keyof BrowserSettings]>
  ) => Promise<void>;
  resetSettings: (category?: keyof BrowserSettings) => Promise<void>;
  getSetting: <K extends keyof BrowserSettings, T extends keyof BrowserSettings[K]>(
    category: K,
    key: T
  ) => BrowserSettings[K][T];
}

const defaultSettings: BrowserSettings = {
  general: {
    homepage: 'https://www.google.com',
    searchEngine: 'google',
    defaultDownloadLocation: '~/Downloads',
    language: 'en-US',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  },
  privacy: {
    cookies: true,
    history: true,
    cache: true,
    doNotTrack: false,
    thirdPartyCookies: true,
    privateBrowsing: false,
  },
  security: {
    safeBrowsing: true,
    passwordManager: true,
    autoFill: true,
    notifications: true,
    location: false,
    camera: false,
    microphone: false,
  },
  appearance: {
    showBookmarksBar: true,
    showStatusBar: true,
    showTabBar: true,
    showToolbar: true,
    showSidebar: true,
    sidebarPosition: 'left',
  },
  performance: {
    hardwareAcceleration: true,
    backgroundTabs: true,
    tabDiscarding: true,
    memoryLimit: 1024,
    cacheSize: 512,
  },
  advanced: {
    developerTools: false,
    remoteDebugging: false,
    experimentalFeatures: false,
    customUserAgent: '',
    proxySettings: {
      enabled: false,
      host: '',
      port: 8080,
      type: 'http',
    },
  },
};

const SettingsContext = createContext<SettingsContextType | null>(null);

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

interface SettingsProviderProps {
  children: React.ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [settings, setSettings] = useState<BrowserSettings>(defaultSettings);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await storage.getItem('browserSettings');
        if (savedSettings) {
          setSettings(JSON.parse(savedSettings));
        }
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    };

    loadSettings();
  }, [storage]);

  const saveSettings = async (newSettings: BrowserSettings) => {
    try {
      await storage.setItem('browserSettings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const updateSettings = async (
    category: keyof BrowserSettings,
    newSettings: Partial<BrowserSettings[keyof BrowserSettings]>
  ) => {
    const updatedSettings = {
      ...settings,
      [category]: {
        ...settings[category],
        ...newSettings,
      },
    };
    await saveSettings(updatedSettings);
  };

  const resetSettings = async (category?: keyof BrowserSettings) => {
    if (category) {
      const updatedSettings = {
        ...settings,
        [category]: defaultSettings[category],
      };
      await saveSettings(updatedSettings);
    } else {
      await saveSettings(defaultSettings);
    }
  };

  const getSetting = <K extends keyof BrowserSettings, T extends keyof BrowserSettings[K]>(
    category: K,
    key: T
  ): BrowserSettings[K][T] => {
    return settings[category][key];
  };

  const value = {
    settings,
    updateSettings,
    resetSettings,
    getSetting,
  };

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>;
};
