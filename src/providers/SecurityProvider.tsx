import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { setSecurity } from '@store/slices/browserSlice';

import { useStorage } from './StorageProvider';

interface SecuritySettings {
  safeBrowsing: boolean;
  phishingProtection: boolean;
  malwareProtection: boolean;
  trackingProtection: boolean;
  cookieControl: boolean;
  javascriptControl: boolean;
  popupBlocking: boolean;
  privateBrowsing: boolean;
  lastUpdated: number;
}

interface SecurityState {
  settings: SecuritySettings;
  blockedDomains: string[];
  allowedDomains: string[];
  lastScan: number;
  threatsDetected: number;
  isScanning: boolean;
}

interface SecurityContextType {
  state: SecurityState;
  updateSettings: (settings: Partial<SecuritySettings>) => Promise<void>;
  addBlockedDomain: (domain: string) => Promise<void>;
  removeBlockedDomain: (domain: string) => Promise<void>;
  addAllowedDomain: (domain: string) => Promise<void>;
  removeAllowedDomain: (domain: string) => Promise<void>;
  startScan: () => Promise<void>;
  stopScan: () => Promise<void>;
  isDomainBlocked: (domain: string) => boolean;
  isDomainAllowed: (domain: string) => boolean;
  getSecurityStatus: () => { isSecure: boolean; threats: number };
}

const defaultSecurityState: SecurityState = {
  settings: {
    safeBrowsing: true,
    phishingProtection: true,
    malwareProtection: true,
    trackingProtection: true,
    cookieControl: true,
    javascriptControl: false,
    popupBlocking: true,
    privateBrowsing: false,
    lastUpdated: Date.now(),
  },
  blockedDomains: [],
  allowedDomains: [],
  lastScan: 0,
  threatsDetected: 0,
  isScanning: false,
};

const SecurityContext = createContext<SecurityContextType | null>(null);

export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

interface SecurityProviderProps {
  children: React.ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const storage = useStorage();
  const [state, setState] = useState<SecurityState>(defaultSecurityState);

  useEffect(() => {
    const loadSecurity = async () => {
      try {
        const savedSecurity = await storage.getItem('browserSecurity');
        if (savedSecurity) {
          setState(JSON.parse(savedSecurity));
        }
      } catch (error) {
        console.error('Error loading security settings:', error);
      }
    };

    loadSecurity();
  }, [storage]);

  const saveSecurity = async (newState: SecurityState) => {
    try {
      await storage.setItem('browserSecurity', JSON.stringify(newState));
      setState(newState);
    } catch (error) {
      console.error('Error saving security settings:', error);
    }
  };

  const updateSettings = async (settings: Partial<SecuritySettings>) => {
    const newState = {
      ...state,
      settings: {
        ...state.settings,
        ...settings,
        lastUpdated: Date.now(),
      },
    };

    await saveSecurity(newState);
  };

  const addBlockedDomain = async (domain: string) => {
    if (state.blockedDomains.includes(domain)) return;

    const newState = {
      ...state,
      blockedDomains: [...state.blockedDomains, domain],
      lastUpdated: Date.now(),
    };

    await saveSecurity(newState);
  };

  const removeBlockedDomain = async (domain: string) => {
    const newState = {
      ...state,
      blockedDomains: state.blockedDomains.filter(d => d !== domain),
      lastUpdated: Date.now(),
    };

    await saveSecurity(newState);
  };

  const addAllowedDomain = async (domain: string) => {
    if (state.allowedDomains.includes(domain)) return;

    const newState = {
      ...state,
      allowedDomains: [...state.allowedDomains, domain],
      lastUpdated: Date.now(),
    };

    await saveSecurity(newState);
  };

  const removeAllowedDomain = async (domain: string) => {
    const newState = {
      ...state,
      allowedDomains: state.allowedDomains.filter(d => d !== domain),
      lastUpdated: Date.now(),
    };

    await saveSecurity(newState);
  };

  const startScan = async () => {
    if (state.isScanning) return;

    const newState = {
      ...state,
      isScanning: true,
      lastScan: Date.now(),
    };

    await saveSecurity(newState);

    // Simulate security scan
    setTimeout(async () => {
      const threats = Math.floor(Math.random() * 5); // Simulated threat detection
      const scanState = {
        ...state,
        isScanning: false,
        threatsDetected: threats,
        lastScan: Date.now(),
      };

      await saveSecurity(scanState);
    }, 3000);
  };

  const stopScan = async () => {
    if (!state.isScanning) return;

    const newState = {
      ...state,
      isScanning: false,
      lastScan: Date.now(),
    };

    await saveSecurity(newState);
  };

  const isDomainBlocked = (domain: string): boolean => {
    return state.blockedDomains.includes(domain);
  };

  const isDomainAllowed = (domain: string): boolean => {
    return state.allowedDomains.includes(domain);
  };

  const getSecurityStatus = () => {
    const isSecure = state.threatsDetected === 0 && !state.isScanning;
    return {
      isSecure,
      threats: state.threatsDetected,
    };
  };

  const value = {
    state,
    updateSettings,
    addBlockedDomain,
    removeBlockedDomain,
    addAllowedDomain,
    removeAllowedDomain,
    startScan,
    stopScan,
    isDomainBlocked,
    isDomainAllowed,
    getSecurityStatus,
  };

  return <SecurityContext.Provider value={value}>{children}</SecurityContext.Provider>;
};
