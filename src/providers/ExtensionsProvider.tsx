import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage: string;
  permissions: string[];
  enabled: boolean;
  installed: boolean;
  updateAvailable: boolean;
  lastUpdated: number;
  size: number;
  icon?: string;
  manifest: {
    name: string;
    version: string;
    description: string;
    permissions: string[];
    content_scripts?: {
      matches: string[];
      js: string[];
      css: string[];
    }[];
    background?: {
      scripts: string[];
      persistent: boolean;
    };
    browser_action?: {
      default_icon: string;
      default_title: string;
      default_popup: string;
    };
    options_page?: string;
  };
}

interface ExtensionsState {
  extensions: Extension[];
  lastUpdated: number;
  totalExtensions: number;
  enabledExtensions: number;
  updateAvailable: boolean;
}

interface ExtensionsContextType {
  extensions: ExtensionsState;
  installExtension: (
    extension: Omit<Extension, 'id' | 'lastUpdated' | 'enabled' | 'installed' | 'updateAvailable'>
  ) => void;
  uninstallExtension: (id: string) => void;
  enableExtension: (id: string) => void;
  disableExtension: (id: string) => void;
  updateExtension: (id: string) => void;
  getExtension: (id: string) => Extension | undefined;
  getEnabledExtensions: () => Extension[];
  getInstalledExtensions: () => Extension[];
  getUpdateAvailableExtensions: () => Extension[];
  checkForUpdates: () => void;
  clearExtensions: () => void;
}

const ExtensionsContext = createContext<ExtensionsContextType | null>(null);

export const useExtensions = () => {
  const context = useContext(ExtensionsContext);
  if (!context) {
    throw new Error('useExtensions must be used within an ExtensionsProvider');
  }
  return context;
};

interface ExtensionsProviderProps {
  children: React.ReactNode;
}

export const ExtensionsProvider: React.FC<ExtensionsProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const [extensions, setExtensions] = useState<ExtensionsState>({
    extensions: [],
    lastUpdated: Date.now(),
    totalExtensions: 0,
    enabledExtensions: 0,
    updateAvailable: false,
  });

  useEffect(() => {
    // Load extensions from localStorage on mount
    const loadExtensions = () => {
      try {
        const savedExtensions = localStorage.getItem('browser-extensions');
        if (savedExtensions) {
          setExtensions(JSON.parse(savedExtensions));
        }
      } catch (error) {
        console.error('Failed to load extensions:', error);
      }
    };

    loadExtensions();
  }, []);

  useEffect(() => {
    // Save extensions to localStorage when they change
    try {
      localStorage.setItem('browser-extensions', JSON.stringify(extensions));
    } catch (error) {
      console.error('Failed to save extensions:', error);
    }
  }, [extensions]);

  const installExtension = (
    extension: Omit<Extension, 'id' | 'lastUpdated' | 'enabled' | 'installed' | 'updateAvailable'>
  ) => {
    const now = Date.now();
    const newExtension: Extension = {
      id: Math.random().toString(36).substr(2, 9),
      ...extension,
      enabled: true,
      installed: true,
      updateAvailable: false,
      lastUpdated: now,
    };

    setExtensions(prev => ({
      ...prev,
      extensions: [...prev.extensions, newExtension],
      lastUpdated: now,
      totalExtensions: prev.totalExtensions + 1,
      enabledExtensions: prev.enabledExtensions + 1,
    }));
  };

  const uninstallExtension = (id: string) => {
    setExtensions(prev => {
      const extension = prev.extensions.find(e => e.id === id);
      return {
        ...prev,
        extensions: prev.extensions.filter(e => e.id !== id),
        lastUpdated: Date.now(),
        totalExtensions: prev.totalExtensions - 1,
        enabledExtensions: extension?.enabled ? prev.enabledExtensions - 1 : prev.enabledExtensions,
      };
    });
  };

  const enableExtension = (id: string) => {
    setExtensions(prev => {
      const extension = prev.extensions.find(e => e.id === id);
      if (!extension || extension.enabled) return prev;

      return {
        ...prev,
        extensions: prev.extensions.map(e => (e.id === id ? { ...e, enabled: true } : e)),
        lastUpdated: Date.now(),
        enabledExtensions: prev.enabledExtensions + 1,
      };
    });
  };

  const disableExtension = (id: string) => {
    setExtensions(prev => {
      const extension = prev.extensions.find(e => e.id === id);
      if (!extension || !extension.enabled) return prev;

      return {
        ...prev,
        extensions: prev.extensions.map(e => (e.id === id ? { ...e, enabled: false } : e)),
        lastUpdated: Date.now(),
        enabledExtensions: prev.enabledExtensions - 1,
      };
    });
  };

  const updateExtension = (id: string) => {
    setExtensions(prev => {
      const extension = prev.extensions.find(e => e.id === id);
      if (!extension || !extension.updateAvailable) return prev;

      return {
        ...prev,
        extensions: prev.extensions.map(e =>
          e.id === id
            ? {
                ...e,
                updateAvailable: false,
                lastUpdated: Date.now(),
              }
            : e
        ),
        lastUpdated: Date.now(),
        updateAvailable: prev.extensions.some(e => e.id !== id && e.updateAvailable),
      };
    });
  };

  const getExtension = (id: string) => {
    return extensions.extensions.find(extension => extension.id === id);
  };

  const getEnabledExtensions = () => {
    return extensions.extensions.filter(extension => extension.enabled);
  };

  const getInstalledExtensions = () => {
    return extensions.extensions.filter(extension => extension.installed);
  };

  const getUpdateAvailableExtensions = () => {
    return extensions.extensions.filter(extension => extension.updateAvailable);
  };

  const checkForUpdates = () => {
    // This would typically make an API call to check for updates
    setExtensions(prev => ({
      ...prev,
      lastUpdated: Date.now(),
    }));
  };

  const clearExtensions = () => {
    setExtensions({
      extensions: [],
      lastUpdated: Date.now(),
      totalExtensions: 0,
      enabledExtensions: 0,
      updateAvailable: false,
    });
  };

  const value = {
    extensions,
    installExtension,
    uninstallExtension,
    enableExtension,
    disableExtension,
    updateExtension,
    getExtension,
    getEnabledExtensions,
    getInstalledExtensions,
    getUpdateAvailableExtensions,
    checkForUpdates,
    clearExtensions,
  };

  return <ExtensionsContext.Provider value={value}>{children}</ExtensionsContext.Provider>;
};
