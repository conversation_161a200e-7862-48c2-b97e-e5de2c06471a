import React, { createContext, useContext, useEffect, useState } from 'react';

import { useStorage } from './StorageProvider';

interface NetworkRequest {
  id: string;
  url: string;
  method: string;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  startTime: number;
  endTime: number;
  duration: number;
  size: number;
  type: string;
  error?: string;
}

interface NetworkSettings {
  offlineMode: boolean;
  dataSaver: boolean;
  proxyEnabled: boolean;
  proxyUrl?: string;
  dnsPrefetch: boolean;
  requestTimeout: number;
  maxConcurrentRequests: number;
  lastUpdated: number;
}

interface NetworkState {
  requests: NetworkRequest[];
  settings: NetworkSettings;
  isOnline: boolean;
  connectionType: string;
  downloadSpeed: number;
  uploadSpeed: number;
  totalDataUsed: number;
  lastUpdated: number;
}

interface NetworkContextType {
  state: NetworkState;
  updateSettings: (settings: Partial<NetworkSettings>) => Promise<void>;
  getRequest: (id: string) => NetworkRequest | undefined;
  getRequestsByUrl: (url: string) => NetworkRequest[];
  getRequestsByType: (type: string) => NetworkRequest[];
  clearRequests: () => Promise<void>;
  simulateOffline: () => Promise<void>;
  simulateOnline: () => Promise<void>;
  getNetworkStatus: () => { isOnline: boolean; type: string; speed: number };
  getDataUsage: () => { total: number; download: number; upload: number };
}

const defaultNetworkState: NetworkState = {
  requests: [],
  settings: {
    offlineMode: false,
    dataSaver: false,
    proxyEnabled: false,
    dnsPrefetch: true,
    requestTimeout: 30000,
    maxConcurrentRequests: 6,
    lastUpdated: Date.now(),
  },
  isOnline: navigator.onLine,
  connectionType: 'unknown',
  downloadSpeed: 0,
  uploadSpeed: 0,
  totalDataUsed: 0,
  lastUpdated: Date.now(),
};

const NetworkContext = createContext<NetworkContextType | null>(null);

export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};

interface NetworkProviderProps {
  children: React.ReactNode;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [state, setState] = useState<NetworkState>(defaultNetworkState);

  useEffect(() => {
    const loadNetwork = async () => {
      try {
        const savedNetwork = await storage.getItem('browserNetwork');
        if (savedNetwork) {
          setState(JSON.parse(savedNetwork));
        }
      } catch (error) {
        console.error('Error loading network settings:', error);
      }
    };

    loadNetwork();
  }, [storage]);

  useEffect(() => {
    const handleOnline = () => {
      setState(prev => ({
        ...prev,
        isOnline: true,
        lastUpdated: Date.now(),
      }));
    };

    const handleOffline = () => {
      setState(prev => ({
        ...prev,
        isOnline: false,
        lastUpdated: Date.now(),
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const saveNetwork = async (newState: NetworkState) => {
    try {
      await storage.setItem('browserNetwork', JSON.stringify(newState));
      setState(newState);
    } catch (error) {
      console.error('Error saving network settings:', error);
    }
  };

  const updateSettings = async (settings: Partial<NetworkSettings>) => {
    const newState = {
      ...state,
      settings: {
        ...state.settings,
        ...settings,
        lastUpdated: Date.now(),
      },
    };

    await saveNetwork(newState);
  };

  const getRequest = (id: string): NetworkRequest | undefined => {
    return state.requests.find(request => request.id === id);
  };

  const getRequestsByUrl = (url: string): NetworkRequest[] => {
    return state.requests.filter(request => request.url.includes(url));
  };

  const getRequestsByType = (type: string): NetworkRequest[] => {
    return state.requests.filter(request => request.type === type);
  };

  const clearRequests = async () => {
    const newState = {
      ...state,
      requests: [],
      lastUpdated: Date.now(),
    };

    await saveNetwork(newState);
  };

  const simulateOffline = async () => {
    const newState = {
      ...state,
      isOnline: false,
      lastUpdated: Date.now(),
    };

    await saveNetwork(newState);
  };

  const simulateOnline = async () => {
    const newState = {
      ...state,
      isOnline: true,
      lastUpdated: Date.now(),
    };

    await saveNetwork(newState);
  };

  const getNetworkStatus = () => {
    return {
      isOnline: state.isOnline,
      type: state.connectionType,
      speed: state.downloadSpeed,
    };
  };

  const getDataUsage = () => {
    const download = state.requests.reduce((total, request) => {
      return total + (request.method === 'GET' ? request.size : 0);
    }, 0);

    const upload = state.requests.reduce((total, request) => {
      return total + (request.method === 'POST' ? request.size : 0);
    }, 0);

    return {
      total: state.totalDataUsed,
      download,
      upload,
    };
  };

  // Intercept fetch requests
  useEffect(() => {
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const startTime = Date.now();
      const requestId = `req_${startTime}_${Math.random().toString(36).substr(2, 9)}`;
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;

      try {
        const response = await originalFetch(input, init);
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Get response size
        const contentLength = response.headers.get('content-length');
        const size = contentLength ? parseInt(contentLength, 10) : 0;

        const request: NetworkRequest = {
          id: requestId,
          url,
          method: init?.method || 'GET',
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          startTime,
          endTime,
          duration,
          size,
          type: response.headers.get('content-type') || 'unknown',
        };

        setState(prev => ({
          ...prev,
          requests: [...prev.requests, request].slice(-1000), // Keep last 1000 requests
          totalDataUsed: prev.totalDataUsed + size,
          lastUpdated: Date.now(),
        }));

        return response;
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        const request: NetworkRequest = {
          id: requestId,
          url,
          method: init?.method || 'GET',
          status: 0,
          statusText: 'Error',
          headers: {},
          startTime,
          endTime,
          duration,
          size: 0,
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        };

        setState(prev => ({
          ...prev,
          requests: [...prev.requests, request].slice(-1000),
          lastUpdated: Date.now(),
        }));

        throw error;
      }
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  const value = {
    state,
    updateSettings,
    getRequest,
    getRequestsByUrl,
    getRequestsByType,
    clearRequests,
    simulateOffline,
    simulateOnline,
    getNetworkStatus,
    getDataUsage,
  };

  return <NetworkContext.Provider value={value}>{children}</NetworkContext.Provider>;
};
