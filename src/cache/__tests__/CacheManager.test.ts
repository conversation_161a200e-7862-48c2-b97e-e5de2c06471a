import { CacheManager } from '../CacheManager';

describe('CacheManager', () => {
  let cacheManager: CacheManager;

  beforeEach(() => {
    // Mock localStorage
    const localStorageMock = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      key: jest.fn(),
      length: 0,
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
    });

    // Mock IndexedDB
    const indexedDBMock = {
      open: jest.fn().mockImplementation(() => ({
        onerror: null,
        onsuccess: null,
        onupgradeneeded: null,
        result: {
          transaction: jest.fn().mockReturnValue({
            objectStore: jest.fn().mockReturnValue({
              put: jest.fn().mockReturnValue({
                onsuccess: null,
                onerror: null,
              }),
              get: jest.fn().mockReturnValue({
                onsuccess: null,
                onerror: null,
              }),
              delete: jest.fn().mockReturnValue({
                onsuccess: null,
                onerror: null,
              }),
              clear: jest.fn().mockReturnValue({
                onsuccess: null,
                onerror: null,
              }),
              getAll: jest.fn().mockReturnValue({
                onsuccess: null,
                onerror: null,
              }),
            }),
          }),
        },
      })),
    };
    Object.defineProperty(window, 'indexedDB', {
      value: indexedDBMock,
    });

    cacheManager = CacheManager.getInstance();
    cacheManager.cleanup();
  });

  describe('Initialization', () => {
    it('should create a singleton instance', () => {
      const instance1 = CacheManager.getInstance();
      const instance2 = CacheManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with default configuration', () => {
      const config = cacheManager.getConfig();
      expect(config.maxSize).toBe(1000);
      expect(config.defaultTTL).toBe(3600000);
      expect(config.cleanupInterval).toBe(300000);
      expect(config.storageType).toBe('memory');
    });

    it('should initialize memory storage', async () => {
      await cacheManager.initialize();
      expect(cacheManager['isInitialized']).toBe(true);
    });

    it('should initialize localStorage storage', async () => {
      cacheManager.updateConfig({ storageType: 'localStorage' });
      await cacheManager.initialize();
      expect(cacheManager['isInitialized']).toBe(true);
    });

    it('should initialize IndexedDB storage', async () => {
      cacheManager.updateConfig({ storageType: 'indexedDB' });
      await cacheManager.initialize();
      expect(cacheManager['isInitialized']).toBe(true);
    });
  });

  describe('Cache Operations', () => {
    beforeEach(async () => {
      await cacheManager.initialize();
    });

    it('should set and get items', async () => {
      const key = 'test';
      const value = { data: 'test' };
      await cacheManager.set(key, value);
      const result = await cacheManager.get(key);
      expect(result).toEqual(value);
    });

    it('should handle expired items', async () => {
      const key = 'test';
      const value = { data: 'test' };
      await cacheManager.set(key, value, { ttl: 0 });
      const result = await cacheManager.get(key);
      expect(result).toBeNull();
    });

    it('should delete items', async () => {
      const key = 'test';
      const value = { data: 'test' };
      await cacheManager.set(key, value);
      await cacheManager.delete(key);
      const result = await cacheManager.get(key);
      expect(result).toBeNull();
    });

    it('should clear all items', async () => {
      await cacheManager.set('key1', 'value1');
      await cacheManager.set('key2', 'value2');
      await cacheManager.clear();
      const result1 = await cacheManager.get('key1');
      const result2 = await cacheManager.get('key2');
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });
  });

  describe('Storage Types', () => {
    it('should work with memory storage', async () => {
      cacheManager.updateConfig({ storageType: 'memory' });
      await cacheManager.initialize();
      await cacheManager.set('test', 'value');
      const result = await cacheManager.get('test');
      expect(result).toBe('value');
    });

    it('should work with localStorage storage', async () => {
      cacheManager.updateConfig({ storageType: 'localStorage' });
      await cacheManager.initialize();
      await cacheManager.set('test', 'value');
      expect(localStorage.setItem).toHaveBeenCalled();
      const result = await cacheManager.get('test');
      expect(result).toBe('value');
    });

    it('should work with IndexedDB storage', async () => {
      cacheManager.updateConfig({ storageType: 'indexedDB' });
      await cacheManager.initialize();
      await cacheManager.set('test', 'value');
      const result = await cacheManager.get('test');
      expect(result).toBe('value');
    });
  });

  describe('Compression and Encryption', () => {
    it('should handle compression', async () => {
      cacheManager.updateConfig({ enableCompression: true });
      await cacheManager.initialize();
      await cacheManager.set('test', 'value');
      const result = await cacheManager.get('test');
      expect(result).toBe('value');
    });

    it('should handle encryption', async () => {
      cacheManager.updateConfig({
        enableEncryption: true,
        encryptionKey: 'test-key',
      });
      await cacheManager.initialize();
      await cacheManager.set('test', 'value');
      const result = await cacheManager.get('test');
      expect(result).toBe('value');
    });
  });

  describe('Cleanup', () => {
    it('should clean up expired items', async () => {
      await cacheManager.initialize();
      await cacheManager.set('test1', 'value1', { ttl: 0 });
      await cacheManager.set('test2', 'value2', { ttl: 3600000 });
      await cacheManager['cleanup']();
      const result1 = await cacheManager.get('test1');
      const result2 = await cacheManager.get('test2');
      expect(result1).toBeNull();
      expect(result2).toBe('value2');
    });

    it('should clean up resources', () => {
      cacheManager.cleanup();
      expect(cacheManager['isInitialized']).toBe(false);
      expect(cacheManager['cleanupInterval']).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors', async () => {
      localStorage.getItem.mockImplementation(() => {
        throw new Error('Storage error');
      });
      cacheManager.updateConfig({ storageType: 'localStorage' });
      await cacheManager.initialize();
      await expect(cacheManager.get('test')).rejects.toThrow();
    });

    it('should handle initialization errors', async () => {
      cacheManager.updateConfig({ storageType: 'invalid' as any });
      await expect(cacheManager.initialize()).rejects.toThrow();
    });
  });

  describe('Configuration', () => {
    it('should update configuration', () => {
      const newConfig = {
        maxSize: 500,
        defaultTTL: 1800000,
        cleanupInterval: 600000,
      };
      cacheManager.updateConfig(newConfig);
      const config = cacheManager.getConfig();
      expect(config.maxSize).toBe(500);
      expect(config.defaultTTL).toBe(1800000);
      expect(config.cleanupInterval).toBe(600000);
    });

    it('should emit config update event', () => {
      const emitSpy = jest.spyOn(cacheManager, 'emit');
      cacheManager.updateConfig({ maxSize: 500 });
      expect(emitSpy).toHaveBeenCalledWith('configUpdated', expect.any(Object));
    });
  });
});
