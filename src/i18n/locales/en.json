{"_metadata": {"description": "English language translations for A14 Browser", "version": "1.0.0", "lastUpdated": "2024-03-19", "categories": {"app": "General application strings", "menu": "Main menu", "common": "Common UI elements", "navigation": "Navigation elements", "browser": "Browser-specific features", "settings": "Settings and preferences", "errors": "Error messages", "notifications": "System notifications", "extensions": "Extension management", "home": "Home page content"}}, "app": {"name": "A14 Browser", "tagline": "A modern, privacy-first, extensible, and lightning-fast browser for everyone."}, "menu": {"file": "File", "edit": "Edit", "view": "View", "history": "History", "bookmarks": "Bookmarks", "tools": "Tools", "help": "Help"}, "common": {"ok": "OK", "cancel": "Cancel", "close": "Close", "save": "Save", "delete": "Delete", "edit": "Edit", "search": "Search", "settings": "Settings", "yes": "Yes", "no": "No", "appName": "A14 Browser", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "open": "Open", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "updateProfile": "Update Profile", "deleteAccount": "Delete Account", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>", "help": "Help", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "about": "About", "version": "Version", "copyright": "Copyright", "allRightsReserved": "All Rights Reserved", "update": "Update", "submit": "Submit", "reset": "Reset", "enabled": "Enabled", "disabled": "Disabled", "status": "Status", "author": "Author", "description": "Description", "details": "Details", "more": "More", "less": "Less", "all": "All", "none": "None", "custom": "Custom", "default": "<PERSON><PERSON><PERSON>", "advanced": "Advanced", "basic": "Basic", "required": "Required", "optional": "Optional", "unknown": "Unknown", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "navigation": {"home": "Home", "browse": "Browse", "bookmarks": "Bookmarks", "history": "History", "downloads": "Downloads", "extensions": "Extensions", "settings": "Settings", "help": "Help"}, "browser": {"newTab": "New Tab", "closeTab": "Close Tab", "reload": "Reload", "stop": "Stop", "forward": "Forward", "back": "Back", "home": "Home", "bookmark": "Bookmark", "download": "Download", "print": "Print", "find": "Find", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "zoomReset": "Reset Zoom", "fullscreen": "Fullscreen", "developerTools": "Developer Tools", "inspectElement": "Inspect Element", "viewSource": "View Source", "savePage": "Save Page", "share": "Share", "translate": "Translate", "readerMode": "Reader Mode", "adBlock": "Ad Block", "privateBrowsing": "Private Browsing"}, "settings": {"general": "General", "appearance": "Appearance", "privacy": "Privacy", "security": "Security", "extensions": "Extensions", "advanced": "Advanced", "language": "Language", "theme": "Theme", "fontSize": "Font Size", "defaultSearchEngine": "Default Search Engine", "startupPage": "Startup Page", "downloadLocation": "Download Location", "clearBrowsingData": "Clear Browsing Data", "cookies": "Cookies", "javascript": "JavaScript", "popups": "Pop-ups", "notifications": "Notifications", "location": "Location", "camera": "Camera", "microphone": "Microphone", "proxy": "Proxy", "updates": "Updates", "about": "About"}, "errors": {"pageNotFound": "Page Not Found", "connectionError": "Connection Error", "serverError": "Server Error", "timeout": "Request Timeout", "invalidUrl": "Invalid URL", "sslError": "SSL Error", "dnsError": "DNS Error", "networkError": "Network Error", "permissionDenied": "Permission Denied", "quotaExceeded": "<PERSON><PERSON><PERSON> Exceeded", "unknownError": "Unknown Error"}, "notifications": {"updateAvailable": "Update Available", "updateDownloaded": "Update Downloaded", "updateInstalled": "Update Installed", "downloadComplete": "Download Complete", "downloadFailed": "Download Failed", "bookmarkAdded": "Bookmark Added", "bookmarkRemoved": "Bookmark Removed", "historyCleared": "History Cleared", "cookiesCleared": "Cookies Cleared", "cacheCleared": "<PERSON><PERSON>ed", "passwordSaved": "Password Saved", "passwordRemoved": "Password Removed"}, "extensions": {"title": "Extensions", "manage": "Manage Extensions", "installed": "Extension Installed", "available": "Available Extensions", "recommended": "Recommended Extensions", "settings": "Extension Settings", "settingsFor": "Settings for {{name}}", "install": "Install Extension", "uninstall": "Uninstall Extension", "update": "Update Extension", "enable": "Enable Extension", "disable": "Disable Extension", "enabled": "Extension Enabled", "disabled": "Extension Disabled", "uninstalled": "Extension Uninstalled", "updating": "Updating Extension...", "updateAvailable": "Update Available", "noUpdates": "No Updates Available", "settingsSaved": "Settings Saved", "autoUpdate": "Auto Update", "notifications": "Show Notifications", "permissions": {"storage": "Storage", "storageDescription": "Access to browser storage", "tabs": "Tabs", "tabsDescription": "Access to browser tabs", "bookmarks": "Bookmarks", "bookmarksDescription": "Access to bookmarks", "history": "History", "historyDescription": "Access to browsing history", "downloads": "Downloads", "downloadsDescription": "Access to downloads", "notifications": "Notifications", "notificationsDescription": "Show notifications", "webNavigation": "Web Navigation", "webNavigationDescription": "Access to web navigation events", "webRequest": "Web Request", "webRequestDescription": "Access to web requests", "cookies": "Cookies", "cookiesDescription": "Access to cookies", "geolocation": "Geolocation", "geolocationDescription": "Access to location data", "clipboardRead": "Clipboard Read", "clipboardReadDescription": "Read from clipboard", "clipboardWrite": "Clipboard Write", "clipboardWriteDescription": "Write to clipboard"}, "permissionsDescription": "Manage extension permissions", "permissionsRequired": "Required Permissions", "permissionsOptional": "Optional Permissions", "permissionsGranted": "Granted Permissions", "permissionsDenied": "Denied Permissions", "errors": {"installFailed": "Failed to install extension", "uninstallFailed": "Failed to uninstall extension", "updateFailed": "Failed to update extension", "enableFailed": "Failed to enable extension", "disableFailed": "Failed to disable extension", "loadFailed": "Failed to load extension", "permissionDenied": "Permission denied", "invalidManifest": "Invalid extension manifest", "incompatibleVersion": "Incompatible version", "networkError": "Network error", "storageError": "Storage error", "runtimeError": "Runtime error"}, "warnings": {"experimental": "This is an experimental extension", "deprecated": "This extension is deprecated", "blocked": "This extension is blocked", "incompatible": "This extension may be incompatible", "permissions": "This extension requires additional permissions", "updates": "This extension has pending updates"}, "info": {"installed": "Extension installed successfully", "uninstalled": "Extension uninstalled successfully", "updated": "Extension updated successfully", "enabled": "Extension enabled successfully", "disabled": "Extension disabled successfully", "settingsSaved": "Setting<PERSON> saved successfully", "permissionsGranted": "Permissions granted successfully", "permissionsDenied": "Permissions denied successfully"}, "store": {"title": "Extension Store", "search": "Search extensions...", "filters": "Filters", "sort": "Sort", "categories": {"productivity": "Productivity", "social": "Social", "entertainment": "Entertainment", "utilities": "Utilities", "security": "Security", "privacy": "Privacy", "developer": "Developer Tools", "accessibility": "Accessibility", "education": "Education", "shopping": "Shopping", "news": "News", "sports": "Sports", "weather": "Weather", "travel": "Travel", "finance": "Finance", "health": "Health", "lifestyle": "Lifestyle", "other": "Other"}, "tags": {"new": "New", "popular": "Popular", "trending": "Trending", "recommended": "Recommended", "award": "Award Winner", "beta": "Beta", "experimental": "Experimental", "premium": "Premium", "free": "Free", "openSource": "Open Source", "verified": "Verified", "featured": "Featured", "promoted": "Promoted"}, "priceRange": "Price Range", "minimumRating": "Minimum Rating", "compatibleOnly": "Compatible Only", "verifiedOnly": "Verified Only", "featuredOnly": "Featured Only", "promotedOnly": "Promoted Only", "all": "All", "install": "Install", "buy": "Buy", "details": "Details", "verified": "Verified Extension", "featured": "Featured", "trial": "{{days}} days trial", "loading": "Loading extensions...", "error": "Failed to load extensions", "noResults": "No extensions found", "sortBy": {"rating": "Rating", "downloads": "Downloads", "reviews": "Reviews", "price": "Price", "date": "Date Added", "name": "Name"}, "status": {"installed": "Installed", "updating": "Updating...", "error": "Error", "success": "Success"}, "actions": {"install": "Install Extension", "uninstall": "Uninstall Extension", "update": "Update Extension", "enable": "Enable Extension", "disable": "Disable Extension", "buy": "Buy Extension", "trial": "Start Trial", "subscribe": "Subscribe", "report": "Report Issue", "review": "Write Review", "share": "Share", "details": "View Details"}, "notifications": {"installSuccess": "Extension installed successfully", "installError": "Failed to install extension", "uninstallSuccess": "Extension uninstalled successfully", "uninstallError": "Failed to uninstall extension", "updateSuccess": "Extension updated successfully", "updateError": "Failed to update extension", "enableSuccess": "Extension enabled successfully", "enableError": "Failed to enable extension", "disableSuccess": "Extension disabled successfully", "disableError": "Failed to disable extension", "buySuccess": "Extension purchased successfully", "buyError": "Failed to purchase extension", "trialStart": "Trial started successfully", "trialError": "Failed to start trial", "subscribeSuccess": "Subscription activated successfully", "subscribeError": "Failed to activate subscription"}}}, "home": {"welcome": "Welcome to NovaBrowser!", "getStarted": "Get Started", "learnMore": "Learn More"}}