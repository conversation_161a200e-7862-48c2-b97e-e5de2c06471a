import * as React from 'react';

/**
 * RTL (Right-to-Left) Manager for supporting Arabic, Hebrew and other RTL languages
 */

export interface RTLConfig {
  enableAutoDetection: boolean;
  enableLogicalProperties: boolean;
  enableBidirectionalText: boolean;
  enableMirroredIcons: boolean;
  enableRTLAnimations: boolean;
  customRTLRules: RTLRule[];
}

export interface RTLRule {
  selector: string;
  property: string;
  ltrValue: string;
  rtlValue: string;
  priority: number;
}

export interface BidirectionalText {
  text: string;
  direction: 'ltr' | 'rtl' | 'auto';
  isolate: boolean;
}

export interface RTLStyleSheet {
  id: string;
  rules: CSSRule[];
  isActive: boolean;
}

export class RTLManager {
  private config: RTLConfig;
  private isRTLActive = false;
  private originalStyles = new Map<Element, Map<string, string>>();
  private rtlStyleSheets = new Map<string, RTLStyleSheet>();
  private mutationObserver?: MutationObserver;
  private rtlProperties = new Set([
    'margin-left',
    'margin-right',
    'padding-left',
    'padding-right',
    'border-left',
    'border-right',
    'left',
    'right',
    'text-align',
    'float',
    'clear',
    'transform-origin',
  ]);

  constructor(config: Partial<RTLConfig> = {}) {
    this.config = {
      enableAutoDetection: true,
      enableLogicalProperties: true,
      enableBidirectionalText: true,
      enableMirroredIcons: true,
      enableRTLAnimations: true,
      customRTLRules: [],
      ...config,
    };

    this.initialize();
  }

  /**
   * Initialize RTL system
   */
  private initialize(): void {
    console.log('🔄 Initializing RTL Manager...');

    this.setupLogicalProperties();
    this.setupMutationObserver();
    this.createRTLStyleSheets();
    this.setupBidirectionalTextSupport();

    console.log('✅ RTL Manager initialized');
  }

  /**
   * Activate RTL mode
   */
  activateRTL(): void {
    if (this.isRTLActive) return;

    console.log('🔄 Activating RTL mode...');

    this.isRTLActive = true;

    // Set document direction
    document.documentElement.dir = 'rtl';
    document.documentElement.setAttribute('data-direction', 'rtl');

    // Add RTL class
    document.body.classList.add('rtl');
    document.body.classList.remove('ltr');

    // Apply RTL styles
    this.applyRTLStyles();

    // Process icons
    if (this.config.enableMirroredIcons) {
      this.mirrorIcons();
    }

    // Process animations
    if (this.config.enableRTLAnimations) {
      this.adjustAnimations();
    }

    // Activate RTL stylesheets
    this.activateRTLStyleSheets();

    console.log('✅ RTL mode activated');
  }

  /**
   * Deactivate RTL mode
   */
  deactivateRTL(): void {
    if (!this.isRTLActive) return;

    console.log('🔄 Deactivating RTL mode...');

    this.isRTLActive = false;

    // Set LTR direction
    document.documentElement.dir = 'ltr';
    document.documentElement.setAttribute('data-direction', 'ltr');

    // Remove RTL class
    document.body.classList.remove('rtl');
    document.body.classList.add('ltr');

    // Restore original styles
    this.restoreOriginalStyles();

    // Deactivate RTL stylesheets
    this.deactivateRTLStyleSheets();

    console.log('✅ RTL mode deactivated');
  }

  /**
   * Toggle RTL mode
   */
  toggleRTL(): void {
    if (this.isRTLActive) {
      this.deactivateRTL();
    } else {
      this.activateRTL();
    }
  }

  /**
   * Apply RTL styles to elements
   */
  private applyRTLStyles(): void {
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
      this.applyRTLToElement(element as HTMLElement);
    });
  }

  /**
   * Apply RTL to specific element
   */
  private applyRTLToElement(element: HTMLElement): void {
    const computedStyle = window.getComputedStyle(element);
    const originalStyles = new Map<string, string>();

    // Save original styles
    this.rtlProperties.forEach(property => {
      const value = computedStyle.getPropertyValue(property);
      if (value) {
        originalStyles.set(property, value);
      }
    });

    this.originalStyles.set(element, originalStyles);

    // Apply RTL transformations
    this.transformElementForRTL(element, computedStyle);
  }

  /**
   * Transform element for RTL
   */
  private transformElementForRTL(element: HTMLElement, computedStyle: CSSStyleDeclaration): void {
    // Margin
    const marginLeft = computedStyle.marginLeft;
    const marginRight = computedStyle.marginRight;
    if (marginLeft !== marginRight) {
      element.style.marginLeft = marginRight;
      element.style.marginRight = marginLeft;
    }

    // Padding
    const paddingLeft = computedStyle.paddingLeft;
    const paddingRight = computedStyle.paddingRight;
    if (paddingLeft !== paddingRight) {
      element.style.paddingLeft = paddingRight;
      element.style.paddingRight = paddingLeft;
    }

    // Border
    const borderLeft = computedStyle.borderLeftWidth;
    const borderRight = computedStyle.borderRightWidth;
    if (borderLeft !== borderRight) {
      element.style.borderLeftWidth = borderRight;
      element.style.borderRightWidth = borderLeft;
    }

    // Position
    const left = computedStyle.left;
    const right = computedStyle.right;
    if (left !== 'auto' && right === 'auto') {
      element.style.right = left;
      element.style.left = 'auto';
    } else if (right !== 'auto' && left === 'auto') {
      element.style.left = right;
      element.style.right = 'auto';
    }

    // Text align
    const textAlign = computedStyle.textAlign;
    if (textAlign === 'left') {
      element.style.textAlign = 'right';
    } else if (textAlign === 'right') {
      element.style.textAlign = 'left';
    }

    // Float
    const float = computedStyle.float;
    if (float === 'left') {
      element.style.float = 'right';
    } else if (float === 'right') {
      element.style.float = 'left';
    }

    // Clear
    const clear = computedStyle.clear;
    if (clear === 'left') {
      element.style.clear = 'right';
    } else if (clear === 'right') {
      element.style.clear = 'left';
    }

    // Transform origin
    const transformOrigin = computedStyle.transformOrigin;
    if (transformOrigin.includes('left')) {
      element.style.transformOrigin = transformOrigin.replace('left', 'right');
    } else if (transformOrigin.includes('right')) {
      element.style.transformOrigin = transformOrigin.replace('right', 'left');
    }

    // Apply custom rules
    this.applyCustomRTLRules(element);
  }

  /**
   * Apply custom RTL rules
   */
  private applyCustomRTLRules(element: HTMLElement): void {
    this.config.customRTLRules.forEach(rule => {
      if (element.matches(rule.selector)) {
        element.style.setProperty(rule.property, rule.rtlValue, 'important');
      }
    });
  }

  /**
   * Restore original styles
   */
  private restoreOriginalStyles(): void {
    this.originalStyles.forEach((styles, element) => {
      styles.forEach((value, property) => {
        (element as HTMLElement).style.setProperty(property, value);
      });
    });

    this.originalStyles.clear();
  }

  /**
   * Setup CSS logical properties
   */
  private setupLogicalProperties(): void {
    if (!this.config.enableLogicalProperties) return;

    // Create CSS with logical properties
    const logicalCSS = `
      .rtl-logical {
        margin-inline-start: var(--margin-start, 0);
        margin-inline-end: var(--margin-end, 0);
        padding-inline-start: var(--padding-start, 0);
        padding-inline-end: var(--padding-end, 0);
        border-inline-start: var(--border-start, none);
        border-inline-end: var(--border-end, none);
        inset-inline-start: var(--inset-start, auto);
        inset-inline-end: var(--inset-end, auto);
      }
    `;

    this.injectCSS('logical-properties', logicalCSS);
  }

  /**
   * Setup bidirectional text support
   */
  private setupBidirectionalTextSupport(): void {
    if (!this.config.enableBidirectionalText) return;

    // Automatic text direction detection
    const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');

    textElements.forEach(element => {
      this.detectAndApplyTextDirection(element as HTMLElement);
    });
  }

  /**
   * Detect and apply text direction
   */
  private detectAndApplyTextDirection(element: HTMLElement): void {
    const text = element.textContent || '';
    const direction = this.detectTextDirection(text);

    if (direction !== 'auto') {
      element.dir = direction;

      if (this.config.enableBidirectionalText) {
        element.style.unicodeBidi = 'isolate';
      }
    }
  }

  /**
   * Detect text direction
   */
  private detectTextDirection(text: string): 'ltr' | 'rtl' | 'auto' {
    const rtlChars =
      /[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    const ltrChars = /[A-Za-z]/;

    const rtlCount = (text.match(rtlChars) || []).length;
    const ltrCount = (text.match(ltrChars) || []).length;

    if (rtlCount > ltrCount) {
      return 'rtl';
    } else if (ltrCount > rtlCount) {
      return 'ltr';
    }

    return 'auto';
  }

  /**
   * Mirror icons for RTL
   */
  private mirrorIcons(): void {
    const iconSelectors = [
      '.icon',
      '[class*="icon-"]',
      'svg',
      '.fa',
      '[class*="fa-"]',
      '.material-icons',
    ];

    const icons = document.querySelectorAll(iconSelectors.join(', '));

    icons.forEach(icon => {
      const element = icon as HTMLElement;

      // Check if icon should be mirrored
      if (this.shouldMirrorIcon(element)) {
        element.style.transform = 'scaleX(-1)';
        element.classList.add('rtl-mirrored');
      }
    });
  }

  /**
   * Check if icon should be mirrored
   */
  private shouldMirrorIcon(element: HTMLElement): boolean {
    const mirrorableIcons = [
      'arrow-left',
      'arrow-right',
      'chevron-left',
      'chevron-right',
      'angle-left',
      'angle-right',
      'caret-left',
      'caret-right',
      'back',
      'forward',
      'previous',
      'next',
      'undo',
      'redo',
    ];

    const className = element.className.toLowerCase();
    const iconName = element.getAttribute('data-icon') || '';

    return mirrorableIcons.some(icon => className.includes(icon) || iconName.includes(icon));
  }

  /**
   * Adjust animations for RTL
   */
  private adjustAnimations(): void {
    const animatedElements = document.querySelectorAll('[class*="animate-"], .animated');

    animatedElements.forEach(element => {
      this.adjustElementAnimations(element as HTMLElement);
    });
  }

  /**
   * Adjust element animations for RTL
   */
  private adjustElementAnimations(element: HTMLElement): void {
    const animations = element.getAnimations();

    animations.forEach(animation => {
      if (animation instanceof CSSAnimation) {
        this.adjustCSSAnimation(animation, element);
      }
    });
  }

  /**
   * Adjust CSS animation for RTL
   */
  private adjustCSSAnimation(animation: CSSAnimation, element: HTMLElement): void {
    const animationName = animation.animationName;

    // Create RTL version of animation
    if (animationName.includes('slide') || animationName.includes('move')) {
      const rtlAnimationName = `${animationName}-rtl`;
      this.createRTLAnimation(animationName, rtlAnimationName);
      element.style.animationName = rtlAnimationName;
    }
  }

  /**
   * Create RTL version of animation
   */
  private createRTLAnimation(originalName: string, rtlName: string): void {
    // Get original keyframes
    const styleSheets = Array.from(document.styleSheets);

    for (const styleSheet of styleSheets) {
      try {
        const rules = Array.from(styleSheet.cssRules || []);

        for (const rule of rules) {
          if (rule instanceof CSSKeyframesRule && rule.name === originalName) {
            const rtlKeyframes = this.convertKeyframesToRTL(rule);
            this.injectKeyframes(rtlName, rtlKeyframes);
            return;
          }
        }
      } catch (error) {
        // Ignore errors accessing external stylesheets
      }
    }
  }

  /**
   * Convert keyframes to RTL
   */
  private convertKeyframesToRTL(keyframesRule: CSSKeyframesRule): string {
    let rtlKeyframes = '';

    for (let i = 0; i < keyframesRule.cssRules.length; i++) {
      const keyframe = keyframesRule.cssRules[i] as CSSKeyframeRule;
      const rtlStyle = this.convertStyleToRTL(keyframe.style.cssText);
      rtlKeyframes += `${keyframe.keyText} { ${rtlStyle} }\n`;
    }

    return rtlKeyframes;
  }

  /**
   * Convert style to RTL
   */
  private convertStyleToRTL(cssText: string): string {
    return cssText
      .replace(/translateX\((-?\d+(?:\.\d+)?(?:px|em|rem|%))\)/g, (match, value) => {
        const numValue = parseFloat(value);
        const unit = value.replace(numValue.toString(), '');
        return `translateX(${-numValue}${unit})`;
      })
      .replace(/left/g, 'right')
      .replace(/right/g, 'left');
  }

  /**
   * Create RTL stylesheets
   */
  private createRTLStyleSheets(): void {
    const rtlCSS = `
      .rtl {
        direction: rtl;
      }
      
      .rtl .text-left {
        text-align: right !important;
      }
      
      .rtl .text-right {
        text-align: left !important;
      }
      
      .rtl .float-left {
        float: right !important;
      }
      
      .rtl .float-right {
        float: left !important;
      }
      
      .rtl .ml-auto {
        margin-right: auto !important;
        margin-left: unset !important;
      }
      
      .rtl .mr-auto {
        margin-left: auto !important;
        margin-right: unset !important;
      }
      
      .rtl .rtl-mirrored {
        transform: scaleX(-1);
      }
    `;

    this.rtlStyleSheets.set('base', {
      id: 'base',
      rules: [],
      isActive: false,
    });

    this.injectCSS('rtl-base', rtlCSS);
  }

  /**
   * Activate RTL stylesheets
   */
  private activateRTLStyleSheets(): void {
    this.rtlStyleSheets.forEach(styleSheet => {
      styleSheet.isActive = true;
    });
  }

  /**
   * Deactivate RTL stylesheets
   */
  private deactivateRTLStyleSheets(): void {
    this.rtlStyleSheets.forEach(styleSheet => {
      styleSheet.isActive = false;
    });
  }

  /**
   * Setup mutation observer
   */
  private setupMutationObserver(): void {
    this.mutationObserver = new MutationObserver(mutations => {
      if (!this.isRTLActive) return;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              this.applyRTLToElement(element);

              // Process child elements
              const children = element.querySelectorAll('*');
              children.forEach(child => {
                this.applyRTLToElement(child as HTMLElement);
              });
            }
          });
        }
      });
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  /**
   * Inject CSS
   */
  private injectCSS(id: string, css: string): void {
    let styleElement = document.getElementById(id) as HTMLStyleElement;

    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = id;
      document.head.appendChild(styleElement);
    }

    styleElement.textContent = css;
  }

  /**
   * Inject keyframes
   */
  private injectKeyframes(name: string, keyframes: string): void {
    const css = `@keyframes ${name} {\n${keyframes}\n}`;
    this.injectCSS(`keyframes-${name}`, css);
  }

  /**
   * Add custom RTL rule
   */
  addCustomRule(rule: RTLRule): void {
    this.config.customRTLRules.push(rule);
    this.config.customRTLRules.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Remove custom RTL rule
   */
  removeCustomRule(selector: string, property: string): void {
    this.config.customRTLRules = this.config.customRTLRules.filter(
      rule => !(rule.selector === selector && rule.property === property)
    );
  }

  /**
   * Check if RTL mode is active
   */
  isRTL(): boolean {
    return this.isRTLActive;
  }

  /**
   * Format bidirectional text
   */
  formatBidirectionalText(text: string, options: Partial<BidirectionalText> = {}): string {
    const config: BidirectionalText = {
      text,
      direction: 'auto',
      isolate: true,
      ...options,
    };

    let formattedText = config.text;

    if (config.isolate) {
      formattedText = `\u2068${formattedText}\u2069`; // FSI + PDI
    }

    if (config.direction !== 'auto') {
      const directionChar = config.direction === 'rtl' ? '\u202E' : '\u202D'; // RLO or LRO
      formattedText = `${directionChar}${formattedText}\u202C`; // + PDF
    }

    return formattedText;
  }

  /**
   * Destroy RTL Manager
   */
  destroy(): void {
    this.deactivateRTL();

    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }

    // Remove injected styles
    const injectedStyles = document.querySelectorAll(
      'style[id^="rtl-"], style[id^="logical-"], style[id^="keyframes-"]'
    );
    injectedStyles.forEach(style => style.remove());

    this.originalStyles.clear();
    this.rtlStyleSheets.clear();
  }
}

// Global instance
export const rtlManager = new RTLManager();

// React Hook for RTL
export function useRTL() {
  const [isRTL, setIsRTL] = React.useState(rtlManager.isRTL());

  React.useEffect(() => {
    const checkRTL = () => setIsRTL(rtlManager.isRTL());

    // Check for document direction changes
    const observer = new MutationObserver(checkRTL);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['dir'],
    });

    return () => observer.disconnect();
  }, []);

  return {
    isRTL,
    activateRTL: () => {
      rtlManager.activateRTL();
      setIsRTL(true);
    },
    deactivateRTL: () => {
      rtlManager.deactivateRTL();
      setIsRTL(false);
    },
    toggleRTL: () => {
      rtlManager.toggleRTL();
      setIsRTL(rtlManager.isRTL());
    },
    formatBidirectionalText: rtlManager.formatBidirectionalText.bind(rtlManager),
  };
}
