import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import { app } from 'electron';
import path from 'path';

import { LANGUAGES } from '../constants';

// Import translations
import enTranslation from './locales/en.json';

// Extended language configuration
export interface LanguageConfig {
  code: string;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  fallback: string;
  region?: string;
  script?: string;
  enabled: boolean;
  completeness: number; // 0-100% translation completeness
}

const resources = {
  en: {
    translation: enTranslation,
  },
};

// Extended language configurations
export const languageConfigs: Record<string, LanguageConfig> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
    fallback: 'en',
    region: 'US',
    script: 'Latn',
    enabled: true,
    completeness: 100,
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    direction: 'ltr',
    fallback: 'en',
    region: 'ES',
    script: 'Latn',
    enabled: false,
    completeness: 0,
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    direction: 'ltr',
    fallback: 'en',
    region: 'FR',
    script: 'Latn',
    enabled: false,
    completeness: 0,
  },
  de: {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    direction: 'ltr',
    fallback: 'en',
    region: 'DE',
    script: 'Latn',
    enabled: false,
    completeness: 0,
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    direction: 'rtl',
    fallback: 'en',
    region: 'SA',
    script: 'Arab',
    enabled: false,
    completeness: 0,
  },
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    direction: 'ltr',
    fallback: 'en',
    region: 'CN',
    script: 'Hans',
    enabled: false,
    completeness: 0,
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    direction: 'ltr',
    fallback: 'en',
    region: 'JP',
    script: 'Jpan',
    enabled: false,
    completeness: 0,
  },
  ru: {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    direction: 'ltr',
    fallback: 'en',
    region: 'RU',
    script: 'Cyrl',
    enabled: false,
    completeness: 0,
  },
};

// Get enabled languages
export const getEnabledLanguages = (): LanguageConfig[] => {
  return Object.values(languageConfigs).filter(lang => lang.enabled);
};

// Get supported language codes
export const getSupportedLanguages = (): string[] => {
  return getEnabledLanguages().map(lang => lang.code);
};

// Initialize i18n with enhanced configuration
const initI18n = async () => {
  const supportedLanguages = getSupportedLanguages();
  const isElectron = typeof app !== 'undefined';

  const backendOptions = isElectron ? {
    // Electron main process
    loadPath: path.join(app.getPath('userData'), 'locales', '{{lng}}', '{{ns}}.json'),
    addPath: path.join(app.getPath('userData'), 'locales', '{{lng}}', '{{ns}}.missing.json'),
  } : {
    // Browser/renderer process
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  };

  await i18n
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources,
      lng: 'en',
      fallbackLng: 'en',
      supportedLngs: supportedLanguages,
      load: 'languageOnly',
      ns: ['common', 'navigation', 'settings', 'security', 'extensions', 'bookmarks', 'history', 'downloads', 'tabs', 'errors'],
      defaultNS: 'common',
      fallbackNS: 'common',
      debug: process.env.NODE_ENV === 'development',
      interpolation: {
        escapeValue: false,
        prefix: '{{',
        suffix: '}}',
      },
      detection: {
        order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
        lookupQuerystring: 'lng',
        lookupCookie: 'i18next',
        lookupLocalStorage: 'i18nextLng',
        lookupFromPathIndex: 0,
        lookupFromSubdomainIndex: 0,
        caches: ['localStorage', 'cookie'],
        excludeCacheFor: ['cimode'],
        checkWhitelist: true,
      },
      backend: backendOptions,
      react: {
        useSuspense: false,
        bindI18n: 'languageChanged',
        bindI18nStore: '',
        transEmptyNodeValue: '',
        transSupportBasicHtmlNodes: true,
        transKeepBasicHtmlNodesFor: ['br', 'strong', 'i'],
      },
      saveMissing: process.env.NODE_ENV === 'development',
      missingKeyHandler: (lng, ns, key, fallbackValue) => {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`Missing translation: ${lng}:${ns}:${key}`);
        }
      },
    });
};

// Initialize i18n
initI18n().catch(console.error);

export { i18n, languageConfigs };

// Language utilities
export const languages = {
  en: {
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
  },
} as const;

export type LanguageCode = keyof typeof languages;

export const getLanguageName = (code: LanguageCode): string => {
  return languages[code].name;
};

export const getNativeLanguageName = (code: LanguageCode): string => {
  return languages[code].nativeName;
};

export const getLanguageDirection = (code: LanguageCode): 'ltr' | 'rtl' => {
  return languages[code].direction;
};

export const isRTL = (code: LanguageCode): boolean => {
  return languages[code].direction === 'rtl';
};

export const getCurrentLanguage = (): LanguageCode => {
  return (i18n.language as LanguageCode) || 'en';
};

export const setLanguage = async (code: LanguageCode): Promise<void> => {
  await i18n.changeLanguage(code);
  document.documentElement.lang = code;
  document.documentElement.dir = getLanguageDirection(code);
};

export const formatDate = (date: Date, locale: string) => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

export const formatNumber = (number: number, locale: string) => {
  return new Intl.NumberFormat(locale).format(number);
};

export const formatCurrency = (amount: number, locale: string, currency: string) => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatRelativeTime = (date: Date, locale: string) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

  if (days > 0) return rtf.format(-days, 'day');
  if (hours > 0) return rtf.format(-hours, 'hour');
  if (minutes > 0) return rtf.format(-minutes, 'minute');
  return rtf.format(-seconds, 'second');
};
