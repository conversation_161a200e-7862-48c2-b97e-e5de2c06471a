{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Unified Schemas for A14-Browser", "description": "Consolidated JSON schemas for all A14-Browser components", "definitions": {"extension": {"type": "object", "required": ["id", "name", "version", "description", "author", "compatibility", "license"], "properties": {"id": {"type": "string", "pattern": "^[a-z0-9-]+$", "description": "Unique identifier for the extension"}, "name": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Display name of the extension"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Version number in semantic versioning format"}, "description": {"type": "string", "minLength": 10, "maxLength": 500, "description": "Detailed description of the extension"}, "author": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "minLength": 1}, "email": {"type": "string", "format": "email"}, "website": {"type": "string", "format": "uri"}}}, "compatibility": {"type": "object", "required": ["minVersion"], "properties": {"minVersion": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "maxVersion": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}}, "license": {"type": "string", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "BSD-3-<PERSON><PERSON>", "ISC", "UNLICENSED"]}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["activeTab", "tabs", "storage", "cookies", "history", "bookmarks", "downloads", "notifications", "contextMenus", "webRequest", "webRequestBlocking", "proxy", "privacy", "management", "identity", "geolocation", "background", "unlimitedStorage", "clipboardRead", "clipboardWrite", "nativeMessaging", "desktopCapture", "pageCapture", "tabCapture", "topSites", "favicon", "idle", "power", "system.cpu", "system.memory", "system.storage"]}}, "contentScripts": {"type": "array", "items": {"type": "object", "required": ["matches", "js"], "properties": {"matches": {"type": "array", "items": {"type": "string", "format": "uri"}}, "excludeMatches": {"type": "array", "items": {"type": "string", "format": "uri"}}, "js": {"type": "array", "items": {"type": "string"}}, "css": {"type": "array", "items": {"type": "string"}}, "runAt": {"type": "string", "enum": ["document_start", "document_end", "document_idle"]}}}}, "background": {"type": "object", "properties": {"serviceWorker": {"type": "string"}, "scripts": {"type": "array", "items": {"type": "string"}}, "persistent": {"type": "boolean"}}}, "icons": {"type": "object", "required": ["16", "48", "128"], "properties": {"16": {"type": "string"}, "48": {"type": "string"}, "128": {"type": "string"}}}, "action": {"type": "object", "properties": {"defaultIcon": {"$ref": "#/definitions/extension/properties/icons"}, "defaultTitle": {"type": "string"}, "defaultPopup": {"type": "string"}}}}}, "appConfig": {"type": "object", "required": ["app", "api", "security", "performance"], "properties": {"app": {"type": "object", "required": ["name", "version", "environment"], "properties": {"name": {"type": "string"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "environment": {"type": "string", "enum": ["development", "staging", "production", "test"]}, "debug": {"type": "boolean"}, "logLevel": {"type": "string", "enum": ["error", "warn", "info", "debug", "trace"]}}}, "api": {"type": "object", "required": ["baseUrl", "timeout"], "properties": {"baseUrl": {"type": "string", "format": "uri"}, "timeout": {"type": "number", "minimum": 1000, "maximum": 300000}, "retryAttempts": {"type": "number", "minimum": 0, "maximum": 10}, "retryDelay": {"type": "number", "minimum": 100, "maximum": 10000}}}, "security": {"type": "object", "required": ["encryption", "jwt"], "properties": {"encryption": {"type": "object", "required": ["algorithm", "keySize", "iterations"], "properties": {"algorithm": {"type": "string", "enum": ["aes-256-gcm", "aes-256-cbc", "aes-192-gcm", "aes-128-gcm"]}, "keySize": {"type": "number", "enum": [16, 24, 32]}, "iterations": {"type": "number", "minimum": 10000, "maximum": 1000000}}}, "jwt": {"type": "object", "required": ["secret", "expiresIn"], "properties": {"secret": {"type": "string", "minLength": 32}, "expiresIn": {"type": "string", "pattern": "^\\d+[smhd]$"}, "refreshExpiresIn": {"type": "string", "pattern": "^\\d+[smhd]$"}}}}}, "performance": {"type": "object", "properties": {"cache": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "ttl": {"type": "number", "minimum": 0}, "maxSize": {"type": "number", "minimum": 1}}}, "compression": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "level": {"type": "number", "minimum": 0, "maximum": 9}}}}}}}, "user": {"type": "object", "required": ["id", "email", "username", "role"], "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "format": "email"}, "username": {"type": "string", "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z0-9_-]+$"}, "role": {"type": "string", "enum": ["admin", "user", "guest", "moderator"]}, "permissions": {"type": "array", "items": {"type": "string"}}, "settings": {"type": "object", "additionalProperties": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}}