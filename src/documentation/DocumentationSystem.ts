/**
 * Interactive documentation system with support for live examples,
 * auto-generation and IDE integration
 */

export interface DocumentationConfig {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  enableSearch: boolean;
  enableLiveExamples: boolean;
  enableVersioning: boolean;
  enableComments: boolean;
  enableAnalytics: boolean;
  autoGenerate: boolean;
  outputFormat: 'html' | 'markdown' | 'pdf' | 'json';
}

export interface DocumentationPage {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  content: DocumentationContent[];
  metadata: {
    author: string;
    lastModified: Date;
    version: string;
    reviewers: string[];
    status: 'draft' | 'review' | 'published' | 'deprecated';
  };
  navigation: {
    previous?: string;
    next?: string;
    parent?: string;
    children?: string[];
  };
}

export interface DocumentationContent {
  id: string;
  type:
    | 'text'
    | 'code'
    | 'example'
    | 'api'
    | 'image'
    | 'video'
    | 'interactive'
    | 'table'
    | 'diagram';
  content: string;
  language?: string;
  runnable?: boolean;
  editable?: boolean;
  collapsible?: boolean;
  metadata?: Record<string, any>;
}

export interface APIDocumentation {
  id: string;
  name: string;
  description: string;
  type: 'function' | 'class' | 'interface' | 'type' | 'constant' | 'module';
  signature: string;
  parameters: APIParameter[];
  returns: APIReturn;
  examples: CodeExample[];
  notes: string[];
  deprecated?: boolean;
  since?: string;
  seeAlso: string[];
}

export interface APIParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: any;
  examples?: any[];
}

export interface APIReturn {
  type: string;
  description: string;
  examples?: any[];
}

export interface CodeExample {
  id: string;
  title: string;
  description: string;
  code: string;
  language: string;
  runnable: boolean;
  output?: string;
  dependencies?: string[];
}

export interface SearchIndex {
  pages: Map<string, DocumentationPage>;
  content: Map<string, DocumentationContent>;
  api: Map<string, APIDocumentation>;
  searchTerms: Map<string, string[]>;
}

export class DocumentationSystem {
  private config: DocumentationConfig;
  private pages = new Map<string, DocumentationPage>();
  private apiDocs = new Map<string, APIDocumentation>();
  private searchIndex: SearchIndex;
  private templates = new Map<string, string>();
  private plugins = new Map<string, DocumentationPlugin>();

  constructor(config: Partial<DocumentationConfig> = {}) {
    this.config = {
      theme: 'auto',
      language: 'en',
      enableSearch: true,
      enableLiveExamples: true,
      enableVersioning: true,
      enableComments: false,
      enableAnalytics: true,
      autoGenerate: true,
      outputFormat: 'html',
      ...config,
    };

    this.searchIndex = {
      pages: new Map(),
      content: new Map(),
      api: new Map(),
      searchTerms: new Map(),
    };

    this.initializeTemplates();
    this.initializePlugins();
  }

  /**
   * Add documentation page
   */
  addPage(page: DocumentationPage): void {
    this.pages.set(page.id, page);
    this.updateSearchIndex(page);

    console.log(`📄 Added documentation page: ${page.title}`);
  }

  /**
   * Add API documentation
   */
  addAPIDocumentation(apiDoc: APIDocumentation): void {
    this.apiDocs.set(apiDoc.id, apiDoc);
    this.searchIndex.api.set(apiDoc.id, apiDoc);

    console.log(`🔧 Added API documentation: ${apiDoc.name}`);
  }

  /**
   * Automatically generate documentation from code
   */
  async generateFromCode(sourceFiles: string[]): Promise<void> {
    if (!this.config.autoGenerate) return;

    console.log('🤖 Auto-generating documentation from code...');

    for (const file of sourceFiles) {
      try {
        const apiDocs = await this.parseSourceFile(file);
        apiDocs.forEach(doc => this.addAPIDocumentation(doc));
      } catch (error) {
        console.error(`Failed to parse ${file}:`, error);
      }
    }

    console.log('✅ Auto-generation completed');
  }

  /**
   * Парсит исходный файл для извлечения документации
   */
  private async parseSourceFile(filePath: string): Promise<APIDocumentation[]> {
    // Упрощенная реализация - в реальном проекте используйте TypeScript Compiler API
    const docs: APIDocumentation[] = [];

    // Здесь будет логика парсинга JSDoc комментариев, TypeScript интерфейсов и т.д.

    return docs;
  }

  /**
   * Выполняет поиск по документации
   */
  search(
    query: string,
    options: {
      category?: string;
      type?: string;
      limit?: number;
    } = {}
  ): SearchResult[] {
    const results: SearchResult[] = [];
    const searchTerms = query.toLowerCase().split(' ');

    // Поиск по страницам
    for (const page of this.pages.values()) {
      const score = this.calculateSearchScore(page, searchTerms);
      if (score > 0) {
        results.push({
          id: page.id,
          type: 'page',
          title: page.title,
          description: page.description,
          score,
          url: `/docs/${page.id}`,
          highlights: this.getHighlights(page, searchTerms),
        });
      }
    }

    // Поиск по API документации
    for (const apiDoc of this.apiDocs.values()) {
      const score = this.calculateAPISearchScore(apiDoc, searchTerms);
      if (score > 0) {
        results.push({
          id: apiDoc.id,
          type: 'api',
          title: apiDoc.name,
          description: apiDoc.description,
          score,
          url: `/docs/api/${apiDoc.id}`,
          highlights: this.getAPIHighlights(apiDoc, searchTerms),
        });
      }
    }

    // Сортируем по релевантности
    results.sort((a, b) => b.score - a.score);

    // Применяем фильтры и лимиты
    let filteredResults = results;

    if (options.category) {
      filteredResults = results.filter(r => {
        const page = this.pages.get(r.id);
        return page?.category === options.category;
      });
    }

    if (options.type) {
      filteredResults = filteredResults.filter(r => r.type === options.type);
    }

    if (options.limit) {
      filteredResults = filteredResults.slice(0, options.limit);
    }

    return filteredResults;
  }

  /**
   * Генерирует статическую документацию
   */
  async generateStaticDocs(outputDir: string): Promise<void> {
    console.log(`📚 Generating static documentation to ${outputDir}...`);

    // Создаем индексную страницу
    await this.generateIndexPage(outputDir);

    // Генерируем страницы документации
    for (const page of this.pages.values()) {
      await this.generatePageHTML(page, outputDir);
    }

    // Генерируем API документацию
    await this.generateAPIPages(outputDir);

    // Копируем ресурсы
    await this.copyAssets(outputDir);

    // Генерируем поисковый индекс
    await this.generateSearchIndex(outputDir);

    console.log('✅ Static documentation generated');
  }

  /**
   * Создает живой пример кода
   */
  createLiveExample(example: CodeExample): HTMLElement {
    const container = document.createElement('div');
    container.className = 'live-example';

    // Создаем редактор кода
    const editor = document.createElement('textarea');
    editor.value = example.code;
    editor.className = 'code-editor';

    // Создаем область вывода
    const output = document.createElement('div');
    output.className = 'example-output';

    // Создаем кнопку запуска
    const runButton = document.createElement('button');
    runButton.textContent = 'Run Example';
    runButton.className = 'run-button';

    runButton.addEventListener('click', async () => {
      try {
        const result = await this.executeCode(editor.value, example.language);
        output.innerHTML = this.formatOutput(result);
      } catch (error) {
        output.innerHTML = `<div class="error">Error: ${(error as Error).message}</div>`;
      }
    });

    container.appendChild(editor);
    container.appendChild(runButton);
    container.appendChild(output);

    return container;
  }

  /**
   * Выполняет код в безопасной среде
   */
  private async executeCode(code: string, language: string): Promise<any> {
    switch (language) {
      case 'javascript':
      case 'typescript':
        // Выполняем в изолированном контексте
        return this.executeJavaScript(code);

      case 'html':
        return this.renderHTML(code);

      case 'css':
        return this.applyCSSExample(code);

      default:
        throw new Error(`Language ${language} not supported for live examples`);
    }
  }

  /**
   * Выполняет JavaScript код
   */
  private executeJavaScript(code: string): any {
    // Создаем изолированный контекст
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);

    try {
      const iframeWindow = iframe.contentWindow;
      if (!iframeWindow) throw new Error('Cannot access iframe window');

      // Выполняем код в iframe
      const result = iframeWindow.eval(`
        (function() {
          ${code}
        })()
      `);

      return result;
    } finally {
      document.body.removeChild(iframe);
    }
  }

  /**
   * Рендерит HTML пример
   */
  private renderHTML(html: string): string {
    // Санитизируем HTML для безопасности
    const sanitized = this.sanitizeHTML(html);
    return sanitized;
  }

  /**
   * Применяет CSS пример
   */
  private applyCSSExample(css: string): string {
    return `<style>${css}</style><div class="css-example">CSS applied</div>`;
  }

  /**
   * Санитизирует HTML
   */
  private sanitizeHTML(html: string): string {
    // Упрощенная санитизация - в реальном проекте используйте DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '');
  }

  /**
   * Создает интерактивную диаграмму
   */
  createInteractiveDiagram(config: DiagramConfig): HTMLElement {
    const container = document.createElement('div');
    container.className = 'interactive-diagram';

    // Здесь будет интеграция с библиотеками диаграмм (Mermaid, D3.js и т.д.)

    return container;
  }

  /**
   * Экспортирует документацию в различные форматы
   */
  async exportDocumentation(format: 'pdf' | 'epub' | 'docx', options: any = {}): Promise<Blob> {
    console.log(`📤 Exporting documentation to ${format}...`);

    switch (format) {
      case 'pdf':
        return this.exportToPDF(options);
      case 'epub':
        return this.exportToEPUB(options);
      case 'docx':
        return this.exportToDocx(options);
      default:
        throw new Error(`Export format ${format} not supported`);
    }
  }

  /**
   * Добавляет плагин документации
   */
  addPlugin(name: string, plugin: DocumentationPlugin): void {
    this.plugins.set(name, plugin);
    plugin.initialize(this);

    console.log(`🔌 Added documentation plugin: ${name}`);
  }

  /**
   * Валидирует документацию
   */
  async validateDocumentation(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Проверяем страницы
    for (const page of this.pages.values()) {
      const pageResults = await this.validatePage(page);
      results.push(...pageResults);
    }

    // Проверяем API документацию
    for (const apiDoc of this.apiDocs.values()) {
      const apiResults = await this.validateAPIDoc(apiDoc);
      results.push(...apiResults);
    }

    return results;
  }

  /**
   * Генерирует метрики документации
   */
  generateMetrics(): DocumentationMetrics {
    const totalPages = this.pages.size;
    const totalAPIItems = this.apiDocs.size;
    const totalContent = Array.from(this.pages.values()).reduce(
      (sum, page) => sum + page.content.length,
      0
    );

    const coverage = this.calculateCoverage();
    const quality = this.calculateQuality();

    return {
      totalPages,
      totalAPIItems,
      totalContent,
      coverage,
      quality,
      lastUpdated: new Date(),
      languages: this.getAvailableLanguages(),
      categories: this.getCategories(),
    };
  }

  // Приватные методы для внутренней логики

  private initializeTemplates(): void {
    this.templates.set(
      'page',
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>{{title}}</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <link rel="stylesheet" href="/docs/assets/styles.css">
        </head>
        <body>
          <nav class="sidebar">{{navigation}}</nav>
          <main class="content">
            <header>
              <h1>{{title}}</h1>
              <p class="description">{{description}}</p>
            </header>
            <article>{{content}}</article>
          </main>
          <script src="/docs/assets/scripts.js"></script>
        </body>
      </html>
    `
    );
  }

  private initializePlugins(): void {
    // Инициализация встроенных плагинов
    this.addPlugin('syntax-highlighter', new SyntaxHighlighterPlugin());
    this.addPlugin('mermaid-diagrams', new MermaidPlugin());
    this.addPlugin('search', new SearchPlugin());
  }

  private updateSearchIndex(page: DocumentationPage): void {
    this.searchIndex.pages.set(page.id, page);

    // Индексируем контент
    page.content.forEach(content => {
      this.searchIndex.content.set(content.id, content);
    });

    // Создаем поисковые термы
    const terms = this.extractSearchTerms(page);
    this.searchIndex.searchTerms.set(page.id, terms);
  }

  private extractSearchTerms(page: DocumentationPage): string[] {
    const terms: string[] = [];

    // Добавляем заголовок и описание
    terms.push(...page.title.toLowerCase().split(' '));
    terms.push(...page.description.toLowerCase().split(' '));

    // Добавляем теги
    terms.push(...page.tags.map(tag => tag.toLowerCase()));

    // Добавляем контент
    page.content.forEach(content => {
      if (content.type === 'text') {
        terms.push(...content.content.toLowerCase().split(' '));
      }
    });

    // Удаляем дубликаты и пустые строки
    return [...new Set(terms)].filter(term => term.length > 2);
  }

  private calculateSearchScore(page: DocumentationPage, searchTerms: string[]): number {
    let score = 0;
    const pageTerms = this.searchIndex.searchTerms.get(page.id) || [];

    searchTerms.forEach(term => {
      // Точное совпадение в заголовке
      if (page.title.toLowerCase().includes(term)) {
        score += 10;
      }

      // Совпадение в описании
      if (page.description.toLowerCase().includes(term)) {
        score += 5;
      }

      // Совпадение в тегах
      if (page.tags.some(tag => tag.toLowerCase().includes(term))) {
        score += 3;
      }

      // Совпадение в контенте
      if (pageTerms.includes(term)) {
        score += 1;
      }
    });

    return score;
  }

  private calculateAPISearchScore(apiDoc: APIDocumentation, searchTerms: string[]): number {
    let score = 0;

    searchTerms.forEach(term => {
      if (apiDoc.name.toLowerCase().includes(term)) {
        score += 10;
      }
      if (apiDoc.description.toLowerCase().includes(term)) {
        score += 5;
      }
      if (apiDoc.signature.toLowerCase().includes(term)) {
        score += 3;
      }
    });

    return score;
  }

  private getHighlights(page: DocumentationPage, searchTerms: string[]): string[] {
    const highlights: string[] = [];

    searchTerms.forEach(term => {
      if (page.title.toLowerCase().includes(term)) {
        highlights.push(this.highlightTerm(page.title, term));
      }
      if (page.description.toLowerCase().includes(term)) {
        highlights.push(this.highlightTerm(page.description, term));
      }
    });

    return highlights;
  }

  private getAPIHighlights(apiDoc: APIDocumentation, searchTerms: string[]): string[] {
    const highlights: string[] = [];

    searchTerms.forEach(term => {
      if (apiDoc.name.toLowerCase().includes(term)) {
        highlights.push(this.highlightTerm(apiDoc.name, term));
      }
      if (apiDoc.description.toLowerCase().includes(term)) {
        highlights.push(this.highlightTerm(apiDoc.description, term));
      }
    });

    return highlights;
  }

  private highlightTerm(text: string, term: string): string {
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  private formatOutput(result: any): string {
    if (typeof result === 'object') {
      return `<pre>${JSON.stringify(result, null, 2)}</pre>`;
    }
    return `<div class="output">${String(result)}</div>`;
  }

  private async generateIndexPage(outputDir: string): Promise<void> {
    // Генерация главной страницы документации
  }

  private async generatePageHTML(page: DocumentationPage, outputDir: string): Promise<void> {
    // Генерация HTML для страницы
  }

  private async generateAPIPages(outputDir: string): Promise<void> {
    // Генерация страниц API документации
  }

  private async copyAssets(outputDir: string): Promise<void> {
    // Копирование CSS, JS и других ресурсов
  }

  private async generateSearchIndex(outputDir: string): Promise<void> {
    // Generate search index for static documentation
  }

  private async exportToPDF(options: any): Promise<Blob> {
    // Export to PDF
    return new Blob();
  }

  private async exportToEPUB(options: any): Promise<Blob> {
    // Export to EPUB
    return new Blob();
  }

  private async exportToDocx(options: any): Promise<Blob> {
    // Export to DOCX
    return new Blob();
  }

  private async validatePage(page: DocumentationPage): Promise<ValidationResult[]> {
    // Validate documentation page
    return [];
  }

  private async validateAPIDoc(apiDoc: APIDocumentation): Promise<ValidationResult[]> {
    // Validate API documentation
    return [];
  }

  private calculateCoverage(): number {
    // Calculate documentation coverage
    return 85;
  }

  private calculateQuality(): number {
    // Calculate documentation quality
    return 90;
  }

  private getAvailableLanguages(): string[] {
    return ['en', 'ru'];
  }

  private getCategories(): string[] {
    return [...new Set(Array.from(this.pages.values()).map(page => page.category))];
  }
}

// Интерфейсы для дополнительных типов
export interface SearchResult {
  id: string;
  type: 'page' | 'api';
  title: string;
  description: string;
  score: number;
  url: string;
  highlights: string[];
}

export interface DiagramConfig {
  type: 'flowchart' | 'sequence' | 'class' | 'state' | 'gantt';
  data: any;
  options: any;
}

export interface DocumentationPlugin {
  name: string;
  initialize(system: DocumentationSystem): void;
  process(content: DocumentationContent): DocumentationContent;
}

export interface ValidationResult {
  type: 'error' | 'warning' | 'info';
  message: string;
  location: string;
  suggestion?: string;
}

export interface DocumentationMetrics {
  totalPages: number;
  totalAPIItems: number;
  totalContent: number;
  coverage: number;
  quality: number;
  lastUpdated: Date;
  languages: string[];
  categories: string[];
}

// Встроенные плагины
class SyntaxHighlighterPlugin implements DocumentationPlugin {
  name = 'syntax-highlighter';

  initialize(system: DocumentationSystem): void {
    console.log('🎨 Syntax highlighter plugin initialized');
  }

  process(content: DocumentationContent): DocumentationContent {
    if (content.type === 'code' && content.language) {
      // Здесь будет подсветка синтаксиса
      content.content = this.highlightSyntax(content.content, content.language);
    }
    return content;
  }

  private highlightSyntax(code: string, language: string): string {
    // Упрощенная подсветка синтаксиса
    return `<pre class="language-${language}"><code>${code}</code></pre>`;
  }
}

class MermaidPlugin implements DocumentationPlugin {
  name = 'mermaid-diagrams';

  initialize(system: DocumentationSystem): void {
    console.log('📊 Mermaid diagrams plugin initialized');
  }

  process(content: DocumentationContent): DocumentationContent {
    if (content.type === 'diagram') {
      content.content = this.renderMermaidDiagram(content.content);
    }
    return content;
  }

  private renderMermaidDiagram(mermaidCode: string): string {
    return `<div class="mermaid">${mermaidCode}</div>`;
  }
}

class SearchPlugin implements DocumentationPlugin {
  name = 'search';

  initialize(system: DocumentationSystem): void {
    console.log('🔍 Search plugin initialized');
  }

  process(content: DocumentationContent): DocumentationContent {
    // Обработка контента для поиска
    return content;
  }
}

// Global instance
export const documentationSystem = new DocumentationSystem();
