import i18next from 'i18next';

import { LanguageCode } from '../i18n/config';

// Translation utilities
export const t = (key: string, options?: i18next.TOptions): string => {
  return i18next.t(key, options);
};

export const tWithFallback = (
  key: string,
  fallback: string,
  options?: i18next.TOptions
): string => {
  const translation = i18next.t(key, options);
  return translation === key ? fallback : translation;
};

// Date formatting utilities
export const formatDate = (
  date: Date | string | number,
  options?: Intl.DateTimeFormatOptions
): string => {
  const currentLanguage = i18next.language as LanguageCode;
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;

  return new Intl.DateTimeFormat(currentLanguage, options).format(dateObj);
};

// Number formatting utilities
export const formatNumber = (number: number, options?: Intl.NumberFormatOptions): string => {
  const currentLanguage = i18next.language as LanguageCode;
  return new Intl.NumberFormat(currentLanguage, options).format(number);
};

// Currency formatting utilities
export const formatCurrency = (
  amount: number,
  currency: string,
  options?: Intl.NumberFormatOptions
): string => {
  const currentLanguage = i18next.language as LanguageCode;
  return new Intl.NumberFormat(currentLanguage, {
    style: 'currency',
    currency,
    ...options,
  }).format(amount);
};

// Pluralization utilities
export const pluralize = (key: string, count: number, options?: i18next.TOptions): string => {
  return i18next.t(key, { count, ...options });
};

// Language utilities
export const getCurrentLanguage = (): LanguageCode => {
  return i18next.language as LanguageCode;
};

export const isRTL = (): boolean => {
  const currentLanguage = getCurrentLanguage();
  return document.documentElement.dir === 'rtl';
};

// Translation key utilities
export const getTranslationKey = (namespace: string, key: string): string => {
  return `${namespace}:${key}`;
};

// Translation validation utilities
export const hasTranslation = (key: string): boolean => {
  const translation = i18next.t(key);
  return translation !== key;
};

// Translation interpolation utilities
export const interpolate = (template: string, values: Record<string, string | number>): string => {
  return template.replace(/\{\{(\w+)\}\}/g, (_, key) => String(values[key] || ''));
};

// Translation namespace utilities
export const addNamespace = (namespace: string, translations: Record<string, any>): void => {
  i18next.addResourceBundle(getCurrentLanguage(), namespace, translations, true, true);
};

export const removeNamespace = (namespace: string): void => {
  i18next.removeResourceBundle(getCurrentLanguage(), namespace);
};

// Translation loading utilities
export const loadTranslations = async (language: LanguageCode): Promise<void> => {
  await i18next.changeLanguage(language);
};

// Translation error handling
export const handleTranslationError = (error: Error): void => {
  console.error('Translation error:', error);
  // Add your error handling logic here
};

// Translation debugging utilities
export const enableTranslationDebug = (): void => {
  i18next.options.debug = true;
};

export const disableTranslationDebug = (): void => {
  i18next.options.debug = false;
};

// Translation cache utilities
export const clearTranslationCache = (): void => {
  i18next.reloadResources();
};

// Translation export utilities
export const exportTranslations = (): Record<string, any> => {
  return i18next.getResourceBundle(getCurrentLanguage(), 'translation');
};

// Translation import utilities
export const importTranslations = (translations: Record<string, any>): void => {
  i18next.addResourceBundle(getCurrentLanguage(), 'translation', translations, true, true);
};
