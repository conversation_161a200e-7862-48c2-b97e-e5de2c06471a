import { BrowserWindow, app } from 'electron';
import { v4 as uuidv4 } from 'uuid';

import { logger } from '../logging/Logger';

interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  sessionId: string;
  userId?: string;
  properties?: Record<string, any>;
}

interface PageView {
  url: string;
  title: string;
  timestamp: number;
  sessionId: string;
  userId?: string;
  referrer?: string;
  properties?: Record<string, any>;
}

interface UserTiming {
  name: string;
  category: string;
  duration: number;
  timestamp: number;
  sessionId: string;
  userId?: string;
  properties?: Record<string, any>;
}

class Analytics {
  private sessionId: string;
  private userId?: string;
  private queue: (AnalyticsEvent | PageView | UserTiming)[] = [];
  private readonly batchSize = 50;
  private readonly flushInterval = 60000; // 1 minute
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.sessionId = uuidv4();
    this.startFlushTimer();
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Track app lifecycle events
    app.on('ready', () => this.trackEvent('app', 'start'));
    app.on('window-all-closed', () => this.trackEvent('app', 'close'));
    app.on('activate', () => this.trackEvent('app', 'activate'));

    // Track window events
    app.on('browser-window-created', (_, window) => {
      this.trackWindowEvents(window);
    });
  }

  private trackWindowEvents(window: BrowserWindow) {
    window.on('focus', () => this.trackEvent('window', 'focus'));
    window.on('blur', () => this.trackEvent('window', 'blur'));
    window.on('maximize', () => this.trackEvent('window', 'maximize'));
    window.on('unmaximize', () => this.trackEvent('window', 'unmaximize'));
    window.on('minimize', () => this.trackEvent('window', 'minimize'));
    window.on('restore', () => this.trackEvent('window', 'restore'));

    // Track page views
    window.webContents.on('did-navigate', (_, url) => {
      this.trackPageView(url, window.webContents.getTitle());
    });

    // Track user timing
    window.webContents.on('did-finish-load', () => {
      window.webContents.executeJavaScript(`
        const performance = window.performance;
        if (performance && performance.timing) {
          const timing = performance.timing;
          const navigationStart = timing.navigationStart;
          
          // Track page load time
          const loadTime = timing.loadEventEnd - navigationStart;
          window.electron.send('analytics-timing', {
            name: 'page_load',
            category: 'performance',
            duration: loadTime
          });

          // Track DOM content loaded time
          const domContentLoaded = timing.domContentLoadedEventEnd - navigationStart;
          window.electron.send('analytics-timing', {
            name: 'dom_content_loaded',
            category: 'performance',
            duration: domContentLoaded
          });

          // Track first paint time
          if (performance.getEntriesByType('paint')) {
            const firstPaint = performance.getEntriesByType('paint')
              .find(entry => entry.name === 'first-paint');
            if (firstPaint) {
              window.electron.send('analytics-timing', {
                name: 'first_paint',
                category: 'performance',
                duration: firstPaint.startTime
              });
            }
          }
        }
      `);
    });
  }

  public trackEvent(
    category: string,
    action: string,
    label?: string,
    value?: number,
    properties?: Record<string, any>
  ) {
    const event: AnalyticsEvent = {
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      properties,
    };

    this.queue.push(event);
    this.checkQueueSize();
  }

  public trackPageView(
    url: string,
    title: string,
    referrer?: string,
    properties?: Record<string, any>
  ) {
    const pageView: PageView = {
      url,
      title,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      referrer,
      properties,
    };

    this.queue.push(pageView);
    this.checkQueueSize();
  }

  public trackTiming(
    name: string,
    category: string,
    duration: number,
    properties?: Record<string, any>
  ) {
    const timing: UserTiming = {
      name,
      category,
      duration,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
      properties,
    };

    this.queue.push(timing);
    this.checkQueueSize();
  }

  public setUserId(userId: string) {
    this.userId = userId;
  }

  private checkQueueSize() {
    if (this.queue.length >= this.batchSize) {
      this.flush();
    }
  }

  private startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  private async flush() {
    if (this.queue.length === 0) return;

    const batch = this.queue.splice(0, this.batchSize);

    try {
      // Here you would typically send the batch to your analytics service
      // For example, using a REST API or a dedicated analytics SDK
      logger.debug('Sending analytics batch:', batch);

      // Example implementation:
      // await fetch('https://your-analytics-api.com/events', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(batch),
      // });
    } catch (error) {
      logger.error('Failed to send analytics batch:', error);
      // Put the failed events back in the queue
      this.queue.unshift(...batch);
    }
  }

  public stop() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    this.flush();
  }
}

export const analytics = new Analytics();
