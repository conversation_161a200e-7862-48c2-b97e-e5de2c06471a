import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { BrowserSettings } from '../shared/types/settings';
import { IpcChannels } from '../shared/ipc-channels';
import { Bookmark } from '../shared/types/bookmarks';

export const api = {
  settings: {
    getAll: (): Promise<BrowserSettings> => ipcRenderer.invoke(IpcChannels.GET_ALL_SETTINGS),
    set: <K extends keyof BrowserSettings>(key: K, value: BrowserSettings[K]): Promise<void> =>
      ipcRenderer.invoke(IpcChannels.SET_SETTING, key, value),
  },
  bookmarks: {
    getAll: (): Promise<Bookmark[]> => ipcRenderer.invoke(IpcChannels.GET_ALL_BOOKMARKS),
    add: (title: string, url: string): Promise<Bookmark> =>
      ipcRenderer.invoke(IpcChannels.ADD_BOOKMARK, title, url),
    remove: (id: string): Promise<void> => ipcRenderer.invoke(IpcChannels.REMOVE_BOOKMARK, id),
  },
  updates: {
    onUpdateAvailable: (callback: () => void) => ipcRenderer.on(IpcChannels.UPDATE_AVAILABLE, callback),
    onUpdateDownloaded: (callback: () => void) => ipcRenderer.on(IpcChannels.UPDATE_DOWNLOADED, callback),
    quitAndInstall: (): void => ipcRenderer.send(IpcChannels.QUIT_AND_INSTALL_UPDATE),
  },
  // Other APIs (bookmarks, history, etc.) will be added here
};

try {
  contextBridge.exposeInMainWorld('electronAPI', api);
} catch (error) {
  console.error('Failed to expose preload API:', error);
}