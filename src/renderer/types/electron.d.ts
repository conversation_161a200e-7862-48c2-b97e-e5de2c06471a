interface ElectronAPI {
  openPath: (path: string) => Promise<void>;
  showItemInFolder: (path: string) => void;
  downloadFile: (url: string, filename: string) => Promise<string>;
  getDownloadPath: () => Promise<string>;
  setDownloadPath: (path: string) => Promise<void>;
  getAppVersion: () => Promise<string>;
  getPlatform: () => Promise<string>;
  isDev: () => boolean;
  openExternal: (url: string) => Promise<void>;
  showOpenDialog: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
  showSaveDialog: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
  showMessageBox: (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue>;
  showErrorBox: (title: string, content: string) => void;
  showNotification: (options: Electron.NotificationConstructorOptions) => void;
  getPath: (name: string) => Promise<string>;
  getAppPath: () => Promise<string>;
  getLocale: () => Promise<string>;
  getSystemLocale: () => Promise<string>;
  getSystemPreferences: () => Promise<Electron.SystemPreferences>;
  getGPUInfo: () => Promise<Electron.GPUInfo>;
  getProcessMemoryInfo: () => Promise<Electron.ProcessMemoryInfo>;
  getSystemMemoryInfo: () => Promise<Electron.SystemMemoryInfo>;
  getCPUUsage: () => Promise<Electron.CPUUsage>;
  getIOCounters: () => Promise<Electron.IOCounters>;
  getSystemVersion: () => Promise<string>;
  getSystemArchitecture: () => Promise<string>;
  getSystemPlatform: () => Promise<string>;
  getSystemHostname: () => Promise<string>;
  getSystemUsername: () => Promise<string>;
  getSystemHomeDir: () => Promise<string>;
  getSystemTempDir: () => Promise<string>;
  getSystemUserDataDir: () => Promise<string>;
  getSystemDownloadsDir: () => Promise<string>;
  getSystemDocumentsDir: () => Promise<string>;
  getSystemPicturesDir: () => Promise<string>;
  getSystemMusicDir: () => Promise<string>;
  getSystemVideosDir: () => Promise<string>;
  getSystemDesktopDir: () => Promise<string>;
  getSystemRecentDir: () => Promise<string>;
  getSystemTrashDir: () => Promise<string>;
  getSystemFontsDir: () => Promise<string>;
  getSystemLogsDir: () => Promise<string>;
  getSystemCacheDir: () => Promise<string>;
  getSystemCrashDumpsDir: () => Promise<string>;
  getSystemGPUCacheDir: () => Promise<string>;
  getSystemCodeCacheDir: () => Promise<string>;
  getSystemBlobStorageDir: () => Promise<string>;
  getSystemSessionStorageDir: () => Promise<string>;
  getSystemLocalStorageDir: () => Promise<string>;
  getSystemIndexedDBDir: () => Promise<string>;
  getSystemWebSQLDir: () => Promise<string>;
  getSystemCookiesDir: () => Promise<string>;
  getSystemPepperFlashDir: () => Promise<string>;
  getSystemPepperFlashVersion: () => Promise<string>;
  getSystemWidevineCdmDir: () => Promise<string>;
  getSystemWidevineCdmVersion: () => Promise<string>;
  getSystemNativeTheme: () => Promise<Electron.NativeTheme>;
  getSystemColorScheme: () => Promise<'light' | 'dark' | 'system'>;
  getSystemAccentColor: () => Promise<string>;
  getSystemHighlightColor: () => Promise<string>;
  getSystemInactiveSelectionBackgroundColor: () => Promise<string>;
  getSystemInactiveSelectionForegroundColor: () => Promise<string>;
  getSystemSelectionBackgroundColor: () => Promise<string>;
  getSystemSelectionForegroundColor: () => Promise<string>;
  getSystemWindowBackgroundColor: () => Promise<string>;
  getSystemWindowTextColor: () => Promise<string>;
  getSystemButtonBackgroundColor: () => Promise<string>;
  getSystemButtonTextColor: () => Promise<string>;
  getSystemButtonHoverBackgroundColor: () => Promise<string>;
  getSystemButtonHoverTextColor: () => Promise<string>;
  getSystemButtonPressedBackgroundColor: () => Promise<string>;
  getSystemButtonPressedTextColor: () => Promise<string>;
  getSystemButtonDisabledBackgroundColor: () => Promise<string>;
  getSystemButtonDisabledTextColor: () => Promise<string>;
  getSystemButtonBorderColor: () => Promise<string>;
  getSystemButtonHoverBorderColor: () => Promise<string>;
  getSystemButtonPressedBorderColor: () => Promise<string>;
  getSystemButtonDisabledBorderColor: () => Promise<string>;
  getSystemButtonFocusBorderColor: () => Promise<string>;
  getSystemButtonActiveBorderColor: () => Promise<string>;
  getSystemButtonInactiveBorderColor: () => Promise<string>;
  getSystemButtonDefaultBackgroundColor: () => Promise<string>;
  getSystemButtonDefaultTextColor: () => Promise<string>;
  getSystemButtonDefaultHoverBackgroundColor: () => Promise<string>;
  getSystemButtonDefaultHoverTextColor: () => Promise<string>;
  getSystemButtonDefaultPressedBackgroundColor: () => Promise<string>;
  getSystemButtonDefaultPressedTextColor: () => Promise<string>;
  getSystemButtonDefaultDisabledBackgroundColor: () => Promise<string>;
  getSystemButtonDefaultDisabledTextColor: () => Promise<string>;
  getSystemButtonDefaultBorderColor: () => Promise<string>;
  getSystemButtonDefaultHoverBorderColor: () => Promise<string>;
  getSystemButtonDefaultPressedBorderColor: () => Promise<string>;
  getSystemButtonDefaultDisabledBorderColor: () => Promise<string>;
  getSystemButtonDefaultFocusBorderColor: () => Promise<string>;
  getSystemButtonDefaultActiveBorderColor: () => Promise<string>;
  getSystemButtonDefaultInactiveBorderColor: () => Promise<string>;
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}
