import { Notification, NotificationAction, useNotificationStore } from './stores/useNotificationStore';

interface NotificationOptions {
  title: string;
  message?: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  source?: string;
  actions?: NotificationAction[];
}

class NotificationManager {
  public show(options: NotificationOptions): string {
    if (!options.title) {
      console.error('Notification title is required.');
      return '';
    }

    const newNotification: Notification = {
      id: `notification-${Date.now()}-${Math.random()}`,
      title: options.title,
      message: options.message,
      type: options.type || 'info',
      source: options.source || 'System',
      timestamp: Date.now(),
      read: false,
      actions: options.actions || [],
    };

    useNotificationStore.getState().addNotification(newNotification);

    return newNotification.id;
  }

  public initialize() {
    // Expose the manager to the window for extensions or legacy code
    (window as any).sendBrowserNotification = this.show.bind(this);
    console.log('NotificationManager initialized and exposed to window.');
  }
}

export default new NotificationManager();