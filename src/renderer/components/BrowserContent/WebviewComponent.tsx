import React, { useEffect, useRef } from 'react';

import { getService } from '../../serviceContainer';
import { TabInfo, useTabStore } from '../../stores/useTabStore';

interface WebviewComponentProps {
  tab: TabInfo;
}

const WebviewComponent: React.FC<WebviewComponentProps> = ({ tab }) => {
  const webviewRef = useRef<Electron.WebviewTag>(null);
  const { updateTab } = useTabStore.getState();
  const historyManager = getService<any>('historyManager');

  useEffect(() => {
    const webview = webviewRef.current;
    if (!webview) return;

    const handlePageTitleUpdated = (e: Electron.PageTitleUpdatedEvent) => {
      updateTab(tab.id, { title: e.title });
    };

    const handlePageFaviconUpdated = (e: Electron.PageFaviconUpdatedEvent) => {
      if (e.favicons && e.favicons.length > 0) {
        updateTab(tab.id, { favicon: e.favicons[0] });
      }
    };

    const handleDidStartLoading = () => {
      updateTab(tab.id, { isLoading: true });
    };

    const handleDidStopLoading = () => {
      if (webview) {
        const newUrl = webview.getURL();
        const newTitle = webview.getTitle();
        updateTab(tab.id, { isLoading: false, url: newUrl, title: newTitle });

        // Add to history, unless it's a blank page or an internal URL
        if (newUrl && newUrl !== 'about:blank' && !newUrl.startsWith('data:')) {
          // In a real app, we would also check for incognito mode here
          historyManager.addHistoryItem({
            url: newUrl,
            title: newTitle,
            timestamp: Date.now(),
          });
        }
      }
    };

    const handleContextMenu = (e: Electron.ContextMenuEvent) => {
      e.preventDefault();
      const { linkURL, srcURL, mediaType } = e.params;

      if (linkURL) {
        window.electronAPI.showContextMenu({ type: 'link', data: { url: linkURL } });
      } else if (srcURL && mediaType === 'image') {
        window.electronAPI.showContextMenu({ type: 'image', data: { url: srcURL } });
      } else {
        window.electronAPI.showContextMenu({ type: 'page', data: {} });
      }
    };

    webview.addEventListener('page-title-updated', handlePageTitleUpdated);
    webview.addEventListener('page-favicon-updated', handlePageFaviconUpdated);
    webview.addEventListener('did-start-loading', handleDidStartLoading);
    webview.addEventListener('did-stop-loading', handleDidStopLoading);
    webview.addEventListener('context-menu', handleContextMenu);

    return () => {
      webview.removeEventListener('page-title-updated', handlePageTitleUpdated);
      webview.removeEventListener('page-favicon-updated', handlePageFaviconUpdated);
      webview.removeEventListener('did-start-loading', handleDidStartLoading);
      webview.removeEventListener('did-stop-loading', handleDidStopLoading);
      webview.removeEventListener('context-menu', handleContextMenu);
    };
  }, [tab.id, updateTab]);

  return (
    <webview
      ref={webviewRef}
      id={`webview-${tab.id}`}
      src={tab.url}
      className={`webview ${tab.isActive ? 'active' : ''}`}
      preload={`../preload.js`}
    />
  );
};

export default React.memo(WebviewComponent);