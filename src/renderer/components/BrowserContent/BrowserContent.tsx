import React from 'react';

import { useTabStore } from '../../stores/useTabStore';
import WebviewComponent from './WebviewComponent';

const BrowserContent: React.FC = () => {
  const { tabs, activeTabId } = useTabStore();

  return (
    <div id="browser-content" className="browser-content">
      {tabs.map(tab => (
        <WebviewComponent key={tab.id} tab={{ ...tab, isActive: tab.id === activeTabId }} />
      ))}
    </div>
  );
};

export default BrowserContent;