import {
  Delete as Delete<PERSON>con,
  Folder as <PERSON>olderIcon,
  Pause as <PERSON>useIcon,
  PlayArrow as PlayIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  Paper,
  Typography,
} from '@mui/material';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import {
  clearCompletedDownloads,
  pauseDownload,
  removeDownload,
  startDownload,
} from '../../../store/slices/downloadSlice';
import { formatBytes, formatDuration } from '../../../utils/formatters';

const DownloadManager: React.FC = () => {
  const dispatch = useDispatch();
  const { downloads, activeDownloads } = useSelector((state: RootState) => state.downloads);

  const handleStartDownload = (id: string) => {
    dispatch(startDownload(id));
  };

  const handlePauseDownload = (id: string) => {
    dispatch(pauseDownload(id));
  };

  const handleRemoveDownload = (id: string) => {
    dispatch(removeDownload(id));
  };

  const handleClearCompleted = () => {
    dispatch(clearCompletedDownloads());
  };

  const handleOpenFolder = (savePath: string) => {
    // TODO: Implement opening the folder containing the downloaded file
    console.log('Opening folder:', savePath);
  };

  return (
    <Paper sx={{ p: 2, maxHeight: '80vh', overflow: 'auto' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Downloads</Typography>
        <Button
          variant="outlined"
          size="small"
          onClick={handleClearCompleted}
          disabled={!downloads.some(d => d.status === 'completed')}
        >
          Clear Completed
        </Button>
      </Box>
      <List>
        {downloads.map(download => (
          <ListItem
            key={download.id}
            sx={{
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              mb: 1,
              bgcolor: 'background.paper',
            }}
          >
            <ListItemText
              primary={
                <Typography variant="subtitle2" noWrap>
                  {download.filename}
                </Typography>
              }
              secondary={
                <Box sx={{ mt: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={download.progress}
                    sx={{ mb: 0.5 }}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="caption" color="text.secondary">
                      {formatBytes(download.receivedBytes)} / {formatBytes(download.totalBytes)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {download.status === 'downloading' && 'Downloading...'}
                      {download.status === 'paused' && 'Paused'}
                      {download.status === 'completed' && 'Completed'}
                      {download.status === 'error' && `Error: ${download.error}`}
                    </Typography>
                  </Box>
                </Box>
              }
            />
            <ListItemSecondaryAction>
              {download.status === 'completed' ? (
                <IconButton
                  edge="end"
                  aria-label="open folder"
                  onClick={() => handleOpenFolder(download.savePath)}
                >
                  <FolderIcon />
                </IconButton>
              ) : download.status === 'downloading' ? (
                <IconButton
                  edge="end"
                  aria-label="pause"
                  onClick={() => handlePauseDownload(download.id)}
                >
                  <PauseIcon />
                </IconButton>
              ) : (
                <IconButton
                  edge="end"
                  aria-label="start"
                  onClick={() => handleStartDownload(download.id)}
                  disabled={download.status === 'error'}
                >
                  <PlayIcon />
                </IconButton>
              )}
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => handleRemoveDownload(download.id)}
                sx={{ ml: 1 }}
              >
                <DeleteIcon />
              </IconButton>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
        {downloads.length === 0 && (
          <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 4 }}>
            No downloads
          </Typography>
        )}
      </List>
    </Paper>
  );
};

export default DownloadManager;
