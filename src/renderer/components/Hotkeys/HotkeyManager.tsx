import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Keyboard as KeyboardIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import {
  addHotkey,
  disableHotkey,
  enableHotkey,
  removeHotkey,
  setError,
  setHotkeys,
  setLoading,
  updateHotkey,
} from '../../../store/slices/hotkeySlice';
import { Hotkey, HotkeyCategory } from '../../hotkeys/types';

const HotkeyManager: React.FC = () => {
  const dispatch = useDispatch();
  const { hotkeys, enabledHotkeys, loading, error } = useSelector(
    (state: RootState) =>
      state.hotkeys as {
        hotkeys: Hotkey[];
        enabledHotkeys: string[];
        loading: boolean;
        error: string | null;
      }
  );
  const [openDialog, setOpenDialog] = useState(false);
  const [newHotkey, setNewHotkey] = useState<Partial<Hotkey>>({
    key: '',
    description: '',
    category: 'custom',
    enabled: true,
    global: false,
  });

  useEffect(() => {
    // Load hotkeys from storage
    const loadHotkeys = async () => {
      try {
        dispatch(setLoading(true));
        // TODO: Implement loading hotkeys from storage
        dispatch(setLoading(false));
      } catch (err) {
        dispatch(setError(err instanceof Error ? err.message : 'Failed to load hotkeys'));
        dispatch(setLoading(false));
      }
    };

    loadHotkeys();
  }, [dispatch]);

  const handleAddHotkey = () => {
    if (newHotkey.key && newHotkey.description) {
      const hotkey: Hotkey = {
        id: Date.now().toString(),
        key: newHotkey.key,
        description: newHotkey.description,
        category: newHotkey.category || 'custom',
        action: () => {}, // TODO: Implement action
        enabled: newHotkey.enabled || false,
        global: newHotkey.global || false,
      };

      dispatch(addHotkey(hotkey));
      setOpenDialog(false);
      setNewHotkey({
        key: '',
        description: '',
        category: 'custom',
        enabled: true,
        global: false,
      });
    }
  };

  const handleRemoveHotkey = (id: string) => {
    dispatch(removeHotkey(id));
  };

  const handleToggleHotkey = (id: string) => {
    if (enabledHotkeys.includes(id)) {
      dispatch(disableHotkey(id));
    } else {
      dispatch(enableHotkey(id));
    }
  };

  const getCategoryColor = (
    category: HotkeyCategory
  ): 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'default' => {
    const colors: Record<
      HotkeyCategory,
      'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'default'
    > = {
      navigation: 'primary',
      tabs: 'secondary',
      bookmarks: 'success',
      downloads: 'info',
      extensions: 'warning',
      search: 'error',
      window: 'default',
      custom: 'default',
    };
    return colors[category];
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={2}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">Hotkeys</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
        >
          Add Hotkey
        </Button>
      </Box>

      <List>
        {hotkeys.map((hotkey: Hotkey) => (
          <ListItem key={hotkey.id}>
            <ListItemText
              primary={
                <Box display="flex" alignItems="center">
                  <KeyboardIcon sx={{ mr: 1 }} />
                  <Typography variant="body1">{hotkey.description}</Typography>
                </Box>
              }
              secondary={
                <Box mt={1}>
                  <Chip label={hotkey.key} size="small" sx={{ mr: 1 }} />
                  <Chip
                    label={hotkey.category}
                    size="small"
                    color={getCategoryColor(hotkey.category)}
                  />
                  {hotkey.global && (
                    <Chip label="Global" size="small" color="info" sx={{ ml: 1 }} />
                  )}
                </Box>
              }
            />
            <ListItemSecondaryAction>
              <Switch
                edge="end"
                checked={enabledHotkeys.includes(hotkey.id)}
                onChange={() => handleToggleHotkey(hotkey.id)}
              />
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => handleRemoveHotkey(hotkey.id)}
              >
                <DeleteIcon />
              </IconButton>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Add New Hotkey</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                autoFocus
                margin="dense"
                label="Key Combination"
                fullWidth
                value={newHotkey.key}
                onChange={e => setNewHotkey({ ...newHotkey, key: e.target.value })}
                placeholder="e.g., Ctrl+Shift+T"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="dense"
                label="Description"
                fullWidth
                value={newHotkey.description}
                onChange={e => setNewHotkey({ ...newHotkey, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                select
                margin="dense"
                label="Category"
                fullWidth
                value={newHotkey.category}
                onChange={e =>
                  setNewHotkey({ ...newHotkey, category: e.target.value as HotkeyCategory })
                }
                SelectProps={{
                  native: true,
                }}
              >
                <option value="navigation">Navigation</option>
                <option value="tabs">Tabs</option>
                <option value="bookmarks">Bookmarks</option>
                <option value="downloads">Downloads</option>
                <option value="extensions">Extensions</option>
                <option value="search">Search</option>
                <option value="window">Window</option>
                <option value="custom">Custom</option>
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <Box display="flex" alignItems="center">
                <Switch
                  checked={newHotkey.global}
                  onChange={e => setNewHotkey({ ...newHotkey, global: e.target.checked })}
                />
                <Typography>Global Hotkey</Typography>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleAddHotkey} color="primary">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default HotkeyManager;
