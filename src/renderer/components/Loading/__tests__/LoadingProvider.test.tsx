import { render, screen } from '@testing-library/react';
import React from 'react';

import { LoadingProvider, useLoading } from '../LoadingProvider';

const TestComponent = () => {
  const { showLoading, hideLoading } = useLoading();
  return (
    <div>
      <button onClick={showLoading}>Show Loading</button>
      <button onClick={hideLoading}>Hide Loading</button>
    </div>
  );
};

describe('LoadingProvider', () => {
  it('renders children without loading indicator by default', () => {
    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Show Loading')).toBeInTheDocument();
    expect(screen.getByText('Hide Loading')).toBeInTheDocument();
  });

  it('shows loading indicator when showLoading is called', () => {
    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    screen.getByText('Show Loading').click();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('hides loading indicator when hideLoading is called', () => {
    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    screen.getByText('Show Loading').click();
    screen.getByText('Hide Loading').click();
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  });
});
