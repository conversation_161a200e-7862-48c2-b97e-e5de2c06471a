import { Box } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { RootState } from '../../../store';
import ThemeCustomizer from '../Theme/ThemeCustomizer';

const Browser: React.FC = () => {
  const isCustomizing = useSelector((state: RootState) => state.theme.isCustomizing);
  const { t } = useTranslation();

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {isCustomizing ? (
        <ThemeCustomizer />
      ) : (
        <Box sx={{ flex: 1, p: 2 }} role="main" aria-label={t('browser.title')}>
          {/* Browser content will go here */}
          <Box sx={{ typography: 'h4', mb: 2 }}>{t('browser.title')}</Box>
          <Box sx={{ typography: 'body1' }}>{t('browser.welcome')}</Box>
        </Box>
      )}
    </Box>
  );
};

export default Browser;
