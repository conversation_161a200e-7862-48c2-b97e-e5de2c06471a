import { Al<PERSON>, <PERSON><PERSON>T<PERSON><PERSON>, <PERSON>, Button, Fade } from '@mui/material';
import React, { useEffect, useState } from 'react';

import { Notification, useNotificationStore } from '../../stores/useNotificationStore';

const NotificationToast: React.FC<{ notification: Notification; onDismiss: (id: string) => void }> = ({ notification, onDismiss }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(notification.id);
    }, 5000); // Auto-dismiss after 5 seconds

    return () => clearTimeout(timer);
  }, [notification.id, onDismiss]);

  return (
    <Fade in>
      <Alert
        severity={notification.type}
        onClose={() => onDismiss(notification.id)}
        sx={{ width: '100%', mb: 1, boxShadow: 3 }}
        action={
          notification.actions && (
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mt: 1 }}>
              {notification.actions.map(action => (
                <Button key={action.id} color="inherit" size="small" onClick={action.callback}>
                  {action.title}
                </Button>
              ))}
            </Box>
          )
        }
      >
        <AlertTitle>{notification.title}</AlertTitle>
        {notification.message}
      </Alert>
    </Fade>
  );
};

const NotificationsContainer: React.FC = () => {
  const { notifications } = useNotificationStore();
  const [visibleNotifications, setVisibleNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    // Only show the latest few notifications as toasts
    setVisibleNotifications(notifications.slice(0, 3));
  }, [notifications]);

  const handleDismiss = (id: string) => {
    setVisibleNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <Box sx={{ position: 'fixed', top: 16, right: 16, width: 350, zIndex: 9999 }}>
      {visibleNotifications.map(n => (
        <NotificationToast key={n.id} notification={n} onDismiss={handleDismiss} />
      ))}
    </Box>
  );
};

export default NotificationsContainer;