import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Palette as PaletteIcon,
  Restore as RestoreIcon,
  Save as SaveIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Slider,
  Switch,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import { addNotification } from '../../../store/slices/notificationsSlice';
import {
  AnimationSettings,
  ColorScheme,
  CustomColors,
  ThemeSettings,
  TypographySettings,
  deletePreset,
  loadPreset,
  resetToDefault,
  savePreset,
  setBorderRadius,
  setColorScheme,
  setCustomColors,
  setIsCustomizing,
  setSpacing,
  setThemeMode,
  updateAnimation,
  updateShadows,
  updateTransitions,
  updateTypography,
} from '../../../store/slices/themeSlice';

import ColorPicker from './ColorPicker';

const ThemeCustomizer: React.FC = () => {
  const dispatch = useDispatch();
  const theme = useSelector((state: RootState) => state.theme.current);
  const presets = useSelector((state: RootState) => state.theme.presets);
  const [activeTab, setActiveTab] = useState(0);
  const [openPresetDialog, setOpenPresetDialog] = useState(false);
  const [newPresetName, setNewPresetName] = useState('');
  const [previewMode, setPreviewMode] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleThemeModeChange = (event: SelectChangeEvent) => {
    dispatch(setThemeMode(event.target.value as 'light' | 'dark' | 'system'));
  };

  const handleColorSchemeChange = (event: SelectChangeEvent) => {
    dispatch(setColorScheme(event.target.value as ColorScheme));
  };

  const handleCustomColorChange = (color: keyof CustomColors, value: string) => {
    dispatch(
      setCustomColors({
        ...theme.customColors,
        [color]: value,
      })
    );
  };

  const handleTypographyChange = (setting: keyof TypographySettings, value: any) => {
    dispatch(
      updateTypography({
        [setting]: value,
      })
    );
  };

  const handleAnimationChange = (setting: keyof AnimationSettings, value: any) => {
    dispatch(
      updateAnimation({
        [setting]: value,
      })
    );
  };

  const handleBorderRadiusChange = (event: Event, value: number | number[]) => {
    dispatch(setBorderRadius(value as number));
  };

  const handleSpacingChange = (event: Event, value: number | number[]) => {
    dispatch(setSpacing(value as number));
  };

  const handleShadowChange = (type: keyof ThemeSettings['shadows'], value: string) => {
    dispatch(
      updateShadows({
        [type]: value,
      })
    );
  };

  const handleTransitionChange = (type: keyof ThemeSettings['transitions'], value: string) => {
    dispatch(
      updateTransitions({
        [type]: value,
      })
    );
  };

  const handleSavePreset = () => {
    if (newPresetName) {
      dispatch(
        savePreset({
          name: newPresetName,
          settings: theme,
        })
      );
      dispatch(
        addNotification({
          type: 'success',
          title: 'Пресет сохранен',
          message: `Тема "${newPresetName}" успешно сохранена`,
          priority: 'medium',
          category: 'system',
        })
      );
      setOpenPresetDialog(false);
      setNewPresetName('');
    }
  };

  const handleLoadPreset = (name: string) => {
    dispatch(loadPreset(name));
    dispatch(
      addNotification({
        type: 'info',
        title: 'Пресет загружен',
        message: `Тема "${name}" успешно загружена`,
        priority: 'medium',
        category: 'system',
      })
    );
  };

  const handleDeletePreset = (name: string) => {
    dispatch(deletePreset(name));
    dispatch(
      addNotification({
        type: 'warning',
        title: 'Пресет удален',
        message: `Тема "${name}" успешно удалена`,
        priority: 'medium',
        category: 'system',
      })
    );
  };

  const handleReset = () => {
    dispatch(resetToDefault());
    dispatch(
      addNotification({
        type: 'info',
        title: 'Тема сброшена',
        message: 'Настройки темы восстановлены по умолчанию',
        priority: 'medium',
        category: 'system',
      })
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PaletteIcon />
          Настройка темы
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={previewMode ? <VisibilityOffIcon /> : <VisibilityIcon />}
            onClick={() => setPreviewMode(!previewMode)}
          >
            {previewMode ? 'Скрыть предпросмотр' : 'Предпросмотр'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={() => setOpenPresetDialog(true)}
          >
            Сохранить пресет
          </Button>
          <Button variant="outlined" startIcon={<RestoreIcon />} onClick={handleReset}>
            Сбросить
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={previewMode ? 8 : 12}>
          <Paper sx={{ p: 2 }}>
            <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab label="Основные" />
              <Tab label="Цвета" />
              <Tab label="Типография" />
              <Tab label="Анимации" />
              <Tab label="Пресеты" />
            </Tabs>

            {activeTab === 0 && (
              <Box>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Режим темы</InputLabel>
                  <Select value={theme.mode} onChange={handleThemeModeChange} label="Режим темы">
                    <MenuItem value="light">Светлая</MenuItem>
                    <MenuItem value="dark">Темная</MenuItem>
                    <MenuItem value="system">Системная</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Цветовая схема</InputLabel>
                  <Select
                    value={theme.colorScheme}
                    onChange={handleColorSchemeChange}
                    label="Цветовая схема"
                  >
                    <MenuItem value="default">По умолчанию</MenuItem>
                    <MenuItem value="blue">Синяя</MenuItem>
                    <MenuItem value="green">Зеленая</MenuItem>
                    <MenuItem value="purple">Фиолетовая</MenuItem>
                    <MenuItem value="orange">Оранжевая</MenuItem>
                    <MenuItem value="custom">Пользовательская</MenuItem>
                  </Select>
                </FormControl>

                <Typography gutterBottom>Скругление углов</Typography>
                <Slider
                  value={theme.borderRadius}
                  onChange={handleBorderRadiusChange}
                  min={0}
                  max={16}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  sx={{ mb: 2 }}
                />

                <Typography gutterBottom>Отступы</Typography>
                <Slider
                  value={theme.spacing}
                  onChange={handleSpacingChange}
                  min={4}
                  max={24}
                  step={2}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>
            )}

            {activeTab === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Пользовательские цвета
                </Typography>
                <Grid container spacing={2}>
                  {Object.entries(theme.customColors || {}).map(([key, value]) => (
                    <Grid item xs={12} sm={6} key={key}>
                      <ColorPicker
                        label={key}
                        value={value}
                        onChange={color =>
                          handleCustomColorChange(key as keyof CustomColors, color)
                        }
                      />
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {activeTab === 2 && (
              <Box>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Шрифт</InputLabel>
                  <Select
                    value={theme.typography.fontFamily}
                    onChange={e => handleTypographyChange('fontFamily', e.target.value)}
                    label="Шрифт"
                  >
                    <MenuItem value="Roboto, sans-serif">Roboto</MenuItem>
                    <MenuItem value="Arial, sans-serif">Arial</MenuItem>
                    <MenuItem value="Times New Roman, serif">Times New Roman</MenuItem>
                  </Select>
                </FormControl>

                <Typography gutterBottom>Базовый размер шрифта</Typography>
                <Slider
                  value={theme.typography.fontSize.base}
                  onChange={(e, value) =>
                    handleTypographyChange('fontSize', {
                      ...theme.typography.fontSize,
                      base: value as number,
                    })
                  }
                  min={12}
                  max={24}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  sx={{ mb: 2 }}
                />

                <Typography gutterBottom>Масштаб шрифта</Typography>
                <Slider
                  value={theme.typography.fontSize.scale}
                  onChange={(e, value) =>
                    handleTypographyChange('fontSize', {
                      ...theme.typography.fontSize,
                      scale: value as number,
                    })
                  }
                  min={1}
                  max={1.5}
                  step={0.1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>
            )}

            {activeTab === 3 && (
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={theme.animation.enabled}
                      onChange={e => handleAnimationChange('enabled', e.target.checked)}
                    />
                  }
                  label="Включить анимации"
                  sx={{ mb: 2 }}
                />

                <Typography gutterBottom>Длительность анимаций</Typography>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  {Object.entries(theme.animation.duration).map(([key, value]) => (
                    <Grid item xs={12} sm={4} key={key}>
                      <TextField
                        label={key}
                        type="number"
                        value={value}
                        onChange={e =>
                          handleAnimationChange('duration', {
                            ...theme.animation.duration,
                            [key]: parseInt(e.target.value),
                          })
                        }
                        fullWidth
                      />
                    </Grid>
                  ))}
                </Grid>

                <Typography gutterBottom>Тип анимации</Typography>
                <Grid container spacing={2}>
                  {Object.entries(theme.animation.easing).map(([key, value]) => (
                    <Grid item xs={12} sm={4} key={key}>
                      <TextField
                        label={key}
                        value={value}
                        onChange={e =>
                          handleAnimationChange('easing', {
                            ...theme.animation.easing,
                            [key]: e.target.value,
                          })
                        }
                        fullWidth
                      />
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {activeTab === 4 && (
              <Box>
                <List>
                  {Object.entries(presets).map(([name, settings]) => (
                    <ListItem key={name}>
                      <ListItemText
                        primary={name}
                        secondary={`Последнее изменение: ${new Date(settings.lastModified).toLocaleString()}`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleLoadPreset(name)}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton edge="end" onClick={() => handleDeletePreset(name)}>
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Paper>
        </Grid>

        {previewMode && (
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Предпросмотр
              </Typography>
              <Box
                sx={{
                  p: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: theme.borderRadius,
                }}
              >
                <Typography variant="h1" gutterBottom>
                  Заголовок H1
                </Typography>
                <Typography variant="h2" gutterBottom>
                  Заголовок H2
                </Typography>
                <Typography variant="h3" gutterBottom>
                  Заголовок H3
                </Typography>
                <Typography variant="body1" gutterBottom>
                  Обычный текст для демонстрации типографики и стилей.
                </Typography>
                <Button variant="contained" sx={{ mr: 1 }}>
                  Кнопка
                </Button>
                <Button variant="outlined">Кнопка</Button>
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>

      <Dialog open={openPresetDialog} onClose={() => setOpenPresetDialog(false)}>
        <DialogTitle>Сохранить пресет</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Название пресета"
            fullWidth
            value={newPresetName}
            onChange={e => setNewPresetName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenPresetDialog(false)}>Отмена</Button>
          <Button onClick={handleSavePreset} variant="contained">
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ThemeCustomizer;
