import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';

import { store } from '../../../../store';
import ThemeCustomizer from '../ThemeCustomizer';

describe('ThemeCustomizer', () => {
  it('renders without crashing', () => {
    render(
      <Provider store={store}>
        <ThemeCustomizer />
      </Provider>
    );
  });

  it('allows changing theme mode', () => {
    render(
      <Provider store={store}>
        <ThemeCustomizer />
      </Provider>
    );

    const modeSelect = screen.getByLabelText(/theme mode/i);
    fireEvent.change(modeSelect, { target: { value: 'dark' } });

    expect(modeSelect).toHaveValue('dark');
  });
});
