import BookmarkIcon from '@mui/icons-material/Bookmark';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import HistoryIcon from '@mui/icons-material/History';
import SettingsIcon from '@mui/icons-material/Settings';
import TabIcon from '@mui/icons-material/Tab';
import { Box, Fade, List, ListItem, ListItemIcon, ListItemText, Modal, TextField, Typography } from '@mui/material';
import React, { useEffect, useMemo, useState } from 'react';

import { getService } from '../../serviceContainer';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import { useHistoryStore } from '../../stores/useHistoryStore';
import { useSettingsStore } from '../../stores/useSettingsStore';
import { useTabStore } from '../../stores/useTabStore';
import { useUIStateStore } from '../../stores/useUIStateStore';
import Favicon from '../Tabs/Favicon';

// Define action types
interface Action {
  id: string;
  type: 'tab' | 'bookmark' | 'history' | 'command';
  title: string;
  group: string;
  icon: React.ReactNode;
  perform: () => void;
  keywords?: string;
}

const modalStyle = {
  display: 'flex',
  alignItems: 'flex-start',
  justifyContent: 'center',
  pt: '20vh',
};

const containerStyle = {
  width: 'clamp(300px, 60vw, 600px)',
  bgcolor: 'background.paper',
  boxShadow: 24,
  borderRadius: 2,
  p: 2,
  outline: 'none',
};

const CommandPalette: React.FC = () => {
  const { isCommandPaletteOpen, toggleCommandPalette } = useUIStateStore();
  const { tabs } = useTabStore();
  const { bookmarks } = useBookmarkStore();
  const { items: historyItems } = useHistoryStore();
  const tabManager = getService<any>('tabManager');
  const navigationManager = getService<any>('navigationManager');
  const historyManager = getService<any>('historyManager');
  const settingsManager = getService<any>('settingsManager');
  const { darkMode, toggleSettingsPanel } = useSettingsStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Memoize the full list of actions
  const allActions = useMemo<Action[]>(() => {
    const tabActions: Action[] = tabs.map(tab => ({ id: `tab-${tab.id}`, type: 'tab', title: tab.title, group: 'Открытые вкладки', icon: <Favicon url={tab.favicon} />, perform: () => tabManager.activateTab(tab.id), keywords: tab.url }));
    const bookmarkActions: Action[] = bookmarks.map(bookmark => ({ id: `bookmark-${bookmark.id}`, type: 'bookmark', title: bookmark.title, group: 'Закладки', icon: <BookmarkIcon fontSize="small" />, perform: () => navigationManager.navigate(bookmark.url), keywords: bookmark.url }));
    const historyActions: Action[] = historyItems.slice(0, 100).map(item => ({ id: `history-${item.id}`, type: 'history', title: item.title, group: 'История', icon: <HistoryIcon fontSize="small" />, perform: () => navigationManager.navigate(item.url), keywords: item.url }));
    const commandActions: Action[] = [
      { id: 'cmd-new-tab', type: 'command', title: 'Новая вкладка', group: 'Команды', icon: <TabIcon fontSize="small" />, perform: () => tabManager.createNewTab() },
      { id: 'cmd-settings', type: 'command', title: 'Открыть настройки', group: 'Команды', icon: <SettingsIcon fontSize="small" />, perform: toggleSettingsPanel },
      {
        id: 'cmd-toggle-dark-mode',
        type: 'command',
        title: 'Переключить темную тему',
        group: 'Команды',
        icon: <Brightness4Icon fontSize="small" />,
        perform: () => {
          settingsManager.saveSetting('darkMode', !darkMode);
        },
      },
      {
        id: 'cmd-clear-history-all',
        type: 'command',
        title: 'Очистить всю историю',
        group: 'Команды',
        icon: <DeleteSweepIcon fontSize="small" />,
        perform: () => {
          if (window.confirm('Вы уверены, что хотите очистить ВСЮ историю? Это действие необратимо.')) {
            historyManager.clearHistory();
          }
        },
      },
    ];
    return [...tabActions, ...commandActions, ...bookmarkActions, ...historyActions];
  }, [tabs, bookmarks, historyItems, tabManager, navigationManager, toggleSettingsPanel, darkMode, settingsManager, historyManager]);

  // Filter actions based on search term
  const filteredActions = useMemo(() => {
    if (!searchTerm) {
      return allActions.filter(a => a.type === 'tab' || a.type === 'command');
    }
    const lowercasedFilter = searchTerm.toLowerCase();
    return allActions.filter(action => action.title.toLowerCase().includes(lowercasedFilter) || action.keywords?.toLowerCase().includes(lowercasedFilter));
  }, [allActions, searchTerm]);

  // Group filtered actions
  const groupedActions = useMemo(() => {
    return filteredActions.reduce((acc, action) => {
      (acc[action.group] = acc[action.group] || []).push(action);
      return acc;
    }, {} as Record<string, Action[]>);
  }, [filteredActions]);

  const flattenedActions = useMemo(() => Object.values(groupedActions).flat(), [groupedActions]);

  // Reset state on open/close
  useEffect(() => {
    if (!isCommandPaletteOpen) {
      setSearchTerm('');
    }
    setSelectedIndex(0);
  }, [isCommandPaletteOpen]);

  const handleClose = () => {
    toggleCommandPalette();
  };

  const handleSelectAction = (action: Action) => {
    action.perform();
    handleClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => Math.min(prev + 1, flattenedActions.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (flattenedActions[selectedIndex]) {
        handleSelectAction(flattenedActions[selectedIndex]);
      }
    }
  };

  return (
    <Modal open={isCommandPaletteOpen} onClose={handleClose} sx={modalStyle} closeAfterTransition>
      <Fade in={isCommandPaletteOpen}>
        <Box sx={containerStyle} onKeyDown={handleKeyDown}>
          <TextField fullWidth autoFocus variant="outlined" placeholder="Введите команду или поисковый запрос..." value={searchTerm} onChange={e => { setSearchTerm(e.target.value); setSelectedIndex(0); }} />
          <List sx={{ maxHeight: 400, overflowY: 'auto', mt: 2 }}>
            {Object.entries(groupedActions).map(([group, actions]) => (
              <React.Fragment key={group}>
                <Typography variant="caption" color="text.secondary" sx={{ px: 2, pt: 1, display: 'block' }}>
                  {group}
                </Typography>
                {actions.map(action => (
                  <ListItem key={action.id} button selected={flattenedActions[selectedIndex]?.id === action.id} onClick={() => handleSelectAction(action)}>
                    <ListItemIcon sx={{ minWidth: 36 }}>{action.icon}</ListItemIcon>
                    <ListItemText primary={action.title} secondary={action.keywords} primaryTypographyProps={{ noWrap: true }} secondaryTypographyProps={{ noWrap: true }} />
                  </ListItem>
                ))}
              </React.Fragment>
            ))}
          </List>
        </Box>
      </Fade>
    </Modal>
  );
};

export default CommandPalette;