import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getService } from '../../serviceContainer';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import { useHistoryStore } from '../../stores/useHistoryStore';
import { useSettingsStore } from '../../stores/useSettingsStore';
import { useTabStore } from '../../stores/useTabStore';
import { useUIStateStore } from '../../stores/useUIStateStore';
import CommandPalette from './CommandPalette';

// Mock services
const mockTabManager = {
  activateTab: vi.fn(),
  createNewTab: vi.fn(),
};
const mockNavigationManager = {
  navigate: vi.fn(),
};
const mockSettingsManager = {
  saveSetting: vi.fn(),
};
const mockHistoryManager = {
  clearHistory: vi.fn(),
};

vi.mock('../../serviceContainer', () => ({
  getService: vi.fn(serviceName => {
    if (serviceName === 'tabManager') return mockTabManager;
    if (serviceName === 'navigationManager') return mockNavigationManager;
    if (serviceName === 'settingsManager') return mockSettingsManager;
    if (serviceName === 'historyManager') return mockHistoryManager;
    return {};
  }),
}));

// Mock data
const mockTabs = [
  { id: 'tab1', url: 'https://google.com', title: 'Google', favicon: null, isActive: true, isLoading: false },
  { id: 'tab2', url: 'https://github.com', title: 'GitHub', favicon: null, isActive: false, isLoading: false },
];
const mockBookmarks = [{ id: 'bm1', url: 'https://react.dev', title: 'React Docs', createdAt: Date.now() }];
const mockHistory = [{ id: 'hist1', url: 'https://vitejs.dev', title: 'Vite', timestamp: Date.now() }];

describe('CommandPalette', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset stores
    useUIStateStore.setState({ isCommandPaletteOpen: true, toggleCommandPalette: vi.fn() });
    useTabStore.setState({ tabs: mockTabs, activeTabId: 'tab1' });
    useBookmarkStore.setState({ bookmarks: mockBookmarks });
    useHistoryStore.setState({ items: mockHistory });
    useSettingsStore.setState({ darkMode: false, toggleSettingsPanel: vi.fn() });
  });

  it('renders when open and shows default actions', () => {
    render(<CommandPalette />);
    expect(screen.getByPlaceholderText('Введите команду или поисковый запрос...')).toBeInTheDocument();
    expect(screen.getByText('Google')).toBeInTheDocument(); // Tab
    expect(screen.getByText('Новая вкладка')).toBeInTheDocument(); // Command
    // Bookmarks and history should not be visible by default
    expect(screen.queryByText('React Docs')).not.toBeInTheDocument();
  });

  it('filters actions based on search term', () => {
    render(<CommandPalette />);
    const searchInput = screen.getByPlaceholderText('Введите команду или поисковый запрос...');
    fireEvent.change(searchInput, { target: { value: 'react' } });
    expect(screen.queryByText('Google')).not.toBeInTheDocument();
    expect(screen.getByText('React Docs')).toBeInTheDocument(); // Bookmark
  });

  it('executes a tab action on enter', () => {
    render(<CommandPalette />);
    const searchInput = screen.getByPlaceholderText('Введите команду или поисковый запрос...');
    fireEvent.change(searchInput, { target: { value: 'git' } }); // Should select GitHub tab
    fireEvent.keyDown(searchInput, { key: 'Enter' });
    expect(mockTabManager.activateTab).toHaveBeenCalledWith('tab2');
    expect(useUIStateStore.getState().toggleCommandPalette).toHaveBeenCalled();
  });

  it('executes a command action on click', () => {
    render(<CommandPalette />);
    fireEvent.click(screen.getByText('Новая вкладка'));
    expect(mockTabManager.createNewTab).toHaveBeenCalled();
    expect(useUIStateStore.getState().toggleCommandPalette).toHaveBeenCalled();
  });

  it('navigates up and down with arrow keys', () => {
    render(<CommandPalette />);
    const searchInput = screen.getByPlaceholderText('Введите команду или поисковый запрос...');

    // Initial state: first item is selected
    expect(screen.getByText('Google').closest('li')).toHaveClass('Mui-selected');

    // Navigate down
    fireEvent.keyDown(searchInput, { key: 'ArrowDown' });
    expect(screen.getByText('GitHub').closest('li')).toHaveClass('Mui-selected');
    expect(screen.getByText('Google').closest('li')).not.toHaveClass('Mui-selected');

    // Navigate up
    fireEvent.keyDown(searchInput, { key: 'ArrowUp' });
    expect(screen.getByText('Google').closest('li')).toHaveClass('Mui-selected');
    expect(screen.getByText('GitHub').closest('li')).not.toHaveClass('Mui-selected');
  });

  it('calls settingsManager to toggle dark mode', () => {
    render(<CommandPalette />);
    const searchInput = screen.getByPlaceholderText('Введите команду или поисковый запрос...');
    fireEvent.change(searchInput, { target: { value: 'темную' } });
    fireEvent.click(screen.getByText('Переключить темную тему'));
    expect(getService('settingsManager').saveSetting).toHaveBeenCalledWith('darkMode', true);
  });
});