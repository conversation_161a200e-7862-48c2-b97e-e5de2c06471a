import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getService } from '../../serviceContainer';
import { useHistoryStore } from '../../stores/useHistoryStore';
import HistoryPanel from './HistoryPanel';

const mockHistoryManager = {
  clearHistory: vi.fn(),
  clearHistoryByRange: vi.fn(),
};
const mockNavigationManager = {
  navigate: vi.fn(),
};

vi.mock('../../serviceContainer', () => ({
  getService: vi.fn(serviceName => {
    if (serviceName === 'historyManager') return mockHistoryManager;
    if (serviceName === 'navigationManager') return mockNavigationManager;
    return {};
  }),
}));

const mockItems = [
  { id: '1', url: 'https://google.com', title: 'Google Search', timestamp: Date.now() },
  { id: '2', url: 'https://github.com', title: 'GitHub', timestamp: Date.now() - 1000 },
  { id: '3', url: 'https://example.com', title: 'An Example Domain', timestamp: Date.now() - 2000 },
];

describe('HistoryPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the store state before each test
    useHistoryStore.setState({
      items: mockItems,
      isPanelOpen: true,
    });
  });

  it('should not render if the panel is closed', () => {
    useHistoryStore.setState({ isPanelOpen: false });
    const { container } = render(<HistoryPanel />);
    expect(container.firstChild).toBeNull();
  });

  it('renders all history items', () => {
    render(<HistoryPanel />);
    expect(screen.getByText('Google Search')).toBeInTheDocument();
    expect(screen.getByText('GitHub')).toBeInTheDocument();
    expect(screen.getByText('An Example Domain')).toBeInTheDocument();
  });

  it('filters items based on search term', () => {
    render(<HistoryPanel />);
    const searchInput = screen.getByPlaceholderText('Поиск по истории...');

    fireEvent.change(searchInput, { target: { value: 'git' } });

    expect(screen.queryByText('Google Search')).not.toBeInTheDocument();
    expect(screen.getByText('GitHub')).toBeInTheDocument();
    expect(screen.queryByText('An Example Domain')).not.toBeInTheDocument();
  });

  it('opens a menu on "Clear Data" click and calls clearHistoryByRange', () => {
    render(<HistoryPanel />);
    const clearButton = screen.getByText('Очистить данные');
    fireEvent.click(clearButton);

    const clearLastHour = screen.getByText('За последний час');
    fireEvent.click(clearLastHour);

    expect(mockHistoryManager.clearHistoryByRange).toHaveBeenCalledTimes(1);
    expect(mockHistoryManager.clearHistoryByRange).toHaveBeenCalledWith(expect.any(Number));
  });

  it('calls historyManager.clearHistory when "all time" is selected and confirmed', () => {
    window.confirm = vi.fn(() => true); // Mock window.confirm to return true
    render(<HistoryPanel />);
    const clearButton = screen.getByText('Очистить данные');
    fireEvent.click(clearButton);
    fireEvent.click(screen.getByText('За все время'));
    expect(mockHistoryManager.clearHistory).toHaveBeenCalledTimes(1);
  });

  it('calls navigationManager.navigate when an item is clicked', () => {
    render(<HistoryPanel />);
    const googleItem = screen.getByText('Google Search');
    fireEvent.click(googleItem);
    expect(mockNavigationManager.navigate).toHaveBeenCalledWith('https://google.com');
    // Also check that the panel closes
    expect(useHistoryStore.getState().isPanelOpen).toBe(false);
  });
});