import {
  Delete as DeleteIcon,
  History as HistoryIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  TextField,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import { clearHistory, removeHistoryItem } from '../../../store/slices/historySlice';

interface HistoryItem {
  id: string;
  url: string;
  title: string;
  timestamp: number;
  visitCount: number;
}

const History: React.FC = () => {
  const dispatch = useDispatch();
  const history = useSelector((state: RootState) => state.history.items);
  const [searchQuery, setSearchQuery] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);

  const filteredHistory = history.filter(
    item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.url.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, item: HistoryItem) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const handleRemoveItem = (id: string) => {
    dispatch(removeHistoryItem(id));
    handleMenuClose();
  };

  const handleClearHistory = () => {
    dispatch(clearHistory());
    handleMenuClose();
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">История</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            size="small"
            placeholder="Поиск в истории"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
          />
          <Button variant="outlined" color="error" onClick={handleClearHistory}>
            Очистить историю
          </Button>
        </Box>
      </Box>

      <List>
        {filteredHistory.map(item => (
          <ListItem
            key={item.id}
            secondaryAction={
              <IconButton edge="end" onClick={e => handleMenuOpen(e, item)}>
                <MoreVertIcon />
              </IconButton>
            }
          >
            <ListItemIcon>
              <HistoryIcon />
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              secondary={
                <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    {item.url}
                  </Typography>
                  <Chip size="small" label={formatDate(item.timestamp)} variant="outlined" />
                  <Chip size="small" label={`Посещений: ${item.visitCount}`} variant="outlined" />
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        <MenuItem onClick={() => selectedItem && handleRemoveItem(selectedItem.id)}>
          <DeleteIcon sx={{ mr: 1 }} />
          Удалить из истории
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default History;
