import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import SearchIcon from '@mui/icons-material/Search';
import { Box, Button, InputAdornment, ListItem, ListItemText, Menu, MenuItem, TextField, Typography } from '@mui/material';
import React, { useMemo, useState } from 'react';
import { FixedSizeList as List } from 'react-window';

import { getService } from '../../serviceContainer';
import { useDebounce } from '../../hooks/useDebounce';
import { useHistoryStore } from '../../stores/useHistoryStore';
import SidePanel from '../Layout/SidePanel';

const HistoryPanel: React.FC = () => {
  const { items, isPanelOpen, togglePanel } = useHistoryStore();
  const historyManager = getService<any>('historyManager');
  const navigationManager = getService<any>('navigationManager');

  if (!isPanelOpen) {
    return null;
  }

  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const filteredItems = useMemo(() => {
    if (!debouncedSearchTerm) {
      return items;
    }
    const lowercasedFilter = debouncedSearchTerm.toLowerCase();
    return items.filter(item => item.title.toLowerCase().includes(lowercasedFilter) || item.url.toLowerCase().includes(lowercasedFilter));
  }, [items, debouncedSearchTerm]);

  const handleNavigate = (url: string) => {
    navigationManager.navigate(url);
    togglePanel(); // Close panel on navigation
  };

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClickClearMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleCloseClearMenu = () => {
    setAnchorEl(null);
  };

  const handleClearHistory = (range: 'hour' | 'day' | 'week' | 'all') => {
    handleCloseClearMenu();
    const now = Date.now();
    let sinceTimestamp = 0;

    if (range === 'hour') sinceTimestamp = now - 60 * 60 * 1000;
    else if (range === 'day') sinceTimestamp = now - 24 * 60 * 60 * 1000;
    else if (range === 'week') sinceTimestamp = now - 7 * 24 * 60 * 60 * 1000;

    if (range === 'all') {
      if (window.confirm('Вы уверены, что хотите очистить ВСЮ историю? Это действие необратимо.')) {
        historyManager.clearHistory();
      }
    } else {
      historyManager.clearHistoryByRange(sinceTimestamp);
    }
  };

  return (
    <SidePanel title="История" isOpen={isPanelOpen} onClose={togglePanel}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <TextField
          fullWidth
          variant="outlined"
          size="small"
          placeholder="Поиск по истории..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start"><SearchIcon /></InputAdornment>,
          }}
        />
      </Box>
      <Box sx={{ flex: 1 }}>
        {filteredItems.length > 0 ? (
          <List height={400} itemCount={filteredItems.length} itemSize={56} width="100%">
            {({ index, style }) => {
              const item = filteredItems[index];
              return (
                <ListItem style={style} key={item.id} button onClick={() => handleNavigate(item.url)}>
                  <ListItemText
                    primary={item.title}
                    secondary={new URL(item.url).hostname}
                    sx={{ '& .MuiListItemText-secondary': { whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' } }}
                  />
                </ListItem>
              );
            }}
          </List>
        ) : (
          <Typography sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
            {searchTerm ? 'Ничего не найдено' : 'История просмотров пуста'}
          </Typography>
        )}
      </Box>
    </SidePanel>
  );
};

export default HistoryPanel;