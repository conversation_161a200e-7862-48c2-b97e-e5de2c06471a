import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getService } from '../../serviceContainer';
import { useSettingsStore } from '../../stores/useSettingsStore';
import SettingsPanel from './SettingsPanel';

const mockSettingsManager = {
  saveSetting: vi.fn(),
};

vi.mock('../../serviceContainer', () => ({
  getService: vi.fn(() => mockSettingsManager),
}));

const initialSettings = {
  darkMode: false,
  homepage: 'https://google.com',
  searchEngineUrl: 'https://google.com/search?q=',
  doNotTrack: false,
};

describe('SettingsPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    useSettingsStore.setState({
      ...initialSettings,
      isSettingsPanelOpen: true,
    });
  });

  it('should render the panel with the General tab active by default', () => {
    render(<SettingsPanel />);
    expect(screen.getByText('Настройки')).toBeInTheDocument();
    expect(screen.getByLabelText('Домашняя страница')).toBeInTheDocument();
    expect(screen.queryByLabelText('Тёмная тема')).not.toBeInTheDocument();
  });

  it('should switch to the Appearance tab and show its content', () => {
    render(<SettingsPanel />);
    fireEvent.click(screen.getByText('Внешний вид'));
    expect(screen.getByLabelText('Тёмная тема')).toBeInTheDocument();
    expect(screen.queryByLabelText('Домашняя страница')).not.toBeInTheDocument();
  });

  it('should have the Save button disabled initially', () => {
    render(<SettingsPanel />);
    const saveButton = screen.getByRole('button', { name: 'Сохранить' });
    expect(saveButton).toBeDisabled();
  });

  it('should enable the Save button when a setting is changed', async () => {
    render(<SettingsPanel />);
    const saveButton = screen.getByRole('button', { name: 'Сохранить' });
    expect(saveButton).toBeDisabled();

    const homepageInput = screen.getByLabelText('Домашняя страница');
    fireEvent.change(homepageInput, { target: { value: 'https://new-homepage.com' } });

    await waitFor(() => {
      expect(saveButton).toBeEnabled();
    });

    fireEvent.click(saveButton);
    expect(mockSettingsManager.saveSetting).toHaveBeenCalledWith('homepage', 'https://new-homepage.com');
    // It should only be called once because only one setting changed.
    expect(mockSettingsManager.saveSetting).toHaveBeenCalledTimes(1);
  });
});