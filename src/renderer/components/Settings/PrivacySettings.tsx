import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import React from 'react';

import { BrowserSettings } from '../../stores/useSettingsStore';

interface PrivacySettingsProps {
  settings: BrowserSettings;
  onSettingChange: <K extends keyof BrowserSettings>(key: K, value: BrowserSettings[K]) => void;
}

const PrivacySettings: React.FC<PrivacySettingsProps> = ({ settings, onSettingChange }) => {
  return (
    <FormGroup sx={{ gap: 2, mt: 2 }}>
      <FormControlLabel control={<Checkbox checked={settings.doNotTrack} onChange={e => onSettingChange('doNotTrack', e.target.checked)} />} label="Отправлять заголовок 'Do Not Track' с запросами" />
    </FormGroup>
  );
};

export default PrivacySettings;