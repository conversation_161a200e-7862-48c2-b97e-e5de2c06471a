import { Box, Button, Cir<PERSON>Progress, Tab, Tabs, Typography } from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { BrowserSettings, useSettingsStore } from '../../stores/useSettingsStore';
import AppearanceSettings from './AppearanceSettings';
import GeneralSettings from './GeneralSettings';
import PrivacySettings from './PrivacySettings';

const SettingsPanel: React.FC = () => {
  const { isSettingsPanelOpen, toggleSettingsPanel } = useSettingsStore();
  const queryClient = useQueryClient();

  const { data: currentSettings, isLoading } = useQuery({
    queryKey: ['settings'],
    queryFn: () => window.electronAPI.settings.getAll(),
    // Settings don't change often, so we can disable some refetches
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });

  // Local state for editing, to avoid spamming the global store on every input
  const [localSettings, setLocalSettings] = useState<BrowserSettings | undefined>(currentSettings);
  const [activeTab, setActiveTab] = useState(0);

  const hasChanges = useMemo(() => {
    if (!localSettings || !currentSettings) return false;
    return JSON.stringify(localSettings) !== JSON.stringify(currentSettings);
  }, [localSettings, currentSettings]);

  React.useEffect(() => {
    // Sync local state if the global state has changed (e.g., from the main process)
    setLocalSettings(currentSettings);
  }, [currentSettings, isSettingsPanelOpen]);

  const mutation = useMutation({
    mutationFn: async (changedSettings: Partial<BrowserSettings>) => {
      // Save only what has changed, in parallel
      const promises = Object.entries(changedSettings).map(([key, value]) =>
        window.electronAPI.settings.set(key as keyof BrowserSettings, value)
      );
      await Promise.all(promises);
    },
    onSuccess: () => {
      // Invalidate the cache so that useQuery gets fresh data
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      toggleSettingsPanel();
    },
    // An onError handler can be added to handle save errors
  });

  if (!isSettingsPanelOpen) {
    return null;
  }

  const handleSave = useCallback(() => {
    if (!localSettings || !currentSettings) return;

    // Find only the changed settings
    const changedSettings: Partial<BrowserSettings> = {};
    (Object.keys(localSettings) as Array<keyof BrowserSettings>).forEach(key => {
      if (localSettings[key] !== currentSettings[key]) {
        changedSettings[key] = localSettings[key];
      }
    });

    if (Object.keys(changedSettings).length > 0) {
      mutation.mutate(changedSettings);
    }
  }, [localSettings, currentSettings, mutation, toggleSettingsPanel]);

  const handleSettingChange = useCallback(<K extends keyof BrowserSettings>(key: K, value: BrowserSettings[K]) => {
    setLocalSettings(prev => (prev ? { ...prev, [key]: value } : undefined));
  }, []);

  const panelRef = useRef<HTMLDivElement>(null);
  // useOnClickOutside(panelRef, toggleSettingsPanel); // The hook can be left if it's defined elsewhere

  // Tab configuration for scalability
  const tabPanels = [
    {
      label: 'General',
      component: GeneralSettings,
      props: { settings: localSettings, onSettingChange: handleSettingChange },
    },
    {
      label: 'Appearance',
      component: AppearanceSettings,
      props: { settings: localSettings, onSettingChange: handleSettingChange },
    },
    {
      label: 'Privacy',
      component: PrivacySettings,
      props: { settings: localSettings, onSettingChange: handleSettingChange },
    },
  ];

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
      }}
    >
      <Box
        ref={panelRef}
        sx={{
          width: 'clamp(300px, 80vw, 600px)',
          maxHeight: '80vh',
          overflowY: 'auto',
          bgcolor: 'background.paper',
          p: 4,
          borderRadius: 2,
          boxShadow: 24,
        }}
      >
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        )}

        {!isLoading && localSettings && (
          <>
        <Typography variant="h4" gutterBottom>
          Settings
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={(_e, newValue) => setActiveTab(newValue)} aria-label="settings tabs">
            {tabPanels.map((panel, index) => (
              <Tab key={panel.label} label={panel.label} id={`tab-${index}`} aria-controls={`tabpanel-${index}`} />
            ))}
          </Tabs>
        </Box>

        {tabPanels.map((panel, index) => {
          const PanelComponent = panel.component;
          return (
            <div
              key={panel.label}
              role="tabpanel"
              hidden={activeTab !== index}
              id={`tabpanel-${index}`}
              aria-labelledby={`tab-${index}`}
            >
              {activeTab === index && <PanelComponent {...panel.props} />}
            </div>
          );
        })}

        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button variant="text" onClick={toggleSettingsPanel} disabled={mutation.isPending}>Cancel</Button>
              <Button variant="contained" onClick={handleSave} disabled={!hasChanges || mutation.isPending}>
                {mutation.isPending ? <CircularProgress size={24} /> : 'Save'}
              </Button>
        </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

export default SettingsPanel;