import { FormGroup, TextField } from '@mui/material';
import React from 'react';

import { BrowserSettings } from '../../stores/useSettingsStore';

interface GeneralSettingsProps {
  settings: BrowserSettings;
  onSettingChange: <K extends keyof BrowserSettings>(key: K, value: BrowserSettings[K]) => void;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({ settings, onSettingChange }) => {
  return (
    <FormGroup sx={{ gap: 2, mt: 2 }}>
      <TextField label="Домашняя страница" variant="outlined" fullWidth value={settings.homepage} onChange={e => onSettingChange('homepage', e.target.value)} />
      <TextField label="Поисковая система (URL)" variant="outlined" fullWidth value={settings.searchEngineUrl} onChange={e => onSettingChange('searchEngineUrl', e.target.value)} helperText="Используйте %s для поискового запроса" />
    </FormGroup>
  );
};

export default GeneralSettings;