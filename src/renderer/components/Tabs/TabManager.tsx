import { Add as AddIcon, Close as CloseIcon } from '@mui/icons-material';
import { Box, IconButton, Tab, Tabs, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import { addTab, closeTab, setActiveTab } from '../../../store/slices/tabSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const StyledTab = styled(Tab)(({ theme }) => ({
  minHeight: 48,
  textTransform: 'none',
  fontWeight: theme.typography.fontWeightRegular,
  fontSize: theme.typography.pxToRem(14),
  marginRight: theme.spacing(1),
  color: theme.palette.text.secondary,
  '&.Mui-selected': {
    color: theme.palette.primary.main,
  },
  '&.Mui-focusVisible': {
    backgroundColor: theme.palette.action.focus,
  },
}));

const TabManager: React.FC = () => {
  const dispatch = useDispatch();
  const { tabs, activeTabIndex } = useSelector((state: RootState) => state.tabs);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    dispatch(setActiveTab(newValue));
  };

  const handleAddTab = () => {
    dispatch(addTab({ url: 'about:blank', title: 'New Tab' }));
  };

  const handleCloseTab = (index: number) => {
    dispatch(closeTab(index));
  };

  return (
    <Box sx={{ width: '100%', bgcolor: 'background.paper' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', display: 'flex', alignItems: 'center' }}>
        <Tabs
          value={activeTabIndex}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          aria-label="browser tabs"
          sx={{ flexGrow: 1 }}
        >
          {tabs.map((tab, index) => (
            <StyledTab
              key={index}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body2" noWrap sx={{ maxWidth: 150 }}>
                    {tab.title}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={e => {
                      e.stopPropagation();
                      handleCloseTab(index);
                    }}
                    sx={{ ml: 1 }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Box>
              }
            />
          ))}
        </Tabs>
        <IconButton onClick={handleAddTab} sx={{ ml: 1 }}>
          <AddIcon />
        </IconButton>
      </Box>
      {tabs.map((tab, index) => (
        <TabPanel key={index} value={activeTabIndex} index={index}>
          <iframe
            src={tab.url}
            style={{ width: '100%', height: 'calc(100vh - 120px)', border: 'none' }}
            title={tab.title}
          />
        </TabPanel>
      ))}
    </Box>
  );
};

export default TabManager;
