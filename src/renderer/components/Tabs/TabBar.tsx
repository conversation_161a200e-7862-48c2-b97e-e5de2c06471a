import React from 'react';

import { getService } from '../../serviceContainer';
import { useTabStore } from '../../stores/useTabStore';
import TabComponent from './TabComponent';

const TabBar: React.FC = () => {
  // Получаем состояние напрямую из store с помощью хука. Компонент будет перерисовываться автоматически.
  const { tabs } = useTabStore();

  const handleNewTab = () => {
    const tabManager = getService('tabManager');
    tabManager.createNewTab(undefined, true);
  };

  return (
    <div className="tabs-container">
      <div id="tabs-bar">
        {tabs.map(tab => (
          <TabComponent key={tab.id} {...tab} />
        ))}
      </div>
      <button id="new-tab-button" onClick={handleNewTab} title="Новая вкладка">
        +
      </button>
    </div>
  );
};

export default TabBar;