import React from 'react';

import { getService } from '../../serviceContainer';
import Favicon from './Favicon';

interface TabProps {
  id: string;
  title: string;
  favicon: string | null;
  isActive: boolean;
  isLoading: boolean;
}

const TabComponent: React.FC<TabProps> = ({ id, title, favicon, isActive, isLoading }) => {
  // Получаем доступ к менеджеру для вызова его методов
  const tabManager = getService('tabManager');

  const handleClick = () => {
    if (!isActive) {
      tabManager.activateTab(id);
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation(); // Предотвращаем клик по вкладке
    tabManager.closeTab(id);
  };

  const handleAuxClick = (e: React.MouseEvent) => {
    // 1 is for middle-click
    if (e.button === 1) {
      handleClose(e);
    }
  };

  const className = `tab ${isActive ? 'active' : ''} ${isLoading ? 'loading' : ''}`;
  
  return (
    <div id={`tab-${id}`} className={className} onClick={handleClick} onAuxClick={handleAuxClick} title={title}>
      {isLoading && <div className="loading-spinner" />}
      {!isLoading && <Favicon url={favicon} />}
      <span className="tab-title">{title}</span>
      <button className="tab-close" onClick={handleClose} aria-label={`Close tab ${title}`}>
        ×
      </button>
    </div>
  );
};

export default TabComponent;