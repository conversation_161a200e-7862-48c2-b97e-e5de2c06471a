import { Box, Button, Typography } from '@mui/material';
import React, { Component, ErrorInfo, ReactNode } from 'react';

// Предполагается, что у нас есть доступ к сервисам через getService
// import { getService } from '../serviceContainer';
// const localizationManager = getService('localizationManager');
// const monitoringService = getService('monitoringService');
// const navigationManager = getService('navigationManager');

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });

    // Отправляем отчет об ошибке в систему мониторинга
    // monitoringService.reportError(error, errorInfo);
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoBack = () => {
    // Безопасно пытаемся вернуться назад
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // Если истории нет, перезагружаем, чтобы сбросить состояние
      this.handleReload();
    }
    // Сбрасываем состояние ошибки после попытки навигации
    this.setState({
      hasError: false,
      error,
      errorInfo,
    });
  }

  public render() {
    if (this.state.hasError) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            p: 3,
            textAlign: 'center',
          }}
        >
          <Typography variant="h4" gutterBottom>
            {/* {localizationManager.t('errorBoundary.title', 'Что-то пошло не так')} */}
            Что-то пошло не так.
          </Typography>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            {/* {localizationManager.t('errorBoundary.message', 'Произошла непредвиденная ошибка.')} */}
            Произошла непредвиденная ошибка.
          </Typography>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <Box
              component="details"
              sx={{ mt: 2, p: 2, bgcolor: 'rgba(255, 0, 0, 0.1)', borderRadius: 1, textAlign: 'left', maxWidth: '80vw' }}
            >
              <summary>
                <Typography variant="body2" color="error.dark">
                  {this.state.error.toString()}
                </Typography>
              </summary>
              {this.state.errorInfo && (
                <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', marginTop: '8px', fontSize: '12px' }}>
                  <code>{this.state.errorInfo.componentStack}</code>
                </pre>
              )}
            </Box>
          )}
          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button variant="outlined" color="secondary" onClick={this.handleGoBack}>
              {/* {localizationManager.t('errorBoundary.goBack', 'Вернуться назад')} */}
              Вернуться назад
            </Button>
            <Button variant="contained" color="primary" onClick={this.handleReload}>
              {/* {localizationManager.t('errorBoundary.reload', 'Перезагрузить страницу')} */}
              Перезагрузить страницу
            </Button>
          </Box>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
