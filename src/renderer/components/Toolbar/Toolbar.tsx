import React, { useState, useEffect } from 'react';
import { IconButton, Typography } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import RefreshIcon from '@mui/icons-material/Refresh';
import CloseIcon from '@mui/icons-material/Close';
import BookmarksIcon from '@mui/icons-material/Bookmarks';
import HistoryIcon from '@mui/icons-material/History';
import SettingsIcon from '@mui/icons-material/Settings';

import { getService } from '../../serviceContainer';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import { useHistoryStore } from '../../stores/useHistoryStore';
import { useNavigationStore } from '../../stores/useNavigationStore';
import { useUIStateStore } from '../../stores/useUIStateStore';
import { useSettingsStore } from '../../stores/useSettingsStore';
import BookmarkButton from '../Bookmarks/BookmarkButton';

const Toolbar: React.FC = () => {
  const navigationManager = getService('navigationManager');
  const { canGoBack, canGoForward, isLoading, url } = useNavigationStore();
  const { isPanelOpen: isBookmarksOpen, togglePanel: toggleBookmarksPanel } = useBookmarkStore();
  const { isPanelOpen: isHistoryOpen, togglePanel: toggleHistoryPanel } = useHistoryStore();
  const { toggleCommandPalette } = useUIStateStore();
  const { isSettingsPanelOpen, toggleSettingsPanel } = useSettingsStore();

  // Локальное состояние для инпута, чтобы не перерисовывать на каждый ввод
  const [inputValue, setInputValue] = useState(url);

  // Синхронизируем инпут с URL из стора, когда он меняется (например, при навигации по ссылке)
  useEffect(() => {
    if (document.activeElement?.id !== 'url-input') {
      setInputValue(url);
    }
  }, [url]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      navigationManager.navigate(inputValue.trim());
      (event.target as HTMLInputElement).blur();
    }
  };

  return (
    <div className="browser-toolbar">
      <IconButton id="back-button" title="Назад" disabled={!canGoBack} onClick={navigationManager.goBack}>
        <ArrowBackIcon />
      </IconButton>
      <IconButton id="forward-button" title="Вперед" disabled={!canGoForward} onClick={navigationManager.goForward}>
        <ArrowForwardIcon />
      </IconButton>
      <IconButton id="reload-button" title={isLoading ? 'Остановить' : 'Обновить'} onClick={navigationManager.reloadOrStop}>
        {isLoading ? <CloseIcon /> : <RefreshIcon />}
      </IconButton>
      <div className="url-input-container" style={{ flex: 1, position: 'relative' }}>
        <input id="url-input" type="text" placeholder="Введите URL или поисковый запрос" value={inputValue} onChange={e => setInputValue(e.target.value)} onKeyDown={handleKeyDown} />
        <BookmarkButton />
      </div>
      <IconButton id="command-palette-button" title="Командная палитра (Ctrl+K)" onClick={toggleCommandPalette}>
        <Typography sx={{ fontFamily: 'monospace', fontWeight: 'bold', lineHeight: 1 }}>⌘</Typography>
      </IconButton>
      <IconButton id="bookmarks-panel-button" title="Закладки" onClick={toggleBookmarksPanel} data-active={isBookmarksOpen}>
        <BookmarksIcon />
      </IconButton>
      <IconButton id="history-panel-button" title="История" onClick={toggleHistoryPanel} data-active={isHistoryOpen}>
        <HistoryIcon />
      </IconButton>
      <IconButton id="settings-button" title="Настройки" onClick={toggleSettingsPanel} data-active={isSettingsPanelOpen}>
        <SettingsIcon />
      </IconButton>
    </div>
  );
};

export default Toolbar;