import React, { useEffect, useLayoutEffect } from 'react';

import { getService } from '../../serviceContainer';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import { useHistoryStore } from '../../stores/useHistoryStore';
import { useSettingsStore } from '../../stores/useSettingsStore';
import { useTabStore } from '../../stores/useTabStore';
import NotificationsContainer from '../Notifications/Notifications';
import HistoryPanel from '../History/HistoryPanel';
import BookmarksPanel from '../Bookmarks/BookmarksPanel';
import BrowserContent from '../BrowserContent/BrowserContent';
import SettingsPanel from '../Settings/SettingsPanel';
import TabBar from '../Tabs/TabBar';
import Toolbar from '../Toolbar/Toolbar';

const BrowserLayout: React.FC = () => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Use metaKey for macOS (Command key) and ctrlKey for Windows/Linux
      const isModifier = e.metaKey || e.ctrlKey;

      if (isModifier && e.key === 't') {
        e.preventDefault();
        getService<any>('tabManager').createNewTab();
      }

      if (isModifier && e.key === 'w') {
        e.preventDefault();
        const activeTabId = useTabStore.getState().activeTabId;
        if (activeTabId) {
          getService<any>('tabManager').closeTab(activeTabId);
        }
      }

      if (isModifier && e.key === ',') {
        e.preventDefault();
        useSettingsStore.getState().toggleSettingsPanel();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  useEffect(() => {
    const removeContextMenuListener = window.electronAPI.onContextMenuCommand(command => {
      if (command.command === 'open-link-in-new-tab' && command.url) {
        getService<any>('tabManager').createNewTab(command.url, false); // open in background
      }
    });

    return () => {
      removeContextMenuListener();
    };
  }, []);

  return (
    <div className="browser-container">
      <header className="browser-header">
        <TabBar />
        <Toolbar />
      </header>
      <main style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <BrowserContent />
      </main>
      <NotificationsContainer />
      <HistoryPanel />
      <BookmarksPanel />
      <SettingsPanel />
    </div>
  );
};

export default BrowserLayout;