import { Box, Button, Typography } from '@mui/material';
import React, { ReactNode } from 'react';

interface SidePanelProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  zIndex?: number;
}

const SidePanel: React.FC<SidePanelProps> = ({ title, isOpen, onClose, children, zIndex = 998 }) => {
  if (!isOpen) {
    return null;
  }

  return (
    <Box
      sx={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0, 0, 0, 0.2)', zIndex }}
      onClick={onClose}
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50px', // Adjust based on toolbar height
          right: 0,
          width: '350px',
          height: 'calc(100vh - 50px)',
          bgcolor: 'background.paper',
          boxShadow: '0 0 15px rgba(0,0,0,0.2)',
          display: 'flex',
          flexDirection: 'column',
        }}
        onClick={e => e.stopPropagation()}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">{title}</Typography>
          <Button onClick={onClose}>Закрыть</Button>
        </Box>
        {children}
      </Box>
    </Box>
  );
};

export default SidePanel;