import React from 'react';

import { getService } from '../../serviceContainer';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import { useNavigationStore } from '../../stores/useNavigationStore';

const BookmarkButton: React.FC = () => {
  const { url } = useNavigationStore();
  const { bookmarks } = useBookmarkStore();
  const notificationManager = getService<any>('notificationManager');
  const bookmarkManager = getService<any>('bookmarkManager');

  const isBookmarked = url ? bookmarks.some(b => b.url === url) : false;

  const handleClick = () => {
    if (!url || url === 'about:blank') return;

    if (isBookmarked) {
      const bookmark = bookmarks.find(b => b.url === url);
      if (bookmark) {
        bookmarkManager.removeBookmark(bookmark.id);
        notificationManager.show({ title: 'Закладка удалена', type: 'info' });
      }
    } else {
      bookmarkManager.addBookmark(url);
      notificationManager.show({ title: 'Закладка добавлена', type: 'success' });
    }
  };

  return (
    <button id="bookmark-button" title={isBookmarked ? 'Удалить закладку' : 'Добавить в закладки'} onClick={handleClick} disabled={!url || url === 'about:blank'}>
      {isBookmarked ? '★' : '☆'}
    </button>
  );
};

export default BookmarkButton;