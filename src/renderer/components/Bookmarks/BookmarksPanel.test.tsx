import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getService } from '../../serviceContainer';
import { useBookmarkStore } from '../../stores/useBookmarkStore';
import BookmarksPanel from './BookmarksPanel';

const mockBookmarkManager = {
  removeBookmark: vi.fn(),
};
const mockNavigationManager = {
  navigate: vi.fn(),
};

vi.mock('../../serviceContainer', () => ({
  getService: vi.fn(serviceName => {
    if (serviceName === 'bookmarkManager') return mockBookmarkManager;
    if (serviceName === 'navigationManager') return mockNavigationManager;
    return {};
  }),
}));

const mockItems = [
  { id: '1', url: 'https://react.dev', title: 'React Official Docs', createdAt: Date.now() },
  { id: '2', url: 'https://vitejs.dev', title: 'Vite | Next Generation Frontend Tooling', createdAt: Date.now() - 1000 },
];

describe('BookmarksPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    useBookmarkStore.setState({
      bookmarks: mockItems,
      isPanelOpen: true,
    });
  });

  it('should not render if the panel is closed', () => {
    useBookmarkStore.setState({ isPanelOpen: false });
    const { container } = render(<BookmarksPanel />);
    expect(container.firstChild).toBeNull();
  });

  it('renders all bookmark items', () => {
    render(<BookmarksPanel />);
    expect(screen.getByText('React Official Docs')).toBeInTheDocument();
    expect(screen.getByText('Vite | Next Generation Frontend Tooling')).toBeInTheDocument();
  });

  it('filters bookmarks based on search term', () => {
    render(<BookmarksPanel />);
    const searchInput = screen.getByPlaceholderText('Поиск по закладкам...');

    fireEvent.change(searchInput, { target: { value: 'vite' } });

    expect(screen.queryByText('React Official Docs')).not.toBeInTheDocument();
    expect(screen.getByText('Vite | Next Generation Frontend Tooling')).toBeInTheDocument();
  });

  it('calls bookmarkManager.removeBookmark when delete button is clicked', () => {
    render(<BookmarksPanel />);
    const deleteButtons = screen.getAllByLabelText('delete');
    fireEvent.click(deleteButtons[0]); // Click delete on the first item ("React")
    expect(mockBookmarkManager.removeBookmark).toHaveBeenCalledWith('1');
  });

  it('calls navigationManager.navigate when an item is clicked', () => {
    render(<BookmarksPanel />);
    const viteItem = screen.getByText('Vite | Next Generation Frontend Tooling');
    fireEvent.click(viteItem);
    expect(mockNavigationManager.navigate).toHaveBeenCalledWith('https://vitejs.dev');
    expect(useBookmarkStore.getState().isPanelOpen).toBe(false);
  });
});