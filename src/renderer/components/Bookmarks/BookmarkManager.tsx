import { Add as AddIcon, Delete as DeleteIcon, Folder as FolderIcon } from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import {
  addBookmark,
  addFolder,
  moveBookmark,
  removeBookmark,
  removeFolder,
  updateBookmark,
} from '../../../store/slices/bookmarkSlice';

const BookmarkManager: React.FC = () => {
  const dispatch = useDispatch();
  const { bookmarks, folders } = useSelector((state: RootState) => state.bookmarks);
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [folder, setFolder] = useState(folders[0] || 'Bookmarks Bar');
  const [newFolder, setNewFolder] = useState('');

  const handleAddBookmark = () => {
    if (title && url) {
      dispatch(addBookmark({ title, url, folder }));
      setTitle('');
      setUrl('');
      setOpen(false);
    }
  };

  const handleAddFolder = () => {
    if (newFolder) {
      dispatch(addFolder(newFolder));
      setNewFolder('');
    }
  };

  const handleRemoveBookmark = (id: string) => {
    dispatch(removeBookmark(id));
  };

  const handleRemoveFolder = (folderName: string) => {
    dispatch(removeFolder(folderName));
  };

  const handleMoveBookmark = (id: string, newFolder: string) => {
    dispatch(moveBookmark({ id, folder: newFolder }));
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Bookmarks</Typography>
        <Button variant="contained" startIcon={<AddIcon />} onClick={() => setOpen(true)}>
          Add Bookmark
        </Button>
      </Box>
      <Box sx={{ mb: 2 }}>
        <FormControl fullWidth>
          <InputLabel>Add Folder</InputLabel>
          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <TextField
              size="small"
              label="Folder Name"
              value={newFolder}
              onChange={e => setNewFolder(e.target.value)}
            />
            <Button variant="outlined" onClick={handleAddFolder}>
              Add
            </Button>
          </Box>
        </FormControl>
      </Box>
      {folders.map(folderName => (
        <Box key={folderName} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <FolderIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1">{folderName}</Typography>
            {folderName !== 'Bookmarks Bar' && folderName !== 'Other Bookmarks' && (
              <IconButton size="small" onClick={() => handleRemoveFolder(folderName)}>
                <DeleteIcon fontSize="small" />
              </IconButton>
            )}
          </Box>
          <List dense>
            {bookmarks
              .filter(b => b.folder === folderName)
              .map(b => (
                <ListItem key={b.id} sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <ListItemText
                    primary={
                      <a href={b.url} target="_blank" rel="noopener noreferrer">
                        {b.title}
                      </a>
                    }
                    secondary={b.url}
                  />
                  <FormControl size="small" sx={{ minWidth: 120, mr: 1 }}>
                    <Select
                      value={b.folder || folders[0]}
                      onChange={e => handleMoveBookmark(b.id, e.target.value as string)}
                    >
                      {folders.map(f => (
                        <MenuItem key={f} value={f}>
                          {f}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleRemoveBookmark(b.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            {bookmarks.filter(b => b.folder === folderName).length === 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ pl: 2, py: 1 }}>
                No bookmarks
              </Typography>
            )}
          </List>
        </Box>
      ))}
      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Add Bookmark</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Title"
            fullWidth
            value={title}
            onChange={e => setTitle(e.target.value)}
          />
          <TextField
            margin="dense"
            label="URL"
            fullWidth
            value={url}
            onChange={e => setUrl(e.target.value)}
          />
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Folder</InputLabel>
            <Select
              value={folder}
              label="Folder"
              onChange={e => setFolder(e.target.value as string)}
            >
              {folders.map(f => (
                <MenuItem key={f} value={f}>
                  {f}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button onClick={handleAddBookmark} variant="contained">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BookmarkManager;
