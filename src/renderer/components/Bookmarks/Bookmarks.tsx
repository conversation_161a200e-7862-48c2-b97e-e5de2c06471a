import {
  AccessTime as AccessTimeIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Bookmark as BookmarkIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Folder as FolderIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  StarBorder as StarBorderIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  Menu,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import {
  addBookmark,
  addBookmarkFolder,
  removeBookmark,
  removeBookmarkFolder,
  toggleBookmarkFavorite,
  updateBookmark,
  updateBookmarkFolder,
} from '../../../store/slices/bookmarksSlice';

interface Bookmark {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  folderId?: string;
  isFavorite: boolean;
  lastVisited?: number;
  tags?: string[];
  description?: string;
}

interface BookmarkFolder {
  id: string;
  name: string;
  parentId?: string;
  isExpanded: boolean;
}

const Bookmarks: React.FC = () => {
  const dispatch = useDispatch();
  const bookmarks = useSelector((state: RootState) => state.bookmarks.items);
  const folders = useSelector((state: RootState) => state.bookmarks.folders);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedBookmark, setSelectedBookmark] = useState<Bookmark | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [newBookmark, setNewBookmark] = useState({
    title: '',
    url: '',
    folderId: '',
    tags: '',
    description: '',
  });

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, bookmark: Bookmark) => {
    setAnchorEl(event.currentTarget);
    setSelectedBookmark(bookmark);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedBookmark(null);
  };

  const handleRemoveBookmark = (id: string) => {
    dispatch(removeBookmark(id));
    handleMenuClose();
  };

  const handleToggleFavorite = (id: string) => {
    dispatch(toggleBookmarkFavorite(id));
  };

  const handleAddBookmark = () => {
    if (newBookmark.title && newBookmark.url) {
      dispatch(
        addBookmark({
          ...newBookmark,
          id: Date.now().toString(),
          isFavorite: false,
          lastVisited: Date.now(),
          tags: newBookmark.tags ? newBookmark.tags.split(',').map(tag => tag.trim()) : [],
        })
      );
      setOpenDialog(false);
      setNewBookmark({
        title: '',
        url: '',
        folderId: '',
        tags: '',
        description: '',
      });
    }
  };

  const handleToggleFolder = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    if (folder) {
      dispatch(
        updateBookmarkFolder({
          ...folder,
          isExpanded: !folder.isExpanded,
        })
      );
    }
  };

  const filteredBookmarks = bookmarks.filter(bookmark => {
    const matchesSearch =
      searchQuery === '' ||
      bookmark.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bookmark.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (bookmark.tags &&
        bookmark.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));
    return matchesSearch;
  });

  const getFolderBookmarks = (folderId?: string) => {
    return filteredBookmarks.filter(bookmark => bookmark.folderId === folderId);
  };

  const getSubFolders = (parentId?: string) => {
    return folders.filter(folder => folder.parentId === parentId);
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  const renderFolder = (folder: BookmarkFolder) => {
    const subFolders = getSubFolders(folder.id);
    const folderBookmarks = getFolderBookmarks(folder.id);

    return (
      <Box key={folder.id} sx={{ ml: 2 }}>
        <ListItem button onClick={() => handleToggleFolder(folder.id)}>
          <ListItemIcon>
            <FolderIcon />
          </ListItemIcon>
          <ListItemText primary={folder.name} />
          {folder.isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </ListItem>
        <Collapse in={folder.isExpanded}>
          {subFolders.map(subFolder => renderFolder(subFolder))}
          {folderBookmarks.map(bookmark => renderBookmark(bookmark))}
        </Collapse>
      </Box>
    );
  };

  const renderBookmark = (bookmark: Bookmark) => (
    <ListItem
      key={bookmark.id}
      button
      component="a"
      href={bookmark.url}
      target="_blank"
      rel="noopener noreferrer"
    >
      <ListItemIcon>
        {bookmark.isFavorite ? <StarIcon color="primary" /> : <StarBorderIcon />}
      </ListItemIcon>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="subtitle1">{bookmark.title}</Typography>
            {bookmark.tags &&
              bookmark.tags.map(tag => (
                <Chip key={tag} size="small" label={tag} variant="outlined" />
              ))}
          </Box>
        }
        secondary={
          <Box component="span" sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Typography variant="body2" color="text.secondary">
              {bookmark.url}
            </Typography>
            {bookmark.lastVisited && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <AccessTimeIcon fontSize="small" />
                <Typography variant="caption" color="text.secondary">
                  Последнее посещение: {formatDate(bookmark.lastVisited)}
                </Typography>
              </Box>
            )}
            {bookmark.description && (
              <Typography variant="body2" color="text.secondary">
                {bookmark.description}
              </Typography>
            )}
          </Box>
        }
      />
      <ListItemSecondaryAction>
        <IconButton
          edge="end"
          onClick={e => {
            e.preventDefault();
            handleToggleFavorite(bookmark.id);
          }}
        >
          {bookmark.isFavorite ? <StarIcon color="primary" /> : <StarBorderIcon />}
        </IconButton>
        <IconButton
          edge="end"
          onClick={e => {
            e.preventDefault();
            handleMenuOpen(e, bookmark);
          }}
        >
          <MoreVertIcon />
        </IconButton>
      </ListItemSecondaryAction>
    </ListItem>
  );

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Закладки</Typography>
        <Button variant="contained" onClick={() => setOpenDialog(true)}>
          Добавить закладку
        </Button>
      </Box>

      <TextField
        fullWidth
        variant="outlined"
        placeholder="Поиск закладок..."
        value={searchQuery}
        onChange={e => setSearchQuery(e.target.value)}
        sx={{ mb: 2 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      <List>
        {getSubFolders().map(folder => renderFolder(folder))}
        {getFolderBookmarks().map(bookmark => renderBookmark(bookmark))}
      </List>

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        {selectedBookmark && (
          <>
            <MenuItem onClick={() => handleToggleFavorite(selectedBookmark.id)}>
              {selectedBookmark.isFavorite ? (
                <>
                  <StarBorderIcon sx={{ mr: 1 }} />
                  Убрать из избранного
                </>
              ) : (
                <>
                  <StarIcon sx={{ mr: 1 }} />
                  Добавить в избранное
                </>
              )}
            </MenuItem>
            <MenuItem onClick={() => handleRemoveBookmark(selectedBookmark.id)}>
              <DeleteIcon sx={{ mr: 1 }} />
              Удалить
            </MenuItem>
          </>
        )}
      </Menu>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Добавить закладку</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Название"
            fullWidth
            value={newBookmark.title}
            onChange={e => setNewBookmark({ ...newBookmark, title: e.target.value })}
          />
          <TextField
            margin="dense"
            label="URL"
            fullWidth
            value={newBookmark.url}
            onChange={e => setNewBookmark({ ...newBookmark, url: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Папка"
            fullWidth
            select
            value={newBookmark.folderId}
            onChange={e => setNewBookmark({ ...newBookmark, folderId: e.target.value })}
          >
            <MenuItem value="">Без папки</MenuItem>
            {folders.map(folder => (
              <MenuItem key={folder.id} value={folder.id}>
                {folder.name}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            margin="dense"
            label="Теги (через запятую)"
            fullWidth
            value={newBookmark.tags}
            onChange={e => setNewBookmark({ ...newBookmark, tags: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Описание"
            fullWidth
            multiline
            rows={2}
            value={newBookmark.description}
            onChange={e => setNewBookmark({ ...newBookmark, description: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Отмена</Button>
          <Button onClick={handleAddBookmark} variant="contained">
            Добавить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Bookmarks;
