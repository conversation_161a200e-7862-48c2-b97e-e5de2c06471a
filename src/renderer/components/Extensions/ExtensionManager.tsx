import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Extension as ExtensionIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { RootState } from '../../../store';
import {
  addExtension,
  disableExtension,
  enableExtension,
  removeExtension,
  setError,
  setExtensions,
  setLoading,
  updateExtension,
} from '../../../store/slices/extensionSlice';
import { Extension } from '../../extensions/types';

const ExtensionManager: React.FC = () => {
  const dispatch = useDispatch();
  const { extensions, enabledExtensions, loading, error } = useSelector(
    (state: RootState) =>
      state.extensions as {
        extensions: Extension[];
        enabledExtensions: string[];
        loading: boolean;
        error: string | null;
      }
  );
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedExtension, setSelectedExtension] = useState<Extension | null>(null);
  const [newExtension, setNewExtension] = useState<Partial<Extension>>({
    name: '',
    version: '',
    description: '',
    author: '',
    permissions: [],
  });

  useEffect(() => {
    // Load extensions from storage
    const loadExtensions = async () => {
      try {
        dispatch(setLoading(true));
        // TODO: Implement loading extensions from storage
        dispatch(setLoading(false));
      } catch (err) {
        dispatch(setError(err instanceof Error ? err.message : 'Failed to load extensions'));
        dispatch(setLoading(false));
      }
    };

    loadExtensions();
  }, [dispatch]);

  const handleAddExtension = () => {
    if (newExtension.name && newExtension.version) {
      const extension: Extension = {
        id: Date.now().toString(),
        name: newExtension.name,
        version: newExtension.version,
        description: newExtension.description || '',
        author: newExtension.author || '',
        permissions: newExtension.permissions || [],
        enabled: false,
        manifest: {
          name: newExtension.name,
          version: newExtension.version,
          description: newExtension.description || '',
          author: newExtension.author || '',
          permissions: newExtension.permissions || [],
        },
      };

      dispatch(addExtension(extension));
      setOpenDialog(false);
      setNewExtension({
        name: '',
        version: '',
        description: '',
        author: '',
        permissions: [],
      });
    }
  };

  const handleRemoveExtension = (id: string) => {
    dispatch(removeExtension(id));
  };

  const handleToggleExtension = (id: string) => {
    if (enabledExtensions.includes(id)) {
      dispatch(disableExtension(id));
    } else {
      dispatch(enableExtension(id));
    }
  };

  const handleOpenSettings = (extension: Extension) => {
    setSelectedExtension(extension);
    // TODO: Implement extension settings dialog
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={2}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">Extensions</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setOpenDialog(true)}
        >
          Add Extension
        </Button>
      </Box>

      <List>
        {extensions.map((extension: Extension) => (
          <ListItem key={extension.id}>
            <ListItemIcon>
              <ExtensionIcon />
            </ListItemIcon>
            <ListItemText
              primary={extension.name}
              secondary={`${extension.version} by ${extension.author}`}
            />
            <ListItemSecondaryAction>
              <IconButton
                edge="end"
                aria-label="settings"
                onClick={() => handleOpenSettings(extension)}
              >
                <SettingsIcon />
              </IconButton>
              <Switch
                edge="end"
                checked={enabledExtensions.includes(extension.id)}
                onChange={() => handleToggleExtension(extension.id)}
              />
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => handleRemoveExtension(extension.id)}
              >
                <DeleteIcon />
              </IconButton>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Add New Extension</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            fullWidth
            value={newExtension.name}
            onChange={e => setNewExtension({ ...newExtension, name: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Version"
            fullWidth
            value={newExtension.version}
            onChange={e => setNewExtension({ ...newExtension, version: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={2}
            value={newExtension.description}
            onChange={e => setNewExtension({ ...newExtension, description: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Author"
            fullWidth
            value={newExtension.author}
            onChange={e => setNewExtension({ ...newExtension, author: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleAddExtension} color="primary">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ExtensionManager;
