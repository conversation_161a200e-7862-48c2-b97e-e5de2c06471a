export interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  permissions: string[];
  enabled: boolean;
  icon?: string;
  manifest: ExtensionManifest;
}

export interface ExtensionManifest {
  name: string;
  version: string;
  description: string;
  author: string;
  permissions: string[];
  background?: {
    scripts: string[];
    persistent: boolean;
  };
  content_scripts?: {
    matches: string[];
    js: string[];
    css: string[];
  }[];
  browser_action?: {
    default_icon: string;
    default_title: string;
    default_popup?: string;
  };
  options_page?: string;
}

export interface ExtensionState {
  extensions: Extension[];
  enabledExtensions: string[];
  loading: boolean;
  error: string | null;
}

export interface ExtensionAction {
  type: string;
  payload: any;
}

export interface ExtensionContext {
  extension: Extension;
  tabId?: string;
  windowId?: string;
  data?: any;
}

export interface ExtensionAPI {
  tabs: {
    query: (queryInfo: any) => Promise<any[]>;
    create: (createProperties: any) => Promise<any>;
    update: (tabId: string, updateProperties: any) => Promise<any>;
    remove: (tabId: string) => Promise<void>;
  };
  storage: {
    local: {
      get: (keys: string[]) => Promise<any>;
      set: (items: any) => Promise<void>;
      remove: (keys: string[]) => Promise<void>;
    };
    sync: {
      get: (keys: string[]) => Promise<any>;
      set: (items: any) => Promise<void>;
      remove: (keys: string[]) => Promise<void>;
    };
  };
  runtime: {
    sendMessage: (message: any) => Promise<any>;
    onMessage: {
      addListener: (callback: (message: any) => void) => void;
      removeListener: (callback: (message: any) => void) => void;
    };
  };
  permissions: {
    request: (permissions: string[]) => Promise<boolean>;
    contains: (permissions: string[]) => Promise<boolean>;
  };
}
