/**
 * @file serviceContainer.ts
 * @description Простой контейнер для внедрения зависимостей (Service Locator).
 * Позволяет регистрировать и получать синглтоны сервисов (менеджеров) в приложении,
 * избегая использования глобальных переменных.
 */

interface IServiceContainer {
  get<T>(name: string): T;
}

type ServiceFactory<T> = (container: IServiceContainer) => T;

const services = new Map<string, ServiceFactory<any>>();
const serviceInstances = new Map<string, any>();

/**
 * Регистрирует сервис в контейнере.
 * @param {string} name - Имя сервиса (ключ).
 * @param {ServiceFactory<T>} factory - Функция-ф<PERSON>брика, которая создает экземпляр сервиса.
 *                             Фабрика получает контейнер в качестве аргумента для разрешения зависимостей.
 */
export function registerService<T>(name: string, factory: ServiceFactory<T>): void {
  if (services.has(name)) {
    console.warn(`Сервис с именем "${name}" уже зарегистрирован. Перезапись.`);
  }
  services.set(name, factory);
}

/**
 * Получает экземпляр сервиса из контейнера.
 * Создает экземпляр при первом вызове (ленивая инициализация) и кэширует его.
 * @param {string} name - Имя сервиса для получения.
 * @returns {T} Экземпляр сервиса.
 */
export function getService<T>(name: string): T {
  if (!serviceInstances.has(name)) {
    if (!services.has(name)) {
      throw new Error(`Сервис с именем "${name}" не зарегистрирован.`);
    }
    const factory = services.get(name);
    serviceInstances.set(name, factory({ get: getService as <T>(name: string) => T }));
  }
  return serviceInstances.get(name) as T;
}