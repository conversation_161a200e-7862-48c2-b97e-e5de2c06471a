import React from 'react';
import { Provider } from 'react-redux';

import { store } from '../store';

import Browser from './components/Browser/Browser';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingProvider from './components/Loading/LoadingProvider';
import NotificationProvider from './components/Notifications/NotificationProvider';
import ThemeProvider from './components/Theme/ThemeProvider';

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ThemeProvider>
          <LoadingProvider>
            <NotificationProvider>
              <Browser />
            </NotificationProvider>
          </LoadingProvider>
        </ThemeProvider>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
