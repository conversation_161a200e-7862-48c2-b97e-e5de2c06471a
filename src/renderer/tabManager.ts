/**
 * @file tabManager.ts
 * @description Управляет логикой вкладок: создание, закрытие, активация, состояние.
 * Манипулирует состоянием вкладок в `useTabStore`, но не взаимодействует с DOM напрямую.
 */

import { TabInfo, useTabStore } from './stores/useTabStore';

// Зависимости, внедряемые через DI
let settingsManager: any;
let historyManager: any;
let sessionManager: any;

class TabManager {
  // Зависимости
  setDependencies(deps: { settingsManager: any; historyManager: any; sessionManager: any }) {
    settingsManager = deps.settingsManager;
    historyManager = deps.historyManager;
    sessionManager = deps.sessionManager;
  }

  async initializeTabManager() {
    // Подписываемся на изменения вкладок для сохранения сессии
    useTabStore.subscribe(state => {
      sessionManager?.saveCurrentSession(state.tabs);
    });

    const restoredUrls = await sessionManager.restorePreviousSession();

    if (restoredUrls && restoredUrls.length > 0) {
      restoredUrls.forEach((url, index) => {
        this.createNewTab(url, index === 0);
      });
    } else {
      this.createNewTab();
    }

    console.log('TabManager initialized.');
  }

  createNewTab(url?: string, activate = true): void {
    const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    const isIncognito = settingsManager?.isIncognitoMode() || false;
    const homePage = settingsManager?.getSetting('homepage') || 'about:blank';

    const newTab: TabInfo = {
      id: tabId,
      url: url || homePage,
      title: 'Новая вкладка',
      favicon: null,
      isLoading: true,
      isActive: false, // Will be set by activateTab
    };

    useTabStore.getState().addTab(newTab);

    if (activate) {
      this.activateTab(tabId);
    }
  }

  activateTab(tabId: string): void {
    if (useTabStore.getState().activeTabId === tabId) return;
    useTabStore.getState().setActiveTabId(tabId);
  }

  closeTab(tabId: string): void {
    const { tabs, activeTabId, removeTab, setActiveTabId } = useTabStore.getState();
    const tabIndex = tabs.findIndex(t => t.id === tabId);
    if (tabIndex === -1) return;

    removeTab(tabId);

    if (activeTabId === tabId) {
      const remainingTabs = tabs.filter(t => t.id !== tabId);
      if (remainingTabs.length > 0) {
        // Activate the previous tab, or the first one if the closed one was the first
        const newActiveIndex = Math.max(0, tabIndex - 1);
        setActiveTabId(remainingTabs[newActiveIndex].id);
      } else {
        this.createNewTab();
      }
    }
  }
}

export default new TabManager();
