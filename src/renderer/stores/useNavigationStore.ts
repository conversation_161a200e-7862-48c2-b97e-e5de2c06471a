import { create } from 'zustand';

interface NavigationState {
  canGoBack: boolean;
  canGoForward: boolean;
  isLoading: boolean;
  url: string;
  setNavigationState: (newState: Partial<Omit<NavigationState, 'setNavigationState'>>) => void;
}

export const useNavigationStore = create<NavigationState>(set => ({
  canGoBack: false,
  canGoForward: false,
  isLoading: false,
  url: '',
  setNavigationState: newState => set(state => ({ ...state, ...newState })),
}));