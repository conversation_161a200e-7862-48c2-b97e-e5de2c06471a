import { create } from 'zustand';

export interface Bookmark {
  id: string;
  url: string;
  title: string;
  folderId?: string; // For future folder support
  tags?: string[];
  createdAt: number;
}

interface BookmarkState {
  bookmarks: Bookmark[];
  isPanelOpen: boolean;
  setBookmarks: (bookmarks: Bookmark[]) => void;
  addBookmark: (bookmark: Bookmark) => void;
  removeBookmark: (id: string) => void;
  togglePanel: () => void;
}

export const useBookmarkStore = create<BookmarkState>(set => ({
  bookmarks: [],
  isPanelOpen: false,
  setBookmarks: bookmarks => set({ bookmarks }),
  addBookmark: bookmark => set(state => ({ bookmarks: [bookmark, ...state.bookmarks] })),
  removeBookmark: id => set(state => ({ bookmarks: state.bookmarks.filter(b => b.id !== id) })),
  togglePanel: () => set(state => ({ isPanelOpen: !state.isPanelOpen })),
}));