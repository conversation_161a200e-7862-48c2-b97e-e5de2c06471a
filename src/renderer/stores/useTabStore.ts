import { create } from 'zustand';

export interface TabInfo {
  id: string;
  title: string;
  url: string;
  favicon: string | null;
  isActive: boolean;
  isLoading: boolean;
}

interface TabState {
  tabs: TabInfo[];
  activeTabId: string | null;
  addTab: (tab: TabInfo) => void;
  removeTab: (id: string) => void;
  setActiveTabId: (id: string | null) => void;
  updateTab: (id: string, newInfo: Partial<Omit<TabInfo, 'id'>>) => void;
}

export const useTabStore = create<TabState>(set => ({
  tabs: [],
  activeTabId: null,
  addTab: tab => set(state => ({ tabs: [...state.tabs, tab] })),
  removeTab: id => set(state => ({ tabs: state.tabs.filter(t => t.id !== id) })),
  setActiveTabId: id => set({ activeTabId: id }),
  updateTab: (id, newInfo) =>
    set(state => ({
      tabs: state.tabs.map(tab => (tab.id === id ? { ...tab, ...newInfo } : tab)),
    })),
}));