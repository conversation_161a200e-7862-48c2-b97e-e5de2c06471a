import { create } from 'zustand';

export interface NotificationAction {
  id: string;
  title: string;
  callback: () => void;
}

export interface Notification {
  id: string;
  title: string;
  message?: string;
  type: 'info' | 'success' | 'warning' | 'error';
  source: string;
  timestamp: number;
  read: boolean;
  actions?: NotificationAction[];
}

interface NotificationState {
  notifications: Notification[];
  setNotifications: (notifications: Notification[]) => void;
  addNotification: (notification: Notification) => void;
}

export const useNotificationStore = create<NotificationState>(set => ({
  notifications: [],
  setNotifications: notifications => set({ notifications }),
  addNotification: notification => set(state => ({ notifications: [notification, ...state.notifications.slice(0, 49)] })),
}));