import { create } from 'zustand';

export interface HistoryItem {
  id: string;
  url: string;
  title: string;
  timestamp: number;
}

interface HistoryState {
  items: HistoryItem[];
  isPanelOpen: boolean;
  setHistory: (items: HistoryItem[]) => void;
  clearHistory: () => void;
  togglePanel: () => void;
}

export const useHistoryStore = create<HistoryState>(set => ({
  items: [],
  isPanelOpen: false,
  setHistory: items => set({ items }),
  clearHistory: () => set({ items: [] }),
  togglePanel: () => set(state => ({ isPanelOpen: !state.isPanelOpen })),
}));