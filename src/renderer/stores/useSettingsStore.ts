import { create } from 'zustand';

// Определяем, какие настройки у нас есть.
// Это должно быть единым источником истины для структуры настроек.
export interface BrowserSettings {
  darkMode: boolean;
  homepage: string;
  searchEngineUrl: string;
  doNotTrack: boolean;
  // ... другие настройки по мере их добавления
}

interface SettingsState extends BrowserSettings {
  isSettingsPanelOpen: boolean;
  // Действия для управления состоянием
  setSettings: (settings: Partial<BrowserSettings>) => void;
  toggleSettingsPanel: () => void;
}

// Начальные/дефолтные значения
const defaultSettings: BrowserSettings = {
  darkMode: false,
  homepage: 'https://www.google.com',
  searchEngineUrl: 'https://www.google.com/search?q=',
  doNotTrack: false,
};

export const useSettingsStore = create<SettingsState>(set => ({
  ...defaultSettings,
  isSettingsPanelOpen: false,
  setSettings: newSettings => set(state => ({ ...state, ...newSettings })),
  toggleSettingsPanel: () => set(state => ({ isSettingsPanelOpen: !state.isSettingsPanelOpen })),
}));