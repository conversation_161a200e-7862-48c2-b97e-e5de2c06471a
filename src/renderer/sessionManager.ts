import { TabInfo } from './stores/useTabStore';

class SessionManager {
  /**
   * Saves the current session (list of tab URLs) to the main process.
   * @param tabs - The current array of tab information.
   */
  async saveCurrentSession(tabs: TabInfo[]): Promise<void> {
    if (!window.electronAPI?.saveSession) {
      // Silently fail if the API is not available, as this is a background task.
      return;
    }
    try {
      // We only need to save the URLs to restore the session.
      const sessionData = tabs.map(tab => tab.url).filter(url => url && url !== 'about:blank');
      await window.electronAPI.saveSession(sessionData);
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }

  /**
   * Restores the previous session from the main process.
   * @returns A promise that resolves to an array of URLs or null.
   */
  async restorePreviousSession(): Promise<string[] | null> {
    if (!window.electronAPI?.restoreSession) {
      return null;
    }
    try {
      return await window.electronAPI.restoreSession();
    } catch (error) {
      console.error('Failed to restore session:', error);
      return null;
    }
  }
}

export default new SessionManager();