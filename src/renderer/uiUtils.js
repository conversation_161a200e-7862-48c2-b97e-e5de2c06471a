/**
 * @file uiUtils.js
 * @description Утилиты для управления пользовательским интерфейсом в A11 Browser.
 */

/**
 * Переключает видимость элемента.
 * @param {HTMLElement|string} elementOrSelector - Элемент или CSS-селектор элемента.
 * @param {boolean} [forceShow] - Принудительно показать или скрыть элемент.
 */
function toggleElementVisibility(elementOrSelector, forceShow) {
  const element =
    typeof elementOrSelector === 'string'
      ? document.querySelector(elementOrSelector)
      : elementOrSelector;
  if (!element) {
    console.warn('Элемент для переключения видимости не найден:', elementOrSelector);
    return;
  }

  if (typeof forceShow === 'boolean') {
    element.style.display = forceShow ? '' : 'none'; // Или 'block', 'flex' в зависимости от элемента
    element.classList.toggle('hidden', !forceShow);
  } else {
    const isHidden = element.style.display === 'none' || element.classList.contains('hidden');
    element.style.display = isHidden ? '' : 'none';
    element.classList.toggle('hidden', !isHidden);
  }
}

/**
 * Показывает уведомление пользователю.
 * @param {string} message - Сообщение уведомления.
 * @param {string} [type='info'] - Тип уведомления ('info', 'success', 'warning', 'error').
 * @param {number} [duration=3000] - Длительность отображения в миллисекундах.
 */
function showNotification(message, type = 'info', duration = 3000) {
  const notificationArea =
    document.getElementById('notification-area') || _createNotificationArea();

  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;

  notificationArea.appendChild(notification);

  setTimeout(() => {
    notification.classList.add('notification-fade-out');
    notification.addEventListener('animationend', () => {
      notification.remove();
      if (!notificationArea.hasChildNodes()) {
        // notificationArea.remove(); // Можно удалять, если больше не нужна
      }
    });
  }, duration);
}

/**
 * Создает контейнер для уведомлений, если он не существует.
 * @private
 */
function _createNotificationArea() {
  let area = document.getElementById('notification-area');
  if (!area) {
    area = document.createElement('div');
    area.id = 'notification-area';
    area.style.position = 'fixed';
    area.style.top = '20px';
    area.style.right = '20px';
    area.style.zIndex = '10000';
    area.style.display = 'flex';
    area.style.flexDirection = 'column';
    area.style.gap = '10px';
    document.body.appendChild(area);
  }
  return area;
}

/**
 * Debounce function to limit the rate at which a function can fire.
 * @param {Function} func - The function to debounce.
 * @param {number} delay - The delay in milliseconds.
 * @returns {Function} - The debounced function.
 */
function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * Throttle function to limit the execution of a function to once in a specified time period.
 * @param {Function} func - The function to throttle.
 * @param {number} limit - The time limit in milliseconds.
 * @returns {Function} - The throttled function.
 */
function throttle(func, limit) {
  let inThrottle;
  let lastFunc;
  let lastRan;
  return function (...args) {
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      lastRan = Date.now();
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
        if (lastFunc) {
          lastFunc.apply(context, args); // Execute the last call if any
          lastFunc = null;
          lastRan = Date.now();
          inThrottle = true; // Re-enter throttle state
          // Setup timeout again for this new execution
          setTimeout(() => (inThrottle = false), limit);
        }
      }, limit);
    } else {
      lastFunc = func;
    }
  };
}

// Пример использования (можно раскомментировать для проверки)
/*
document.addEventListener('DOMContentLoaded', () => {

  // Пример для toggleElementVisibility
  // const myElement = document.createElement('div');
  // myElement.id = 'test-element';
  // myElement.textContent = 'Test Element';
  // document.body.appendChild(myElement);
  // toggleElementVisibility(myElement);
  // setTimeout(() => toggleElementVisibility('#test-element', true), 2000);

  // Пример для showNotification
  // showNotification('Это информационное сообщение.', 'info');
  // setTimeout(() => showNotification('Успех!', 'success'), 1000);
  // setTimeout(() => showNotification('Предупреждение!', 'warning'), 2000);
  // setTimeout(() => showNotification('Ошибка!', 'error'), 3000);

  // Пример для debounce
  // const debouncedLog = debounce(() => console.log('Debounced!'), 500);
  // window.addEventListener('mousemove', debouncedLog);

  // Пример для throttle
  // const throttledLog = throttle(() => console.log('Throttled!'), 1000);
  // window.addEventListener('scroll', throttledLog);
});
*/

export { toggleElementVisibility, showNotification, debounce, throttle };
