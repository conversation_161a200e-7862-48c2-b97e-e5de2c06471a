/**
 * Tab Group Manager for A11 Browser
 * Provides advanced tab grouping, organization and visualization
 * with support for intelligent automation and user settings
 */

class TabGroupManager {
  constructor() {
    // Main data structures
    this.groups = new Map(); // Stores all tab groups
    this.tabToGroup = new Map(); // Relationship between tabs and groups
    this.nextGroupId = 1; // Counter for generating group IDs
    this.groupTemplates = new Map(); // Group templates for quick creation
    this.groupHistory = []; // History of group actions for undo/redo
    this.pinnedGroups = new Set(); // Pinned groups
    this.hiddenGroups = new Set(); // Hidden groups
    this.groupRelationships = new Map(); // Relationships between groups (subgroups, related groups)
    this.groupWorkspaces = new Map(); // Workspaces (sets of groups)

    // Color schemes for groups
    this.colorSchemes = {
      default: [
        { name: 'red', color: '#ff5f57', textColor: '#ffffff' },
        { name: 'orange', color: '#ffbd2e', textColor: '#000000' },
        { name: 'yellow', color: '#ffdc5f', textColor: '#000000' },
        { name: 'green', color: '#28c941', textColor: '#ffffff' },
        { name: 'blue', color: '#1e98f5', textColor: '#ffffff' },
        { name: 'purple', color: '#8e44ad', textColor: '#ffffff' },
        { name: 'pink', color: '#e84393', textColor: '#ffffff' },
        { name: 'gray', color: '#95a5a6', textColor: '#ffffff' },
        { name: 'brown', color: '#8d6e63', textColor: '#ffffff' },
        { name: 'teal', color: '#00bcd4', textColor: '#ffffff' },
      ],
      pastel: [
        { name: 'pastelRed', color: '#ffb3ba', textColor: '#000000' },
        { name: 'pastelOrange', color: '#ffdfba', textColor: '#000000' },
        { name: 'pastelYellow', color: '#ffffba', textColor: '#000000' },
        { name: 'pastelGreen', color: '#baffc9', textColor: '#000000' },
        { name: 'pastelBlue', color: '#bae1ff', textColor: '#000000' },
        { name: 'pastelPurple', color: '#d8b5ff', textColor: '#000000' },
        { name: 'pastelPink', color: '#ffb3e6', textColor: '#000000' },
        { name: 'pastelGray', color: '#e5e5e5', textColor: '#000000' },
        { name: 'pastelBrown', color: '#d7ccc8', textColor: '#000000' },
        { name: 'pastelTeal', color: '#b2ebf2', textColor: '#000000' },
      ],
      dark: [
        { name: 'darkRed', color: '#c0392b', textColor: '#ffffff' },
        { name: 'darkOrange', color: '#d35400', textColor: '#ffffff' },
        { name: 'darkYellow', color: '#f39c12', textColor: '#000000' },
        { name: 'darkGreen', color: '#27ae60', textColor: '#ffffff' },
        { name: 'darkBlue', color: '#2980b9', textColor: '#ffffff' },
        { name: 'darkPurple', color: '#6c3483', textColor: '#ffffff' },
        { name: 'darkPink', color: '#c2185b', textColor: '#ffffff' },
        { name: 'darkGray', color: '#7f8c8d', textColor: '#ffffff' },
        { name: 'darkBrown', color: '#5d4037', textColor: '#ffffff' },
        { name: 'darkTeal', color: '#00838f', textColor: '#ffffff' },
      ],
      custom: [], // Custom colors
    };

    // Icons for groups
    this.groupIcons = [
      'globe',
      'star',
      'heart',
      'home',
      'work',
      'school',
      'shopping',
      'social',
      'news',
      'entertainment',
      'finance',
      'travel',
      'food',
      'health',
      'sports',
      'tech',
      'music',
      'video',
      'document',
      'code',
      'chat',
      'mail',
      'calendar',
      'map',
      'cloud',
      'settings',
      'search',
      'bookmark',
      'folder',
      'lock',
      'user',
      'group',
      'bell',
      'camera',
      'microphone',
      'phone',
      'download',
      'upload',
      'refresh',
      'trash',
    ];

    // Tab group settings
    this.settings = {
      // Basic settings
      enabled: true, // Whether group functionality is enabled
      defaultColorScheme: 'default', // Default color scheme
      defaultView: 'horizontal', // Group display view (horizontal, vertical, grid)

      // Automation
      autoGrouping: false, // Automatic tab grouping
      autoGroupingThreshold: 3, // Number of tabs from same domain for auto-grouping
      autoGroupingRules: [], // Rules for automatic grouping
      smartGrouping: true, // Intelligent grouping based on content
      suggestGroups: true, // Suggest grouping

      // Визуальные настройки
      colorCoding: true, // Цветовое кодирование групп
      collapsible: true, // Возможность сворачивать группы
      showGroupLabels: true, // Показывать названия групп
      showGroupCount: true, // Показывать количество вкладок в группе
      showGroupIcons: true, // Показывать иконки групп
      groupPreview: true, // Предпросмотр группы при наведении
      animateGroupChanges: true, // Анимировать изменения в группах
      compactGroups: false, // Компактный режим отображения групп

      // Взаимодействие
      dragBetweenGroups: true, // Перетаскивание вкладок между группами
      dragToReorder: true, // Перетаскивание для изменения порядка групп
      multiSelectTabs: true, // Множественный выбор вкладок
      rightClickGroupOptions: true, // Опции группы в контекстном меню
      doubleClickToRename: true, // Двойной клик для переименования группы
      middleClickToClose: true, // Средний клик для закрытия группы

      // Расширенные функции
      nestedGroups: false, // Поддержка вложенных групп
      groupSessions: true, // Сохранение групп как сессий
      groupSync: true, // Синхронизация групп между устройствами
      groupSearch: true, // Поиск по группам
      groupFilters: true, // Фильтры для групп
      groupTags: true, // Теги для групп
      groupNotes: true, // Заметки для групп
      groupExport: true, // Экспорт/импорт групп
      groupStatistics: true, // Статистика использования групп

      // Производительность
      lazyLoadInactiveGroups: true, // Ленивая загрузка неактивных групп
      unloadHiddenGroups: false, // Выгрузка скрытых групп из памяти
      maxGroupsVisible: 0, // Максимальное количество видимых групп (0 - без ограничений)
      groupMemoryLimit: 0, // Ограничение памяти для группы (0 - без ограничений)
    };

    // Статистика использования групп
    this.statistics = {
      groupsCreated: 0,
      groupsDeleted: 0,
      tabsGrouped: 0,
      tabsUngrouped: 0,
      mostUsedGroups: {},
      averageGroupSize: 0,
      averageGroupLifetime: 0,
      groupOperationsHistory: [],
      lastUsedGroups: [],
    };

    // Загружаем настройки
    this._loadSettings();

    // Загружаем сохраненные группы
    this._loadGroups();

    // Инициализируем обработчики событий
    this._initEventListeners();

    // Инициализируем интеллектуальную группировку, если включена
    if (this.settings.smartGrouping) {
      this._initSmartGrouping();
    }

    // Инициализируем систему подсказок для групп
    if (this.settings.suggestGroups) {
      this._initGroupSuggestions();
    }
  }

  /**
   * Загружает сохраненные группы вкладок
   * @private
   */
  _loadGroups() {
    try {
      const savedGroups = JSON.parse(localStorage.getItem('a11_tab_groups') || '[]');
      this.groups = savedGroups;

      // Устанавливаем активную группу, если она есть
      const activeGroupId = localStorage.getItem('a11_active_group_id');
      if (activeGroupId && this.groups.some(group => group.id === activeGroupId)) {
        this.activeGroupId = activeGroupId;
      } else if (this.groups.length > 0) {
        // Если активной группы нет или она не найдена, устанавливаем первую группу
        this.activeGroupId = this.groups[0].id;
      }
    } catch (error) {
      console.error('Ошибка при загрузке групп вкладок:', error);
      this.groups = [];
    }
  }

  /**
   * Сохраняет группы вкладок
   * @private
   */
  _saveGroups() {
    try {
      localStorage.setItem('a11_tab_groups', JSON.stringify(this.groups));
      if (this.activeGroupId) {
        localStorage.setItem('a11_active_group_id', this.activeGroupId);
      }
    } catch (error) {
      console.error('Ошибка при сохранении групп вкладок:', error);
    }
  }

  /**
   * Создает новую группу вкладок
   * @param {string} name - Название группы
   * @param {string} color - Цвет группы (HEX или название)
   * @param {Array} tabs - Массив вкладок для добавления в группу
   * @returns {string} - ID созданной группы
   */
  createGroup(name, color = '#2962ff', tabs = []) {
    const groupId = `group_${Date.now()}`;

    const newGroup = {
      id: groupId,
      name: name || 'Новая группа',
      color,
      tabs: [...tabs],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.groups.push(newGroup);

    // Если это первая группа, делаем её активной
    if (this.groups.length === 1) {
      this.activeGroupId = groupId;
    }

    this._saveGroups();

    // Генерируем событие создания группы
    window.dispatchEvent(
      new CustomEvent('tabgroupcreated', {
        detail: { group: newGroup },
      })
    );

    return groupId;
  }

  /**
   * Удаляет группу вкладок
   * @param {string} groupId - ID группы
   * @returns {boolean} - Успешность операции
   */
  removeGroup(groupId) {
    const groupIndex = this.groups.findIndex(group => group.id === groupId);

    if (groupIndex === -1) {
      console.error(`Группа с ID "${groupId}" не найдена`);
      return false;
    }

    const removedGroup = this.groups[groupIndex];
    this.groups.splice(groupIndex, 1);

    // Если удаляем активную группу, переключаемся на другую
    if (this.activeGroupId === groupId) {
      this.activeGroupId = this.groups.length > 0 ? this.groups[0].id : null;
    }

    this._saveGroups();

    // Генерируем событие удаления группы
    window.dispatchEvent(
      new CustomEvent('tabgroupremoved', {
        detail: { groupId, tabs: removedGroup.tabs },
      })
    );

    return true;
  }

  /**
   * Добавляет вкладку в группу
   * @param {string} groupId - ID группы
   * @param {Object} tab - Объект вкладки
   * @returns {boolean} - Успешность операции
   */
  addTabToGroup(groupId, tab) {
    const group = this.groups.find(group => group.id === groupId);

    if (!group) {
      console.error(`Группа с ID "${groupId}" не найдена`);
      return false;
    }

    // Проверяем, есть ли уже такая вкладка в группе
    if (group.tabs.some(existingTab => existingTab.id === tab.id)) {
      return true; // Вкладка уже в группе
    }

    group.tabs.push(tab);
    group.updatedAt = new Date().toISOString();

    this._saveGroups();

    // Генерируем событие добавления вкладки в группу
    window.dispatchEvent(
      new CustomEvent('tabtogroupadded', {
        detail: { groupId, tab },
      })
    );

    return true;
  }

  /**
   * Удаляет вкладку из группы
   * @param {string} groupId - ID группы
   * @param {string} tabId - ID вкладки
   * @returns {boolean} - Успешность операции
   */
  removeTabFromGroup(groupId, tabId) {
    const group = this.groups.find(group => group.id === groupId);

    if (!group) {
      console.error(`Группа с ID "${groupId}" не найдена`);
      return false;
    }

    const tabIndex = group.tabs.findIndex(tab => tab.id === tabId);

    if (tabIndex === -1) {
      console.error(`Вкладка с ID "${tabId}" не найдена в группе`);
      return false;
    }

    const removedTab = group.tabs[tabIndex];
    group.tabs.splice(tabIndex, 1);
    group.updatedAt = new Date().toISOString();

    this._saveGroups();

    // Генерируем событие удаления вкладки из группы
    window.dispatchEvent(
      new CustomEvent('tabfromgroupremoved', {
        detail: { groupId, tabId, tab: removedTab },
      })
    );

    return true;
  }

  /**
   * Переименовывает группу
   * @param {string} groupId - ID группы
   * @param {string} newName - Новое название
   * @returns {boolean} - Успешность операции
   */
  renameGroup(groupId, newName) {
    const group = this.groups.find(group => group.id === groupId);

    if (!group) {
      console.error(`Группа с ID "${groupId}" не найдена`);
      return false;
    }

    group.name = newName;
    group.updatedAt = new Date().toISOString();

    this._saveGroups();

    // Генерируем событие переименования группы
    window.dispatchEvent(
      new CustomEvent('tabgrouprenamed', {
        detail: { groupId, newName },
      })
    );

    return true;
  }

  /**
   * Изменяет цвет группы
   * @param {string} groupId - ID группы
   * @param {string} newColor - Новый цвет
   * @returns {boolean} - Успешность операции
   */
  setGroupColor(groupId, newColor) {
    const group = this.groups.find(group => group.id === groupId);

    if (!group) {
      console.error(`Группа с ID "${groupId}" не найдена`);
      return false;
    }

    group.color = newColor;
    group.updatedAt = new Date().toISOString();

    this._saveGroups();

    // Генерируем событие изменения цвета группы
    window.dispatchEvent(
      new CustomEvent('tabgroupcolorchanged', {
        detail: { groupId, newColor },
      })
    );

    return true;
  }

  /**
   * Устанавливает активную группу
   * @param {string} groupId - ID группы
   * @returns {boolean} - Успешность операции
   */
  setActiveGroup(groupId) {
    if (!this.groups.some(group => group.id === groupId)) {
      console.error(`Группа с ID "${groupId}" не найдена`);
      return false;
    }

    this.activeGroupId = groupId;
    localStorage.setItem('a11_active_group_id', groupId);

    // Генерируем событие изменения активной группы
    window.dispatchEvent(
      new CustomEvent('tabgroupactivated', {
        detail: { groupId },
      })
    );

    return true;
  }

  /**
   * Получает все группы вкладок
   * @returns {Array} - Массив групп
   */
  getAllGroups() {
    return [...this.groups];
  }

  /**
   * Получает активную группу
   * @returns {Object|null} - Активная группа или null
   */
  getActiveGroup() {
    if (!this.activeGroupId) return null;
    return this.groups.find(group => group.id === this.activeGroupId) || null;
  }

  /**
   * Получает группу по ID
   * @param {string} groupId - ID группы
   * @returns {Object|null} - Группа или null
   */
  getGroupById(groupId) {
    return this.groups.find(group => group.id === groupId) || null;
  }

  /**
   * Перемещает вкладку между группами
   * @param {string} tabId - ID вкладки
   * @param {string} sourceGroupId - ID исходной группы
   * @param {string} targetGroupId - ID целевой группы
   * @returns {boolean} - Успешность операции
   */
  moveTabBetweenGroups(tabId, sourceGroupId, targetGroupId) {
    const sourceGroup = this.groups.find(group => group.id === sourceGroupId);
    const targetGroup = this.groups.find(group => group.id === targetGroupId);

    if (!sourceGroup) {
      console.error(`Исходная группа с ID "${sourceGroupId}" не найдена`);
      return false;
    }

    if (!targetGroup) {
      console.error(`Целевая группа с ID "${targetGroupId}" не найдена`);
      return false;
    }

    const tabIndex = sourceGroup.tabs.findIndex(tab => tab.id === tabId);

    if (tabIndex === -1) {
      console.error(`Вкладка с ID "${tabId}" не найдена в исходной группе`);
      return false;
    }

    // Извлекаем вкладку из исходной группы
    const tab = sourceGroup.tabs[tabIndex];
    sourceGroup.tabs.splice(tabIndex, 1);
    sourceGroup.updatedAt = new Date().toISOString();

    // Добавляем вкладку в целевую группу
    targetGroup.tabs.push(tab);
    targetGroup.updatedAt = new Date().toISOString();

    this._saveGroups();

    // Генерируем событие перемещения вкладки между группами
    window.dispatchEvent(
      new CustomEvent('tabmovedtogroup', {
        detail: { tabId, sourceGroupId, targetGroupId, tab },
      })
    );

    return true;
  }
}

// Экспортируем класс для использования в других модулях
module.exports = TabGroupManager;
