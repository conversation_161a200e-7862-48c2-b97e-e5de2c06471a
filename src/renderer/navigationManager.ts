/**
 * @file navigationManager.ts
 * @description Handles browser navigation controls like back, forward, reload, and URL input.
 */

import { useNavigationStore } from './stores/useNavigationStore.ts';
import { useTabStore } from './stores/useTabStore.ts';

// Определяем интерфейсы для зависимостей и данных
interface Tab {
  id: string;
  isLoading: boolean;
  webview: Electron.WebviewTag;
  goBack: () => void;
  goForward: () => void;
  reload: () => void;
  stopLoading: () => void;
  navigateTo: (url: string) => void;
}

interface TabManager {
  getActiveTab: () => Tab | null;
}

// Зависимости теперь будут внедряться, а не импортироваться или браться из window
// let tabManager: TabManager; // No longer needed
let isInitialized = false;

/**
 * @description Внедрение зависимостей в модуль.
 * @param {object} deps - Объект с зависимостями.
 * @param {object} [deps.tabManager] - Менеджер вкладок (больше не используется).
 */
function setDependencies(): void {
  // tabManager = deps.tabManager;
}

/**
 * @description Initializes navigation controls and event listeners.
 */
function initializeNavigation(): void {
  if (isInitialized) return;

  // Обновляем состояние кнопок при смене вкладки или загрузке страницы
  // We can now subscribe directly to the stores
  useTabStore.subscribe(state => updateNavigationState(state.activeTabId), (state, prevState) => state.activeTabId !== prevState.activeTabId || state.tabs.find(t => t.id === state.activeTabId)?.isLoading !== prevState.tabs.find(t => t.id === prevState.activeTabId)?.isLoading);

  // Первоначальное обновление состояния
  updateNavigationState();
  isInitialized = true;
  console.log('NavigationManager initialized.');
}

/**
 * @description Updates the state of navigation buttons (enabled/disabled) based on the active tab's state.
 * В идеале, эта логика должна обновлять централизованное состояние (Zustand/Redux), а UI-компоненты (React) будут на него реагировать.
 * @param {object} [tabInfo] - Информация о вкладке из события webview.
 * @param {string | null} [activeTabId] - The ID of the currently active tab.
 */
function updateNavigationState(activeTabId?: string | null): void {
  const id = activeTabId ?? useTabStore.getState().activeTabId;
  if (!id) return;

  const activeTab = useTabStore.getState().tabs.find(t => t.id === id);
  const setStoreState = useNavigationStore.getState().setNavigationState;
  const webview = document.getElementById(`webview-${id}`) as Electron.WebviewTag | null;

  if (activeTab && webview) {
    // Если событие пришло от активной вкладки, или это просто смена вкладок
    setStoreState({
      canGoBack: webview.canGoBack(),
      canGoForward: webview.canGoForward(),
      isLoading: activeTab.isLoading,
      url: webview.getURL(),
    });
  } else {
    // Если нет активной вкладки
    setStoreState({
      canGoBack: false,
      canGoForward: false,
      isLoading: false,
      url: '',
    });
  }
}

function getActiveWebview(): Electron.WebviewTag | null {
  const activeTabId = useTabStore.getState().activeTabId;
  if (!activeTabId) return null;
  return document.getElementById(`webview-${activeTabId}`) as Electron.WebviewTag | null;
}

function goBack(): void {
  getActiveWebview()?.goBack();
}

function goForward(): void {
  getActiveWebview()?.goForward();
}

function reloadOrStop(): void {
  const webview = getActiveWebview();
  if (webview) {
    if (useNavigationStore.getState().isLoading) {
      webview.stop();
    } else {
      webview.reload();
    }
  }
}

function navigate(url: string): void {
  const webview = getActiveWebview();
  if (!webview || !url || typeof url !== 'string') return;

  let targetUrl = url;
  const searchEngineUrl = useSettingsStore.getState().searchEngineUrl || 'https://www.google.com/search?q=';

  try {
    // Check if it's a full URL
    // eslint-disable-next-line no-new
    new URL(url);
  } catch (e) {
    // If not, check if it looks like a domain or localhost
    if (url.includes('.') || url.startsWith('localhost')) {
      targetUrl = `http://${url}`;
    } else {
      // Otherwise, treat it as a search query
      targetUrl = `${searchEngineUrl.replace('%s', encodeURIComponent(url))}`;
    }
  }

  webview.loadURL(targetUrl);
}

// Экспорт функций (если они будут использоваться другими модулями)
export default {
  setDependencies,
  initializeNavigation,
  updateNavigationState,
  // Новые методы для вызова из UI
  goBack,
  goForward,
  reloadOrStop,
  navigate,
};
