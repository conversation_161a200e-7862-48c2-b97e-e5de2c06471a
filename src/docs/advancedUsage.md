# Advanced Usage

## Architecture Diagram
```mermaid
flowchart TD
  A[Form Component] --> B(useFormValidation)
  B --> C{Validation}
  C -->|Synchronous| D[Local Rules]
  C -->|Asynchronous| E[Server Validation]
  E --> F[Security Utilities]
  E --> G[Retry Logic]
  B --> H[Error Handling]
  H --> I[Logging]
  H --> J[User Messages]
```

## Migration from v1.x to v2.0
```ts
// Before
const { errors } = useForm(initialValues);

// After
const {
  errors,
  validateField,
  resetValidation
} = useFormValidation(initialValues, schema);
```

## Multilingual Validation
```tsx
const i18nSchema = {
  email: (v) => ({
    en: /^[^\s@]+@[^\s@]+$/.test(v) || 'Invalid email',
    ru: /^[^\s@]+@[^\s@]+$/.test(v) || 'Invalid email'
  })[locale]
};
```

## Testing
```tsx
const TestWrapper = ({ children }) => (
  <SecurityProvider config={testSecurityConfig}>
    <ValidationProvider>
      {children}
    </ValidationProvider>
  </SecurityProvider>
);

const { result } = renderHook(
  () => useFormValidation(testValues, testSchema),
  { wrapper: TestWrapper }
);
```

## Common Scenarios
### Dynamic Fields
```tsx
const dynamicSchema = {
  [fieldName]: dynamicValidationRules
};
```

### Composite Rules
```ts
const compositeValidator = (value) => {
  const basicCheck = required(value);
  if (basicCheck !== true) return basicCheck;
  return formatCheck(value);
};
```