import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface Breakpoint {
  name: string;
  minWidth: number;
  maxWidth?: number;
  columns: number;
  gutter: number;
  margin: number;
}

export interface ResponsiveConfig {
  breakpoints: Breakpoint[];
  enableFluidTypography: boolean;
  enableContainerQueries: boolean;
  enableResponsiveImages: boolean;
  enableTouchOptimization: boolean;
  enableOrientationHandling: boolean;
  enableDensityOptimization: boolean;
  defaultBreakpoint: string;
  fluidTypographyRatio: number;
  touchTargetMinSize: number;
}

export interface ViewportInfo {
  width: number;
  height: number;
  aspectRatio: number;
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  currentBreakpoint: string;
  isTouch: boolean;
  isRetina: boolean;
}

export interface ResponsiveComponent {
  id: string;
  element: HTMLElement;
  breakpointStyles: Map<string, CSSStyleDeclaration>;
  fluidProperties: string[];
  touchOptimized: boolean;
}

export interface LayoutGrid {
  columns: number;
  gutter: number;
  margin: number;
  maxWidth?: number;
  breakpoint: string;
}

export class ResponsiveDesignSystem extends EventEmitter {
  private static instance: ResponsiveDesignSystem;
  private config: ResponsiveConfig;
  private currentViewport: ViewportInfo;
  private components: Map<string, ResponsiveComponent> = new Map();
  private resizeObserver?: ResizeObserver;
  private mediaQueryLists: Map<string, MediaQueryList> = new Map();
  private orientationHandler?: () => void;

  private constructor() {
    super();
    this.config = {
      breakpoints: [
        { name: 'xs', minWidth: 0, maxWidth: 575, columns: 4, gutter: 16, margin: 16 },
        { name: 'sm', minWidth: 576, maxWidth: 767, columns: 8, gutter: 16, margin: 24 },
        { name: 'md', minWidth: 768, maxWidth: 991, columns: 12, gutter: 24, margin: 32 },
        { name: 'lg', minWidth: 992, maxWidth: 1199, columns: 12, gutter: 24, margin: 40 },
        { name: 'xl', minWidth: 1200, maxWidth: 1399, columns: 12, gutter: 32, margin: 48 },
        { name: 'xxl', minWidth: 1400, columns: 12, gutter: 32, margin: 56 },
      ],
      enableFluidTypography: true,
      enableContainerQueries: true,
      enableResponsiveImages: true,
      enableTouchOptimization: true,
      enableOrientationHandling: true,
      enableDensityOptimization: true,
      defaultBreakpoint: 'md',
      fluidTypographyRatio: 1.25,
      touchTargetMinSize: 44,
    };

    this.currentViewport = this.getCurrentViewportInfo();
    this.initializeResponsiveSystem();
  }

  public static getInstance(): ResponsiveDesignSystem {
    if (!ResponsiveDesignSystem.instance) {
      ResponsiveDesignSystem.instance = new ResponsiveDesignSystem();
    }
    return ResponsiveDesignSystem.instance;
  }

  private async initializeResponsiveSystem(): Promise<void> {
    // Load configuration
    const responsiveConfig = configManager.get('responsive', {});
    this.config = { ...this.config, ...responsiveConfig };

    // Setup viewport monitoring
    this.setupViewportMonitoring();

    // Setup media queries
    this.setupMediaQueries();

    // Setup orientation handling
    if (this.config.enableOrientationHandling) {
      this.setupOrientationHandling();
    }

    // Apply initial responsive styles
    this.applyResponsiveStyles();

    // Setup fluid typography
    if (this.config.enableFluidTypography) {
      this.setupFluidTypography();
    }

    // Setup touch optimization
    if (this.config.enableTouchOptimization) {
      this.setupTouchOptimization();
    }

    logger.info('Responsive design system initialized', {
      currentBreakpoint: this.currentViewport.currentBreakpoint,
      viewportSize: `${this.currentViewport.width}x${this.currentViewport.height}`,
      isTouch: this.currentViewport.isTouch,
      pixelRatio: this.currentViewport.pixelRatio,
    });
  }

  private getCurrentViewportInfo(): ViewportInfo {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const aspectRatio = width / height;
    const orientation = width > height ? 'landscape' : 'portrait';
    const pixelRatio = window.devicePixelRatio || 1;
    const currentBreakpoint = this.determineBreakpoint(width);
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isRetina = pixelRatio > 1;

    return {
      width,
      height,
      aspectRatio,
      orientation,
      pixelRatio,
      currentBreakpoint,
      isTouch,
      isRetina,
    };
  }

  private determineBreakpoint(width: number): string {
    for (const breakpoint of this.config.breakpoints) {
      if (width >= breakpoint.minWidth && (!breakpoint.maxWidth || width <= breakpoint.maxWidth)) {
        return breakpoint.name;
      }
    }
    return this.config.defaultBreakpoint;
  }

  private setupViewportMonitoring(): void {
    // Resize observer for viewport changes
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver(() => {
        this.handleViewportChange();
      });
      this.resizeObserver.observe(document.documentElement);
    }

    // Fallback resize listener
    window.addEventListener('resize', () => {
      this.handleViewportChange();
    });

    // Pixel ratio changes (zoom, external monitor)
    if (window.matchMedia) {
      const pixelRatioQuery = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`);
      pixelRatioQuery.addEventListener('change', () => {
        this.handleViewportChange();
      });
    }
  }

  private setupMediaQueries(): void {
    this.config.breakpoints.forEach(breakpoint => {
      const query = breakpoint.maxWidth
        ? `(min-width: ${breakpoint.minWidth}px) and (max-width: ${breakpoint.maxWidth}px)`
        : `(min-width: ${breakpoint.minWidth}px)`;

      const mediaQueryList = window.matchMedia(query);
      this.mediaQueryLists.set(breakpoint.name, mediaQueryList);

      mediaQueryList.addEventListener('change', event => {
        if (event.matches) {
          this.handleBreakpointChange(breakpoint.name);
        }
      });
    });
  }

  private setupOrientationHandling(): void {
    this.orientationHandler = () => {
      setTimeout(() => {
        this.handleViewportChange();
      }, 100); // Delay to ensure viewport dimensions are updated
    };

    window.addEventListener('orientationchange', this.orientationHandler);

    // Modern approach
    if (screen.orientation) {
      screen.orientation.addEventListener('change', this.orientationHandler);
    }
  }

  private handleViewportChange(): void {
    const oldViewport = { ...this.currentViewport };
    this.currentViewport = this.getCurrentViewportInfo();

    // Check if breakpoint changed
    if (oldViewport.currentBreakpoint !== this.currentViewport.currentBreakpoint) {
      this.handleBreakpointChange(this.currentViewport.currentBreakpoint);
    }

    // Check if orientation changed
    if (oldViewport.orientation !== this.currentViewport.orientation) {
      this.handleOrientationChange(this.currentViewport.orientation);
    }

    this.emit('viewport_changed', {
      oldViewport,
      newViewport: this.currentViewport,
    });

    logger.debug('Viewport changed', {
      size: `${this.currentViewport.width}x${this.currentViewport.height}`,
      breakpoint: this.currentViewport.currentBreakpoint,
      orientation: this.currentViewport.orientation,
    });
  }

  private handleBreakpointChange(newBreakpoint: string): void {
    // Update CSS custom properties
    this.updateBreakpointVariables(newBreakpoint);

    // Apply component-specific styles
    this.applyBreakpointStyles(newBreakpoint);

    // Update layout grid
    this.updateLayoutGrid(newBreakpoint);

    this.emit('breakpoint_changed', {
      breakpoint: newBreakpoint,
      viewport: this.currentViewport,
    });

    logger.debug('Breakpoint changed', { breakpoint: newBreakpoint });
  }

  private handleOrientationChange(newOrientation: string): void {
    document.documentElement.setAttribute('data-orientation', newOrientation);

    this.emit('orientation_changed', {
      orientation: newOrientation,
      viewport: this.currentViewport,
    });

    logger.debug('Orientation changed', { orientation: newOrientation });
  }

  private applyResponsiveStyles(): void {
    const root = document.documentElement;

    // Set initial breakpoint
    root.setAttribute('data-breakpoint', this.currentViewport.currentBreakpoint);
    root.setAttribute('data-orientation', this.currentViewport.orientation);

    // Set touch class
    if (this.currentViewport.isTouch) {
      root.classList.add('touch-device');
    } else {
      root.classList.add('no-touch');
    }

    // Set retina class
    if (this.currentViewport.isRetina) {
      root.classList.add('retina');
    }

    // Set pixel ratio
    root.style.setProperty('--pixel-ratio', this.currentViewport.pixelRatio.toString());

    // Update breakpoint variables
    this.updateBreakpointVariables(this.currentViewport.currentBreakpoint);
  }

  private updateBreakpointVariables(breakpointName: string): void {
    const breakpoint = this.config.breakpoints.find(bp => bp.name === breakpointName);
    if (!breakpoint) return;

    const root = document.documentElement;
    root.style.setProperty('--current-breakpoint', breakpointName);
    root.style.setProperty('--grid-columns', breakpoint.columns.toString());
    root.style.setProperty('--grid-gutter', `${breakpoint.gutter}px`);
    root.style.setProperty('--grid-margin', `${breakpoint.margin}px`);
    root.style.setProperty('--viewport-width', `${this.currentViewport.width}px`);
    root.style.setProperty('--viewport-height', `${this.currentViewport.height}px`);
  }

  private setupFluidTypography(): void {
    const root = document.documentElement;

    // Calculate fluid font sizes
    const minSize = 16; // Base font size
    const maxSize = 24; // Maximum font size
    const minViewport = 320; // Minimum viewport width
    const maxViewport = 1200; // Maximum viewport width

    // Clamp function for fluid typography
    const fluidSize = `clamp(${minSize}px, ${minSize}px + (${maxSize} - ${minSize}) * ((100vw - ${minViewport}px) / (${maxViewport} - ${minViewport})), ${maxSize}px)`;

    root.style.setProperty('--fluid-font-size', fluidSize);
    root.style.setProperty('--fluid-ratio', this.config.fluidTypographyRatio.toString());

    // Generate fluid scale
    for (let i = 1; i <= 6; i++) {
      const scale = Math.pow(this.config.fluidTypographyRatio, i);
      const fluidHeading = `clamp(${minSize * scale * 0.8}px, ${minSize}px + (${maxSize * scale} - ${minSize}) * ((100vw - ${minViewport}px) / (${maxViewport} - ${minViewport})), ${maxSize * scale}px)`;
      root.style.setProperty(`--fluid-h${i}`, fluidHeading);
    }
  }

  private setupTouchOptimization(): void {
    if (!this.currentViewport.isTouch) return;

    const root = document.documentElement;
    root.style.setProperty('--touch-target-min-size', `${this.config.touchTargetMinSize}px`);

    // Optimize touch targets
    const interactiveElements = document.querySelectorAll(
      'button, a, input, select, textarea, [role="button"]'
    );

    interactiveElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const rect = htmlElement.getBoundingClientRect();

      if (
        rect.width < this.config.touchTargetMinSize ||
        rect.height < this.config.touchTargetMinSize
      ) {
        htmlElement.classList.add('touch-optimized');
      }
    });
  }

  public registerComponent(
    id: string,
    element: HTMLElement,
    options: {
      breakpointStyles?: Map<string, Partial<CSSStyleDeclaration>>;
      fluidProperties?: string[];
      touchOptimized?: boolean;
    } = {}
  ): void {
    const component: ResponsiveComponent = {
      id,
      element,
      breakpointStyles: options.breakpointStyles || new Map(),
      fluidProperties: options.fluidProperties || [],
      touchOptimized: options.touchOptimized || false,
    };

    this.components.set(id, component);

    // Apply current breakpoint styles
    this.applyComponentStyles(component, this.currentViewport.currentBreakpoint);

    logger.debug('Responsive component registered', {
      id,
      breakpointStyles: component.breakpointStyles.size,
    });
  }

  public unregisterComponent(id: string): void {
    this.components.delete(id);
    logger.debug('Responsive component unregistered', { id });
  }

  private applyBreakpointStyles(breakpoint: string): void {
    this.components.forEach(component => {
      this.applyComponentStyles(component, breakpoint);
    });
  }

  private applyComponentStyles(component: ResponsiveComponent, breakpoint: string): void {
    const styles = component.breakpointStyles.get(breakpoint);
    if (!styles) return;

    Object.entries(styles).forEach(([property, value]) => {
      if (value !== undefined) {
        component.element.style.setProperty(property, value);
      }
    });
  }

  private updateLayoutGrid(breakpoint: string): void {
    const breakpointConfig = this.config.breakpoints.find(bp => bp.name === breakpoint);
    if (!breakpointConfig) return;

    const grid: LayoutGrid = {
      columns: breakpointConfig.columns,
      gutter: breakpointConfig.gutter,
      margin: breakpointConfig.margin,
      breakpoint,
    };

    this.emit('layout_grid_updated', grid);
  }

  public generateResponsiveCSS(): string {
    let css = '';

    // Generate breakpoint media queries
    this.config.breakpoints.forEach(breakpoint => {
      const query = breakpoint.maxWidth
        ? `@media (min-width: ${breakpoint.minWidth}px) and (max-width: ${breakpoint.maxWidth}px)`
        : `@media (min-width: ${breakpoint.minWidth}px)`;

      css += `
${query} {
  :root {
    --current-breakpoint: ${breakpoint.name};
    --grid-columns: ${breakpoint.columns};
    --grid-gutter: ${breakpoint.gutter}px;
    --grid-margin: ${breakpoint.margin}px;
  }
  
  .container {
    max-width: ${breakpoint.maxWidth || 'none'};
    margin: 0 auto;
    padding: 0 ${breakpoint.margin}px;
  }
  
  .grid {
    display: grid;
    grid-template-columns: repeat(${breakpoint.columns}, 1fr);
    gap: ${breakpoint.gutter}px;
  }
}`;
    });

    // Touch optimization styles
    if (this.config.enableTouchOptimization) {
      css += `
.touch-device .touch-optimized {
  min-width: ${this.config.touchTargetMinSize}px;
  min-height: ${this.config.touchTargetMinSize}px;
  padding: 8px;
}

.touch-device button,
.touch-device a,
.touch-device [role="button"] {
  min-height: ${this.config.touchTargetMinSize}px;
}`;
    }

    // Fluid typography styles
    if (this.config.enableFluidTypography) {
      css += `
body {
  font-size: var(--fluid-font-size);
}

h1 { font-size: var(--fluid-h1); }
h2 { font-size: var(--fluid-h2); }
h3 { font-size: var(--fluid-h3); }
h4 { font-size: var(--fluid-h4); }
h5 { font-size: var(--fluid-h5); }
h6 { font-size: var(--fluid-h6); }`;
    }

    return css;
  }

  public getBreakpointInfo(breakpointName?: string): Breakpoint | null {
    const name = breakpointName || this.currentViewport.currentBreakpoint;
    return this.config.breakpoints.find(bp => bp.name === name) || null;
  }

  public isBreakpointActive(breakpointName: string): boolean {
    return this.currentViewport.currentBreakpoint === breakpointName;
  }

  public isViewportSmallerThan(breakpointName: string): boolean {
    const breakpoint = this.config.breakpoints.find(bp => bp.name === breakpointName);
    return breakpoint ? this.currentViewport.width < breakpoint.minWidth : false;
  }

  public isViewportLargerThan(breakpointName: string): boolean {
    const breakpoint = this.config.breakpoints.find(bp => bp.name === breakpointName);
    return breakpoint
      ? this.currentViewport.width > (breakpoint.maxWidth || breakpoint.minWidth)
      : false;
  }

  // Getters
  public getCurrentViewport(): ViewportInfo {
    return { ...this.currentViewport };
  }

  public getBreakpoints(): Breakpoint[] {
    return [...this.config.breakpoints];
  }

  public getComponents(): ResponsiveComponent[] {
    return Array.from(this.components.values());
  }

  public updateConfig(config: Partial<ResponsiveConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('responsive', this.config);

    // Reapply responsive styles
    this.applyResponsiveStyles();

    this.emit('config_updated', this.config);
  }

  public getConfig(): ResponsiveConfig {
    return { ...this.config };
  }

  public destroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    if (this.orientationHandler) {
      window.removeEventListener('orientationchange', this.orientationHandler);
      if (screen.orientation) {
        screen.orientation.removeEventListener('change', this.orientationHandler);
      }
    }

    this.mediaQueryLists.forEach(mql => {
      mql.removeEventListener('change', () => {});
    });

    this.removeAllListeners();
  }
}

// Export singleton instance
export const responsiveDesignSystem = ResponsiveDesignSystem.getInstance();
