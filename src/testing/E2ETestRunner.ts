/**
 * E2E Test Runner с поддержкой Playwright и автоматизированного тестирования
 */

import { <PERSON><PERSON><PERSON>, BrowserContext, Page, chromium, firefox, webkit } from 'playwright';

export interface E2EConfig {
  browsers: ('chromium' | 'firefox' | 'webkit')[];
  headless: boolean;
  viewport: { width: number; height: number };
  timeout: number;
  retries: number;
  baseURL: string;
  screenshotOnFailure: boolean;
  videoRecording: boolean;
  tracing: boolean;
  slowMo: number;
}

export interface E2ETestCase {
  id: string;
  name: string;
  description: string;
  tags: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  steps: E2EStep[];
  assertions: E2EAssertion[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
}

export interface E2EStep {
  id: string;
  description: string;
  action:
    | 'navigate'
    | 'click'
    | 'type'
    | 'select'
    | 'wait'
    | 'scroll'
    | 'hover'
    | 'drag'
    | 'upload'
    | 'custom';
  selector?: string;
  value?: string;
  options?: Record<string, any>;
  timeout?: number;
  screenshot?: boolean;
}

export interface E2EAssertion {
  id: string;
  description: string;
  type:
    | 'visible'
    | 'hidden'
    | 'text'
    | 'value'
    | 'attribute'
    | 'count'
    | 'url'
    | 'title'
    | 'custom';
  selector?: string;
  expected: any;
  timeout?: number;
}

export interface E2EResult {
  testId: string;
  browser: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: Error;
  screenshots: string[];
  video?: string;
  trace?: string;
  stepResults: StepResult[];
}

export interface StepResult {
  stepId: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: Error;
  screenshot?: string;
}

export class E2ETestRunner {
  private config: E2EConfig;
  private browsers: Map<string, Browser> = new Map();
  private contexts: Map<string, BrowserContext> = new Map();
  private pages: Map<string, Page> = new Map();

  constructor(config: Partial<E2EConfig> = {}) {
    this.config = {
      browsers: ['chromium'],
      headless: true,
      viewport: { width: 1280, height: 720 },
      timeout: 30000,
      retries: 2,
      baseURL: 'http://localhost:3000',
      screenshotOnFailure: true,
      videoRecording: false,
      tracing: false,
      slowMo: 0,
      ...config,
    };
  }

  /**
   * Инициализирует браузеры
   */
  async initialize(): Promise<void> {
    console.log('🚀 Initializing E2E test environment...');

    for (const browserName of this.config.browsers) {
      let browser: Browser;

      switch (browserName) {
        case 'chromium':
          browser = await chromium.launch({
            headless: this.config.headless,
            slowMo: this.config.slowMo,
          });
          break;
        case 'firefox':
          browser = await firefox.launch({
            headless: this.config.headless,
            slowMo: this.config.slowMo,
          });
          break;
        case 'webkit':
          browser = await webkit.launch({
            headless: this.config.headless,
            slowMo: this.config.slowMo,
          });
          break;
        default:
          throw new Error(`Unsupported browser: ${browserName}`);
      }

      this.browsers.set(browserName, browser);
      console.log(`✅ ${browserName} browser initialized`);
    }
  }

  /**
   * Создает новый контекст браузера
   */
  async createContext(browserName: string, options: any = {}): Promise<BrowserContext> {
    const browser = this.browsers.get(browserName);
    if (!browser) {
      throw new Error(`Browser ${browserName} not initialized`);
    }

    const context = await browser.newContext({
      viewport: this.config.viewport,
      recordVideo: this.config.videoRecording ? { dir: 'test-results/videos' } : undefined,
      ...options,
    });

    if (this.config.tracing) {
      await context.tracing.start({ screenshots: true, snapshots: true });
    }

    const contextId = `${browserName}-${Date.now()}`;
    this.contexts.set(contextId, context);

    return context;
  }

  /**
   * Создает новую страницу
   */
  async createPage(context: BrowserContext): Promise<Page> {
    const page = await context.newPage();

    // Устанавливаем таймауты
    page.setDefaultTimeout(this.config.timeout);
    page.setDefaultNavigationTimeout(this.config.timeout);

    // Настраиваем обработчики событий
    page.on('console', msg => {
      console.log(`[Browser Console] ${msg.type()}: ${msg.text()}`);
    });

    page.on('pageerror', error => {
      console.error(`[Page Error] ${error.message}`);
    });

    page.on('requestfailed', request => {
      console.warn(`[Request Failed] ${request.url()}: ${request.failure()?.errorText}`);
    });

    const pageId = `page-${Date.now()}`;
    this.pages.set(pageId, page);

    return page;
  }

  /**
   * Запускает E2E тесты
   */
  async runTests(tests: E2ETestCase[]): Promise<Map<string, E2EResult[]>> {
    const results = new Map<string, E2EResult[]>();

    for (const browserName of this.config.browsers) {
      console.log(`🌐 Running tests in ${browserName}...`);
      const browserResults: E2EResult[] = [];

      for (const test of tests) {
        const result = await this.runTest(browserName, test);
        browserResults.push(result);
      }

      results.set(browserName, browserResults);
    }

    return results;
  }

  /**
   * Запускает один E2E тест
   */
  async runTest(browserName: string, test: E2ETestCase): Promise<E2EResult> {
    const startTime = performance.now();
    let status: 'passed' | 'failed' | 'skipped' = 'passed';
    let error: Error | undefined;
    const screenshots: string[] = [];
    const stepResults: StepResult[] = [];
    let video: string | undefined;
    let trace: string | undefined;

    console.log(`🧪 Running test: ${test.name} in ${browserName}`);

    let context: BrowserContext | undefined;
    let page: Page | undefined;

    try {
      // Создаем контекст и страницу
      context = await this.createContext(browserName);
      page = await this.createPage(context);

      // Выполняем setup
      if (test.setup) {
        await test.setup();
      }

      // Выполняем шаги теста
      for (const step of test.steps) {
        const stepResult = await this.executeStep(page, step);
        stepResults.push(stepResult);

        if (stepResult.status === 'failed') {
          throw stepResult.error || new Error(`Step ${step.id} failed`);
        }

        if (stepResult.screenshot) {
          screenshots.push(stepResult.screenshot);
        }
      }

      // Выполняем проверки
      for (const assertion of test.assertions) {
        await this.executeAssertion(page, assertion);
      }
    } catch (err) {
      error = err as Error;
      status = 'failed';

      // Делаем скриншот при ошибке
      if (this.config.screenshotOnFailure && page) {
        const screenshotPath = `test-results/screenshots/${test.id}-${browserName}-failure.png`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        screenshots.push(screenshotPath);
      }
    } finally {
      // Выполняем teardown
      if (test.teardown) {
        try {
          await test.teardown();
        } catch (teardownError) {
          console.error(`Teardown error for test ${test.name}:`, teardownError);
        }
      }

      // Сохраняем трейс
      if (this.config.tracing && context) {
        trace = `test-results/traces/${test.id}-${browserName}.zip`;
        await context.tracing.stop({ path: trace });
      }

      // Получаем видео
      if (this.config.videoRecording && page) {
        video = await page.video()?.path();
      }

      // Закрываем страницу и контекст
      if (page) {
        await page.close();
      }
      if (context) {
        await context.close();
      }
    }

    const duration = performance.now() - startTime;

    const result: E2EResult = {
      testId: test.id,
      browser: browserName,
      status,
      duration,
      error,
      screenshots,
      video,
      trace,
      stepResults,
    };

    // Выводим результат
    const statusIcon = status === 'passed' ? '✅' : '❌';
    console.log(`${statusIcon} ${test.name} in ${browserName} (${duration.toFixed(2)}ms)`);

    if (error) {
      console.error(`   Error: ${error.message}`);
    }

    return result;
  }

  /**
   * Выполняет шаг теста
   */
  private async executeStep(page: Page, step: E2EStep): Promise<StepResult> {
    const startTime = performance.now();
    let status: 'passed' | 'failed' | 'skipped' = 'passed';
    let error: Error | undefined;
    let screenshot: string | undefined;

    try {
      console.log(`  📝 Executing step: ${step.description}`);

      switch (step.action) {
        case 'navigate':
          await page.goto(step.value || this.config.baseURL, {
            timeout: step.timeout || this.config.timeout,
          });
          break;

        case 'click':
          if (!step.selector) throw new Error('Selector required for click action');
          await page.click(step.selector, {
            timeout: step.timeout || this.config.timeout,
            ...step.options,
          });
          break;

        case 'type':
          if (!step.selector || !step.value) {
            throw new Error('Selector and value required for type action');
          }
          await page.fill(step.selector, step.value, {
            timeout: step.timeout || this.config.timeout,
          });
          break;

        case 'select':
          if (!step.selector || !step.value) {
            throw new Error('Selector and value required for select action');
          }
          await page.selectOption(step.selector, step.value, {
            timeout: step.timeout || this.config.timeout,
          });
          break;

        case 'wait':
          if (step.selector) {
            await page.waitForSelector(step.selector, {
              timeout: step.timeout || this.config.timeout,
              ...step.options,
            });
          } else if (step.value) {
            await page.waitForTimeout(parseInt(step.value));
          }
          break;

        case 'scroll':
          if (step.selector) {
            await page.locator(step.selector).scrollIntoViewIfNeeded();
          } else {
            await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
          }
          break;

        case 'hover':
          if (!step.selector) throw new Error('Selector required for hover action');
          await page.hover(step.selector, {
            timeout: step.timeout || this.config.timeout,
          });
          break;

        case 'drag':
          if (!step.selector || !step.value) {
            throw new Error('Selector and target required for drag action');
          }
          await page.dragAndDrop(step.selector, step.value, {
            timeout: step.timeout || this.config.timeout,
          });
          break;

        case 'upload':
          if (!step.selector || !step.value) {
            throw new Error('Selector and file path required for upload action');
          }
          await page.setInputFiles(step.selector, step.value);
          break;

        case 'custom':
          // Выполнение кастомного кода
          if (step.options?.customFunction) {
            await step.options.customFunction(page);
          }
          break;

        default:
          throw new Error(`Unknown action: ${step.action}`);
      }

      // Делаем скриншот если требуется
      if (step.screenshot) {
        screenshot = `test-results/screenshots/step-${step.id}.png`;
        await page.screenshot({ path: screenshot });
      }
    } catch (err) {
      error = err as Error;
      status = 'failed';
    }

    const duration = performance.now() - startTime;

    return {
      stepId: step.id,
      status,
      duration,
      error,
      screenshot,
    };
  }

  /**
   * Выполняет проверку
   */
  private async executeAssertion(page: Page, assertion: E2EAssertion): Promise<void> {
    console.log(`  🔍 Checking assertion: ${assertion.description}`);

    switch (assertion.type) {
      case 'visible':
        if (!assertion.selector) throw new Error('Selector required for visible assertion');
        await page.waitForSelector(assertion.selector, {
          state: 'visible',
          timeout: assertion.timeout || this.config.timeout,
        });
        break;

      case 'hidden':
        if (!assertion.selector) throw new Error('Selector required for hidden assertion');
        await page.waitForSelector(assertion.selector, {
          state: 'hidden',
          timeout: assertion.timeout || this.config.timeout,
        });
        break;

      case 'text':
        if (!assertion.selector) throw new Error('Selector required for text assertion');
        const text = await page.textContent(assertion.selector);
        if (text !== assertion.expected) {
          throw new Error(`Expected text "${assertion.expected}", got "${text}"`);
        }
        break;

      case 'value':
        if (!assertion.selector) throw new Error('Selector required for value assertion');
        const value = await page.inputValue(assertion.selector);
        if (value !== assertion.expected) {
          throw new Error(`Expected value "${assertion.expected}", got "${value}"`);
        }
        break;

      case 'attribute':
        if (!assertion.selector) throw new Error('Selector required for attribute assertion');
        const attr = await page.getAttribute(assertion.selector, assertion.expected.name);
        if (attr !== assertion.expected.value) {
          throw new Error(
            `Expected attribute "${assertion.expected.name}" to be "${assertion.expected.value}", got "${attr}"`
          );
        }
        break;

      case 'count':
        if (!assertion.selector) throw new Error('Selector required for count assertion');
        const count = await page.locator(assertion.selector).count();
        if (count !== assertion.expected) {
          throw new Error(`Expected ${assertion.expected} elements, found ${count}`);
        }
        break;

      case 'url':
        const url = page.url();
        if (url !== assertion.expected) {
          throw new Error(`Expected URL "${assertion.expected}", got "${url}"`);
        }
        break;

      case 'title':
        const title = await page.title();
        if (title !== assertion.expected) {
          throw new Error(`Expected title "${assertion.expected}", got "${title}"`);
        }
        break;

      case 'custom':
        // Выполнение кастомной проверки
        if (assertion.expected?.customFunction) {
          await assertion.expected.customFunction(page);
        }
        break;

      default:
        throw new Error(`Unknown assertion type: ${assertion.type}`);
    }
  }

  /**
   * Закрывает все браузеры
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up E2E test environment...');

    // Закрываем все страницы
    for (const page of this.pages.values()) {
      await page.close();
    }

    // Закрываем все контексты
    for (const context of this.contexts.values()) {
      await context.close();
    }

    // Закрываем все браузеры
    for (const browser of this.browsers.values()) {
      await browser.close();
    }

    this.pages.clear();
    this.contexts.clear();
    this.browsers.clear();

    console.log('✅ E2E test environment cleaned up');
  }

  /**
   * Генерирует отчет по E2E тестам
   */
  generateReport(results: Map<string, E2EResult[]>): void {
    console.log('\n📊 E2E Test Results Summary:');

    for (const [browser, browserResults] of results) {
      const passed = browserResults.filter(r => r.status === 'passed').length;
      const failed = browserResults.filter(r => r.status === 'failed').length;
      const total = browserResults.length;

      console.log(`\n🌐 ${browser.toUpperCase()}:`);
      console.log(`  ✅ Passed: ${passed}`);
      console.log(`  ❌ Failed: ${failed}`);
      console.log(`  📈 Total: ${total}`);
      console.log(`  🎯 Success Rate: ${((passed / total) * 100).toFixed(2)}%`);

      if (failed > 0) {
        console.log(`  ❌ Failed Tests:`);
        browserResults
          .filter(r => r.status === 'failed')
          .forEach(result => {
            console.log(`     • Test ${result.testId}: ${result.error?.message}`);
          });
      }
    }
  }
}

// Утилиты для создания E2E тестов
export class E2ETestBuilder {
  private test: Partial<E2ETestCase> = {
    steps: [],
    assertions: [],
    tags: [],
  };

  static create(name: string): E2ETestBuilder {
    const builder = new E2ETestBuilder();
    builder.test.id = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    builder.test.name = name;
    return builder;
  }

  description(desc: string): E2ETestBuilder {
    this.test.description = desc;
    return this;
  }

  priority(priority: 'low' | 'medium' | 'high' | 'critical'): E2ETestBuilder {
    this.test.priority = priority;
    return this;
  }

  tag(...tags: string[]): E2ETestBuilder {
    this.test.tags = [...(this.test.tags || []), ...tags];
    return this;
  }

  navigate(url: string): E2ETestBuilder {
    this.test.steps!.push({
      id: `step-${this.test.steps!.length}`,
      description: `Navigate to ${url}`,
      action: 'navigate',
      value: url,
    });
    return this;
  }

  click(selector: string, description?: string): E2ETestBuilder {
    this.test.steps!.push({
      id: `step-${this.test.steps!.length}`,
      description: description || `Click ${selector}`,
      action: 'click',
      selector,
    });
    return this;
  }

  type(selector: string, value: string, description?: string): E2ETestBuilder {
    this.test.steps!.push({
      id: `step-${this.test.steps!.length}`,
      description: description || `Type "${value}" into ${selector}`,
      action: 'type',
      selector,
      value,
    });
    return this;
  }

  expectVisible(selector: string, description?: string): E2ETestBuilder {
    this.test.assertions!.push({
      id: `assertion-${this.test.assertions!.length}`,
      description: description || `Expect ${selector} to be visible`,
      type: 'visible',
      selector,
      expected: true,
    });
    return this;
  }

  expectText(selector: string, text: string, description?: string): E2ETestBuilder {
    this.test.assertions!.push({
      id: `assertion-${this.test.assertions!.length}`,
      description: description || `Expect ${selector} to contain text "${text}"`,
      type: 'text',
      selector,
      expected: text,
    });
    return this;
  }

  build(): E2ETestCase {
    if (!this.test.name || !this.test.id) {
      throw new Error('Test name and ID are required');
    }

    return this.test as E2ETestCase;
  }
}

// Глобальный экземпляр
export const e2eRunner = new E2ETestRunner();
