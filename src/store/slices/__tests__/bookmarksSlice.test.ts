import bookmarksSlice, { addBookmark, removeBookmark } from '../bookmarksSlice';

describe('bookmarksSlice', () => {
  const initialState = {
    bookmarks: [],
  };

  it('добавляет новую закладку', () => {
    const newBookmark = { id: '1', title: 'Яндекс', url: 'https://yandex.ru' };
    const action = addBookmark(newBookmark);
    const state = bookmarksSlice(initialState, action);
    expect(state.bookmarks).toHaveLength(1);
    expect(state.bookmarks[0].url).toBe('https://yandex.ru');
  });

  it('удаляет закладку', () => {
    const stateWithBookmark = {
      bookmarks: [{ id: '1', title: 'Яндекс', url: 'https://yandex.ru' }],
    };
    const action = removeBookmark('1');
    const state = bookmarksSlice(stateWithBookmark, action);
    expect(state.bookmarks).toHaveLength(0);
  });
});
