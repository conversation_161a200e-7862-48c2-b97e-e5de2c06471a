import themeReducer, {
  deletePreset,
  loadPreset,
  savePreset,
  setAnimationSettings,
  setColorScheme,
  setCustomColors,
  setThemeMode,
  setTypography,
} from '../themeSlice';

describe('themeSlice', () => {
  const initialState = {
    settings: {
      mode: 'light',
      colorScheme: 'default',
      customColors: {
        primary: '#1976d2',
        secondary: '#dc004e',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196f3',
        success: '#4caf50',
      },
      typography: {
        fontFamily: 'Roboto',
        fontSize: 14,
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 700,
      },
      animation: {
        enabled: true,
        duration: 300,
        easing: 'ease-in-out',
      },
      borderRadius: 4,
      spacing: 8,
      shadows: {
        xs: '0 2px 4px rgba(0,0,0,0.1)',
        sm: '0 4px 8px rgba(0,0,0,0.1)',
        md: '0 8px 16px rgba(0,0,0,0.1)',
        lg: '0 16px 24px rgba(0,0,0,0.1)',
        xl: '0 24px 32px rgba(0,0,0,0.1)',
      },
      transitions: {
        duration: 300,
        easing: 'ease-in-out',
      },
    },
    presets: {
      default: {
        name: 'Default',
        settings: {
          mode: 'light',
          colorScheme: 'default',
          customColors: {
            primary: '#1976d2',
            secondary: '#dc004e',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196f3',
            success: '#4caf50',
          },
          typography: {
            fontFamily: 'Roboto',
            fontSize: 14,
            fontWeightLight: 300,
            fontWeightRegular: 400,
            fontWeightMedium: 500,
            fontWeightBold: 700,
          },
          animation: {
            enabled: true,
            duration: 300,
            easing: 'ease-in-out',
          },
          borderRadius: 4,
          spacing: 8,
          shadows: {
            xs: '0 2px 4px rgba(0,0,0,0.1)',
            sm: '0 4px 8px rgba(0,0,0,0.1)',
            md: '0 8px 16px rgba(0,0,0,0.1)',
            lg: '0 16px 24px rgba(0,0,0,0.1)',
            xl: '0 24px 32px rgba(0,0,0,0.1)',
          },
          transitions: {
            duration: 300,
            easing: 'ease-in-out',
          },
        },
      },
    },
  };

  it('should handle initial state', () => {
    expect(themeReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle setThemeMode', () => {
    const actual = themeReducer(initialState, setThemeMode('dark'));
    expect(actual.settings.mode).toEqual('dark');
  });

  it('should handle setColorScheme', () => {
    const actual = themeReducer(initialState, setColorScheme('custom'));
    expect(actual.settings.colorScheme).toEqual('custom');
  });

  it('should handle setCustomColors', () => {
    const newColors = {
      primary: '#000000',
      secondary: '#ffffff',
    };
    const actual = themeReducer(initialState, setCustomColors(newColors));
    expect(actual.settings.customColors).toEqual({
      ...initialState.settings.customColors,
      ...newColors,
    });
  });

  it('should handle setTypography', () => {
    const newTypography = {
      fontFamily: 'Arial',
      fontSize: 16,
    };
    const actual = themeReducer(initialState, setTypography(newTypography));
    expect(actual.settings.typography).toEqual({
      ...initialState.settings.typography,
      ...newTypography,
    });
  });

  it('should handle setAnimationSettings', () => {
    const newAnimation = {
      enabled: false,
      duration: 500,
    };
    const actual = themeReducer(initialState, setAnimationSettings(newAnimation));
    expect(actual.settings.animation).toEqual({
      ...initialState.settings.animation,
      ...newAnimation,
    });
  });

  it('should handle savePreset', () => {
    const preset = {
      name: 'Test Preset',
      settings: initialState.settings,
    };
    const actual = themeReducer(initialState, savePreset(preset));
    expect(actual.presets['test-preset']).toEqual(preset);
  });

  it('should handle deletePreset', () => {
    const stateWithPreset = {
      ...initialState,
      presets: {
        ...initialState.presets,
        'test-preset': {
          name: 'Test Preset',
          settings: initialState.settings,
        },
      },
    };
    const actual = themeReducer(stateWithPreset, deletePreset('test-preset'));
    expect(actual.presets['test-preset']).toBeUndefined();
  });

  it('should handle loadPreset', () => {
    const preset = {
      name: 'Test Preset',
      settings: {
        ...initialState.settings,
        mode: 'dark',
      },
    };
    const stateWithPreset = {
      ...initialState,
      presets: {
        ...initialState.presets,
        'test-preset': preset,
      },
    };
    const actual = themeReducer(stateWithPreset, loadPreset('test-preset'));
    expect(actual.settings).toEqual(preset.settings);
  });
});
