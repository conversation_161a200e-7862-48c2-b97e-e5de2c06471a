import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export interface Download {
  id: string;
  filename: string;
  url: string;
  size: number;
  progress: number;
  status: 'downloading' | 'paused' | 'completed' | 'error';
  speed: number;
  startTime: number;
  endTime?: number;
  error?: string;
}

interface DownloadsState {
  items: Download[];
}

const initialState: DownloadsState = {
  items: [],
};

const downloadsSlice = createSlice({
  name: 'downloads',
  initialState,
  reducers: {
    addDownload: (
      state,
      action: PayloadAction<Omit<Download, 'id' | 'progress' | 'status' | 'speed' | 'startTime'>>
    ) => {
      state.items.push({
        ...action.payload,
        id: Date.now().toString(),
        progress: 0,
        status: 'downloading',
        speed: 0,
        startTime: Date.now(),
      });
    },
    updateDownload: (state, action: PayloadAction<Partial<Download> & { id: string }>) => {
      const index = state.items.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = { ...state.items[index], ...action.payload };
      }
    },
    removeDownload: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    },
    pauseDownload: (state, action: PayloadAction<string>) => {
      const download = state.items.find(item => item.id === action.payload);
      if (download && download.status === 'downloading') {
        download.status = 'paused';
      }
    },
    resumeDownload: (state, action: PayloadAction<string>) => {
      const download = state.items.find(item => item.id === action.payload);
      if (download && download.status === 'paused') {
        download.status = 'downloading';
      }
    },
    clearDownloads: state => {
      state.items = [];
    },
  },
});

export const {
  addDownload,
  updateDownload,
  removeDownload,
  pauseDownload,
  resumeDownload,
  clearDownloads,
} = downloadsSlice.actions;

export default downloadsSlice.reducer;
