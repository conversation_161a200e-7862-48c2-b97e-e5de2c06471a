import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
}

interface TabState {
  tabs: Tab[];
  activeTabIndex: number;
}

const initialState: TabState = {
  tabs: [
    {
      id: '1',
      url: 'about:blank',
      title: 'New Tab',
      isLoading: false,
      canGoBack: false,
      canGoForward: false,
    },
  ],
  activeTabIndex: 0,
};

const tabSlice = createSlice({
  name: 'tabs',
  initialState,
  reducers: {
    addTab: (
      state,
      action: PayloadAction<Omit<Tab, 'id' | 'isLoading' | 'canGoBack' | 'canGoForward'>>
    ) => {
      const newTab: Tab = {
        ...action.payload,
        id: Date.now().toString(),
        isLoading: false,
        canGoBack: false,
        canGoForward: false,
      };
      state.tabs.push(newTab);
      state.activeTabIndex = state.tabs.length - 1;
    },
    closeTab: (state, action: PayloadAction<number>) => {
      state.tabs.splice(action.payload, 1);
      if (state.activeTabIndex >= state.tabs.length) {
        state.activeTabIndex = state.tabs.length - 1;
      }
    },
    setActiveTab: (state, action: PayloadAction<number>) => {
      state.activeTabIndex = action.payload;
    },
    updateTab: (state, action: PayloadAction<{ index: number; updates: Partial<Tab> }>) => {
      const { index, updates } = action.payload;
      if (state.tabs[index]) {
        state.tabs[index] = { ...state.tabs[index], ...updates };
      }
    },
    updateTabNavigation: (
      state,
      action: PayloadAction<{ index: number; canGoBack: boolean; canGoForward: boolean }>
    ) => {
      const { index, canGoBack, canGoForward } = action.payload;
      if (state.tabs[index]) {
        state.tabs[index].canGoBack = canGoBack;
        state.tabs[index].canGoForward = canGoForward;
      }
    },
  },
});

export const { addTab, closeTab, setActiveTab, updateTab, updateTabNavigation } = tabSlice.actions;
export default tabSlice.reducer;
