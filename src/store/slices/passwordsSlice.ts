import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export interface Password {
  id: string;
  url: string;
  username: string;
  password: string;
  isVisible: boolean;
  lastUsed: number;
  lastChanged: number;
  strength: 'weak' | 'medium' | 'strong';
  notes?: string;
}

interface PasswordsState {
  items: Password[];
  loading: boolean;
  error: string | null;
}

const initialState: PasswordsState = {
  items: [],
  loading: false,
  error: null,
};

const passwordsSlice = createSlice({
  name: 'passwords',
  initialState,
  reducers: {
    addPassword: (state, action: PayloadAction<Password>) => {
      state.items.push(action.payload);
    },
    removePassword: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    },
    updatePassword: (state, action: PayloadAction<Password>) => {
      const index = state.items.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = action.payload;
      }
    },
    togglePasswordVisibility: (state, action: PayloadAction<string>) => {
      const password = state.items.find(item => item.id === action.payload);
      if (password) {
        password.isVisible = !password.isVisible;
      }
    },
    updateLastUsed: (state, action: PayloadAction<string>) => {
      const password = state.items.find(item => item.id === action.payload);
      if (password) {
        password.lastUsed = Date.now();
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  addPassword,
  removePassword,
  updatePassword,
  togglePasswordVisibility,
  updateLastUsed,
  setLoading,
  setError,
} = passwordsSlice.actions;

export default passwordsSlice.reducer;
