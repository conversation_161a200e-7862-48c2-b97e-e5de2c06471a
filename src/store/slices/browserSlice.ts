import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { RootState } from '../index';

export interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
  isActive: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isIncognito: boolean;
  groupId?: string;
}

interface BrowserState {
  tabs: Tab[];
  activeTabId: string | null;
  groups: {
    id: string;
    name: string;
    color: string;
  }[];
  downloads: {
    id: string;
    filename: string;
    url: string;
    status: 'pending' | 'downloading' | 'completed' | 'error';
    progress: number;
    error?: string;
  }[];
  isIncognito: boolean;
  isFullscreen: boolean;
  isMaximized: boolean;
  isMinimized: boolean;
  isFocused: boolean;
  isOnline: boolean;
  isUpdating: boolean;
  lastUpdateCheck: number | null;
  version: string;
  buildNumber: string;
  platform: string;
  architecture: string;
  memoryUsage: {
    total: number;
    used: number;
    free: number;
  };
  cpuUsage: number;
  networkStatus: {
    type: 'wifi' | 'ethernet' | 'cellular' | 'none';
    speed: number;
    isMetered: boolean;
  };
  permissions: {
    notifications: boolean;
    camera: boolean;
    microphone: boolean;
    location: boolean;
    clipboard: boolean;
  };
  security: {
    isSecure: boolean;
    hasValidCertificate: boolean;
    isPrivateBrowsing: boolean;
    isTrackingProtectionEnabled: boolean;
    isAdBlockerEnabled: boolean;
  };
  performance: {
    fps: number;
    memoryUsage: number;
    cpuUsage: number;
    networkLatency: number;
  };
  url: string;
  title: string;
  isLoading: boolean;
  history: string[];
  bookmarks: string[];
  settings: {
    defaultSearchEngine: string;
    homepage: string;
    enableJavaScript: boolean;
    enableCookies: boolean;
    enableImages: boolean;
    enablePlugins: boolean;
    enableJavascript: boolean;
    enableWebGL: boolean;
    enableWebRTC: boolean;
    enableNotifications: boolean;
    enableGeolocation: boolean;
    enableMicrophone: boolean;
    enableCamera: boolean;
    enablePopups: boolean;
    enableBackgroundSync: boolean;
    enableServiceWorkers: boolean;
    enablePushNotifications: boolean;
    enableWebUSB: boolean;
    enableWebBluetooth: boolean;
    enableWebMIDI: boolean;
    enableWebSerial: boolean;
    enableWebHID: boolean;
    enableWebNFC: boolean;
    enableWebXR: boolean;
    enableWebAssembly: boolean;
    enableWebGPU: boolean;
    enableWebNN: boolean;
    enableWebAuthn: boolean;
    enableWebCrypto: boolean;
    enableWebStorage: boolean;
    enableWebSQL: boolean;
    enableIndexedDB: boolean;
    enableCacheStorage: boolean;
    enableApplicationCache: boolean;
    enableFileSystem: boolean;
    enableFileSystemAccess: boolean;
    enableFileSystemSync: boolean;
    enableFileSystemQuota: boolean;
    enableFileSystemDirectory: boolean;
    enableFileSystemDirectoryReader: boolean;
    enableFileSystemDirectoryEntry: boolean;
    enableFileSystemFileEntry: boolean;
    enableFileSystemFileWriter: boolean;
    enableFileSystemFileReader: boolean;
    enableFileSystemFileSaver: boolean;
    enableFileSystemFileSystem: boolean;
    enableFileSystemFileSystemSync: boolean;
    enableFileSystemFileSystemQuota: boolean;
    enableFileSystemFileSystemDirectory: boolean;
    enableFileSystemFileSystemDirectoryReader: boolean;
    enableFileSystemFileSystemDirectoryEntry: boolean;
    enableFileSystemFileSystemFileEntry: boolean;
    enableFileSystemFileSystemFileWriter: boolean;
    enableFileSystemFileSystemFileReader: boolean;
    enableFileSystemFileSystemFileSaver: boolean;
  };
  currentUrl: string;
  canGoBack: boolean;
  canGoForward: boolean;
  zoomLevel: number;
}

const initialState: BrowserState = {
  tabs: [],
  activeTabId: null,
  groups: [],
  downloads: [],
  isIncognito: false,
  isFullscreen: false,
  isMaximized: false,
  isMinimized: false,
  isFocused: true,
  isOnline: navigator.onLine,
  isUpdating: false,
  lastUpdateCheck: null,
  version: process.env.APP_VERSION || '1.0.0',
  buildNumber: process.env.BUILD_NUMBER || '1',
  platform: process.platform,
  architecture: process.arch,
  memoryUsage: {
    total: 0,
    used: 0,
    free: 0,
  },
  cpuUsage: 0,
  networkStatus: {
    type: 'none',
    speed: 0,
    isMetered: false,
  },
  permissions: {
    notifications: false,
    camera: false,
    microphone: false,
    location: false,
    clipboard: false,
  },
  security: {
    isSecure: true,
    hasValidCertificate: true,
    isPrivateBrowsing: false,
    isTrackingProtectionEnabled: true,
    isAdBlockerEnabled: true,
  },
  performance: {
    fps: 60,
    memoryUsage: 0,
    cpuUsage: 0,
    networkLatency: 0,
  },
  url: '',
  title: '',
  isLoading: false,
  history: [],
  bookmarks: [],
  settings: {
    defaultSearchEngine: 'google',
    homepage: 'https://www.google.com',
    enableJavaScript: true,
    enableCookies: true,
    enableImages: true,
    enablePlugins: true,
    enableJavascript: true,
    enableWebGL: true,
    enableWebRTC: true,
    enableNotifications: true,
    enableGeolocation: true,
    enableMicrophone: true,
    enableCamera: true,
    enablePopups: true,
    enableBackgroundSync: true,
    enableServiceWorkers: true,
    enablePushNotifications: true,
    enableWebUSB: true,
    enableWebBluetooth: true,
    enableWebMIDI: true,
    enableWebSerial: true,
    enableWebHID: true,
    enableWebNFC: true,
    enableWebXR: true,
    enableWebAssembly: true,
    enableWebGPU: true,
    enableWebNN: true,
    enableWebAuthn: true,
    enableWebCrypto: true,
    enableWebStorage: true,
    enableWebSQL: true,
    enableIndexedDB: true,
    enableCacheStorage: true,
    enableApplicationCache: true,
    enableFileSystem: true,
    enableFileSystemAccess: true,
    enableFileSystemSync: true,
    enableFileSystemQuota: true,
    enableFileSystemDirectory: true,
    enableFileSystemDirectoryReader: true,
    enableFileSystemDirectoryEntry: true,
    enableFileSystemFileEntry: true,
    enableFileSystemFileWriter: true,
    enableFileSystemFileReader: true,
    enableFileSystemFileSaver: true,
    enableFileSystemFileSystem: true,
    enableFileSystemFileSystemSync: true,
    enableFileSystemFileSystemQuota: true,
    enableFileSystemFileSystemDirectory: true,
    enableFileSystemFileSystemDirectoryReader: true,
    enableFileSystemFileSystemDirectoryEntry: true,
    enableFileSystemFileSystemFileEntry: true,
    enableFileSystemFileSystemFileWriter: true,
    enableFileSystemFileSystemFileReader: true,
    enableFileSystemFileSystemFileSaver: true,
  },
  currentUrl: '',
  canGoBack: false,
  canGoForward: false,
  zoomLevel: 1,
};

const browserSlice = createSlice({
  name: 'browser',
  initialState,
  reducers: {
    addTab: (state, action: PayloadAction<Omit<Tab, 'id'>>) => {
      const newTab: Tab = {
        ...action.payload,
        id: crypto.randomUUID(),
      };
      state.tabs.push(newTab);
      state.activeTabId = newTab.id;
    },
    removeTab: (state, action: PayloadAction<string>) => {
      const index = state.tabs.findIndex(tab => tab.id === action.payload);
      if (index !== -1) {
        state.tabs.splice(index, 1);
        if (state.activeTabId === action.payload) {
          state.activeTabId = state.tabs[index - 1]?.id || state.tabs[0]?.id || null;
        }
      }
    },
    updateTab: (state, action: PayloadAction<Partial<Tab> & { id: string }>) => {
      const index = state.tabs.findIndex(tab => tab.id === action.payload.id);
      if (index !== -1) {
        state.tabs[index] = { ...state.tabs[index], ...action.payload };
      }
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTabId = action.payload;
      state.tabs = state.tabs.map(tab => ({
        ...tab,
        isActive: tab.id === action.payload,
      }));
    },
    addGroup: (state, action: PayloadAction<{ name: string; color: string }>) => {
      state.groups.push({
        id: crypto.randomUUID(),
        ...action.payload,
      });
    },
    removeGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter(group => group.id !== action.payload);
      state.tabs = state.tabs.map(tab =>
        tab.groupId === action.payload ? { ...tab, groupId: undefined } : tab
      );
    },
    updateGroup: (state, action: PayloadAction<{ id: string; name?: string; color?: string }>) => {
      const group = state.groups.find(g => g.id === action.payload.id);
      if (group) {
        Object.assign(group, action.payload);
      }
    },
    addDownload: (state, action: PayloadAction<{ filename: string; url: string }>) => {
      state.downloads.push({
        id: crypto.randomUUID(),
        ...action.payload,
        status: 'pending',
        progress: 0,
      });
    },
    updateDownload: (
      state,
      action: PayloadAction<{
        id: string;
        status?: 'downloading' | 'completed' | 'error';
        progress?: number;
        error?: string;
      }>
    ) => {
      const download = state.downloads.find(d => d.id === action.payload.id);
      if (download) {
        Object.assign(download, action.payload);
      }
    },
    removeDownload: (state, action: PayloadAction<string>) => {
      state.downloads = state.downloads.filter(d => d.id !== action.payload);
    },
    setIncognito: (state, action: PayloadAction<boolean>) => {
      state.isIncognito = action.payload;
    },
    setFullscreen: (state, action: PayloadAction<boolean>) => {
      state.isFullscreen = action.payload;
    },
    setMaximized: (state, action: PayloadAction<boolean>) => {
      state.isMaximized = action.payload;
    },
    setMinimized: (state, action: PayloadAction<boolean>) => {
      state.isMinimized = action.payload;
    },
    setFocused: (state, action: PayloadAction<boolean>) => {
      state.isFocused = action.payload;
    },
    setOnline: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    setUpdating: (state, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
    },
    setLastUpdateCheck: (state, action: PayloadAction<number>) => {
      state.lastUpdateCheck = action.payload;
    },
    setMemoryUsage: (state, action: PayloadAction<BrowserState['memoryUsage']>) => {
      state.memoryUsage = action.payload;
    },
    setCpuUsage: (state, action: PayloadAction<number>) => {
      state.cpuUsage = action.payload;
    },
    setNetworkStatus: (state, action: PayloadAction<BrowserState['networkStatus']>) => {
      state.networkStatus = action.payload;
    },
    setPermissions: (state, action: PayloadAction<Partial<BrowserState['permissions']>>) => {
      state.permissions = { ...state.permissions, ...action.payload };
    },
    setSecurity: (state, action: PayloadAction<Partial<BrowserState['security']>>) => {
      state.security = { ...state.security, ...action.payload };
    },
    setPerformance: (state, action: PayloadAction<Partial<BrowserState['performance']>>) => {
      state.performance = { ...state.performance, ...action.payload };
    },
    setUrl: (state, action: PayloadAction<string>) => {
      state.url = action.payload;
    },
    setTitle: (state, action: PayloadAction<string>) => {
      state.title = action.payload;
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    addToHistory: (state, action: PayloadAction<string>) => {
      state.history.push(action.payload);
    },
    addBookmark: (state, action: PayloadAction<string>) => {
      state.bookmarks.push(action.payload);
    },
    removeBookmark: (state, action: PayloadAction<string>) => {
      state.bookmarks = state.bookmarks.filter(url => url !== action.payload);
    },
    updateSettings: (state, action: PayloadAction<Partial<BrowserState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    setCurrentUrl: (state, action: PayloadAction<string>) => {
      state.currentUrl = action.payload;
    },
    setNavigationState: (
      state,
      action: PayloadAction<{ canGoBack: boolean; canGoForward: boolean }>
    ) => {
      state.canGoBack = action.payload.canGoBack;
      state.canGoForward = action.payload.canGoForward;
    },
    setZoomLevel: (state, action: PayloadAction<number>) => {
      state.zoomLevel = action.payload;
    },
  },
});

// Selectors
export const selectBrowserState = (state: RootState) => state.browser;
export const selectIsIncognito = (state: RootState) => state.browser.isIncognito;
export const selectIsFullscreen = (state: RootState) => state.browser.isFullscreen;
export const selectIsMaximized = (state: RootState) => state.browser.isMaximized;
export const selectIsMinimized = (state: RootState) => state.browser.isMinimized;
export const selectIsFocused = (state: RootState) => state.browser.isFocused;
export const selectIsOnline = (state: RootState) => state.browser.isOnline;
export const selectIsUpdating = (state: RootState) => state.browser.isUpdating;
export const selectLastUpdateCheck = (state: RootState) => state.browser.lastUpdateCheck;
export const selectVersion = (state: RootState) => state.browser.version;
export const selectBuildNumber = (state: RootState) => state.browser.buildNumber;
export const selectPlatform = (state: RootState) => state.browser.platform;
export const selectArchitecture = (state: RootState) => state.browser.architecture;
export const selectMemoryUsage = (state: RootState) => state.browser.memoryUsage;
export const selectCpuUsage = (state: RootState) => state.browser.cpuUsage;
export const selectNetworkStatus = (state: RootState) => state.browser.networkStatus;
export const selectPermissions = (state: RootState) => state.browser.permissions;
export const selectSecurity = (state: RootState) => state.browser.security;
export const selectPerformance = (state: RootState) => state.browser.performance;

// Actions
export const {
  addTab,
  removeTab,
  updateTab,
  setActiveTab,
  addGroup,
  removeGroup,
  updateGroup,
  addDownload,
  updateDownload,
  removeDownload,
  setIncognito,
  setFullscreen,
  setMaximized,
  setMinimized,
  setFocused,
  setOnline,
  setUpdating,
  setLastUpdateCheck,
  setMemoryUsage,
  setCpuUsage,
  setNetworkStatus,
  setPermissions,
  setSecurity,
  setPerformance,
  setUrl,
  setTitle,
  setIsLoading,
  addToHistory,
  addBookmark,
  removeBookmark,
  updateSettings,
  setCurrentUrl,
  setNavigationState,
  setZoomLevel,
} = browserSlice.actions;

export default browserSlice.reducer;
