import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export type UserRole =
  | 'user'
  | 'moderator'
  | 'admin'
  | 'developer'
  | 'tester'
  | 'designer'
  | 'legal';

export interface User {
  id: string;
  email: string;
  username: string;
  roles: UserRole[];
  permissions: string[];
  isActive: boolean;
  lastLogin?: number;
  createdAt: number;
  settings: UserSettings;
  profile: UserProfile;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    browser: boolean;
    desktop: boolean;
  };
  privacy: {
    historyEnabled: boolean;
    cookiesEnabled: boolean;
    trackingEnabled: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    passwordLastChanged: number;
    loginAttempts: number;
  };
}

export interface UserProfile {
  displayName: string;
  avatar?: string;
  bio?: string;
  location?: string;
  timezone?: string;
  preferences: {
    defaultSearchEngine: string;
    defaultNewTabPage: string;
    defaultDownloadLocation: string;
  };
}

interface AuthState {
  currentUser: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  session: {
    token: string | null;
    expiresAt: number | null;
  };
}

const initialState: AuthState = {
  currentUser: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  session: {
    token: null,
    expiresAt: null,
  },
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.isAuthenticated = true;
    },
    clearUser: state => {
      state.currentUser = null;
      state.isAuthenticated = false;
      state.session = {
        token: null,
        expiresAt: null,
      };
    },
    setSession: (state, action: PayloadAction<{ token: string; expiresAt: number }>) => {
      state.session = action.payload;
    },
    updateUserSettings: (state, action: PayloadAction<Partial<UserSettings>>) => {
      if (state.currentUser) {
        state.currentUser.settings = {
          ...state.currentUser.settings,
          ...action.payload,
        };
      }
    },
    updateUserProfile: (state, action: PayloadAction<Partial<UserProfile>>) => {
      if (state.currentUser) {
        state.currentUser.profile = {
          ...state.currentUser.profile,
          ...action.payload,
        };
      }
    },
    addUserRole: (state, action: PayloadAction<UserRole>) => {
      if (state.currentUser && !state.currentUser.roles.includes(action.payload)) {
        state.currentUser.roles.push(action.payload);
      }
    },
    removeUserRole: (state, action: PayloadAction<UserRole>) => {
      if (state.currentUser) {
        state.currentUser.roles = state.currentUser.roles.filter(role => role !== action.payload);
      }
    },
    addUserPermission: (state, action: PayloadAction<string>) => {
      if (state.currentUser && !state.currentUser.permissions.includes(action.payload)) {
        state.currentUser.permissions.push(action.payload);
      }
    },
    removeUserPermission: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.permissions = state.currentUser.permissions.filter(
          permission => permission !== action.payload
        );
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    updateLastLogin: state => {
      if (state.currentUser) {
        state.currentUser.lastLogin = Date.now();
      }
    },
  },
});

export const {
  setUser,
  clearUser,
  setSession,
  updateUserSettings,
  updateUserProfile,
  addUserRole,
  removeUserRole,
  addUserPermission,
  removeUserPermission,
  setLoading,
  setError,
  updateLastLogin,
} = authSlice.actions;

export default authSlice.reducer;
