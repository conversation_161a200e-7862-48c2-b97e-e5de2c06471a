import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { notificationManager } from '../../notifications/NotificationManager';
import type {
  Notification,
  NotificationAccessibility,
  NotificationAnimation,
  NotificationBehavior,
  NotificationLayout,
  NotificationPosition,
  NotificationPriority,
  NotificationSound,
  NotificationTheme,
  NotificationType,
} from '../../types/notifications';

// Simplified types for Redux state
type ReduxNotification = Omit<Notification, 'animation'> & {
  animation?: Omit<NotificationAnimation, 'transition'>;
};

type ReduxNotificationTheme = Omit<NotificationTheme, 'animation'> & {
  animation?: Omit<NotificationAnimation, 'transition'>;
};

interface NotificationsState {
  items: ReduxNotification[];
  settings: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    email: boolean;
    categories: {
      system: boolean;
      security: boolean;
      update: boolean;
      download: boolean;
      sync: boolean;
      custom: boolean;
    };
    maxItems: number;
    autoClear: boolean;
    autoClearDelay: number;
    defaultPosition: NotificationPosition;
    defaultType: NotificationType;
    defaultPriority: NotificationPriority;
    defaultTheme: ReduxNotificationTheme;
    defaultAnimation: Omit<NotificationAnimation, 'transition'>;
    defaultSound: NotificationSound;
    defaultBehavior: NotificationBehavior;
    defaultLayout: NotificationLayout;
    defaultAccessibility: NotificationAccessibility;
  };
  unreadCount: number;
}

const initialState: NotificationsState = {
  items: [],
  settings: {
    enabled: true,
    sound: true,
    desktop: true,
    email: false,
    categories: {
      system: true,
      security: true,
      update: true,
      download: true,
      sync: true,
      custom: true,
    },
    maxItems: 100,
    autoClear: true,
    autoClearDelay: 5000,
    defaultPosition: 'top-right',
    defaultType: 'info',
    defaultPriority: 'normal',
    defaultTheme: {},
    defaultAnimation: {},
    defaultSound: {},
    defaultBehavior: 'stack',
    defaultLayout: 'stack',
    defaultAccessibility: 'default',
  },
  unreadCount: 0,
};

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Omit<ReduxNotification, 'id' | 'read'>>) => {
      const notification = {
        ...action.payload,
        id: crypto.randomUUID(),
        read: false,
      };
      state.items.push(notification);
      state.unreadCount += 1;
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      const index = state.items.findIndex(item => item.id === action.payload);
      if (index !== -1) {
        if (!state.items[index].read) {
          state.unreadCount -= 1;
        }
        state.items.splice(index, 1);
      }
    },
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.items.find(item => item.id === action.payload);
      if (notification && !notification.read) {
        notification.read = true;
        state.unreadCount -= 1;
      }
    },
    clearNotifications: state => {
      state.items = [];
      state.unreadCount = 0;
    },
    updateSettings: (state, action: PayloadAction<Partial<NotificationsState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
  },
});

export const {
  addNotification,
  removeNotification,
  markAsRead,
  clearNotifications,
  updateSettings,
} = notificationsSlice.actions;

export default notificationsSlice.reducer;
