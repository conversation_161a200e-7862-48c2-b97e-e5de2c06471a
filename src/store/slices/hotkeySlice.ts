import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { Hotkey, HotkeyState } from '../../renderer/hotkeys/types';

const initialState: HotkeyState = {
  hotkeys: [],
  enabledHotkeys: [],
  loading: false,
  error: null,
};

const hotkeySlice = createSlice({
  name: 'hotkeys',
  initialState,
  reducers: {
    setHotkeys: (state, action: PayloadAction<Hotkey[]>) => {
      state.hotkeys = action.payload;
    },
    addHotkey: (state, action: PayloadAction<Hotkey>) => {
      state.hotkeys.push(action.payload);
    },
    removeHotkey: (state, action: PayloadAction<string>) => {
      state.hotkeys = state.hotkeys.filter((hotkey: Hotkey) => hotkey.id !== action.payload);
      state.enabledHotkeys = state.enabledHotkeys.filter((id: string) => id !== action.payload);
    },
    updateHotkey: (state, action: PayloadAction<Hotkey>) => {
      const index = state.hotkeys.findIndex((hotkey: Hotkey) => hotkey.id === action.payload.id);
      if (index !== -1) {
        state.hotkeys[index] = action.payload;
      }
    },
    enableHotkey: (state, action: PayloadAction<string>) => {
      if (!state.enabledHotkeys.includes(action.payload)) {
        state.enabledHotkeys.push(action.payload);
      }
    },
    disableHotkey: (state, action: PayloadAction<string>) => {
      state.enabledHotkeys = state.enabledHotkeys.filter((id: string) => id !== action.payload);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setHotkeys,
  addHotkey,
  removeHotkey,
  updateHotkey,
  enableHotkey,
  disableHotkey,
  setLoading,
  setError,
} = hotkeySlice.actions;

export default hotkeySlice.reducer;
