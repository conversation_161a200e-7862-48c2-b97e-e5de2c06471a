import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export type DownloadStatus = 'pending' | 'downloading' | 'paused' | 'completed' | 'error';

export interface Download {
  id: string;
  filename: string;
  url: string;
  status: DownloadStatus;
  progress: number;
  totalBytes: number;
  receivedBytes: number;
  startTime: number;
  endTime?: number;
  error?: string;
  savePath: string;
}

interface DownloadState {
  downloads: Download[];
  activeDownloads: string[];
}

const initialState: DownloadState = {
  downloads: [],
  activeDownloads: [],
};

const downloadSlice = createSlice({
  name: 'downloads',
  initialState,
  reducers: {
    addDownload: (
      state,
      action: PayloadAction<
        Omit<Download, 'id' | 'status' | 'progress' | 'startTime' | 'receivedBytes'>
      >
    ) => {
      const newDownload: Download = {
        ...action.payload,
        id: Date.now().toString(),
        status: 'pending',
        progress: 0,
        startTime: Date.now(),
        receivedBytes: 0,
      };
      state.downloads.push(newDownload);
    },
    startDownload: (state, action: PayloadAction<string>) => {
      const download = state.downloads.find((d: Download) => d.id === action.payload);
      if (download) {
        download.status = 'downloading';
        if (!state.activeDownloads.includes(action.payload)) {
          state.activeDownloads.push(action.payload);
        }
      }
    },
    pauseDownload: (state, action: PayloadAction<string>) => {
      const download = state.downloads.find((d: Download) => d.id === action.payload);
      if (download) {
        download.status = 'paused';
        state.activeDownloads = state.activeDownloads.filter((id: string) => id !== action.payload);
      }
    },
    updateDownloadProgress: (
      state,
      action: PayloadAction<{ id: string; receivedBytes: number; totalBytes: number }>
    ) => {
      const { id, receivedBytes, totalBytes } = action.payload;
      const download = state.downloads.find((d: Download) => d.id === id);
      if (download) {
        download.receivedBytes = receivedBytes;
        download.totalBytes = totalBytes;
        download.progress = (receivedBytes / totalBytes) * 100;
      }
    },
    completeDownload: (state, action: PayloadAction<string>) => {
      const download = state.downloads.find((d: Download) => d.id === action.payload);
      if (download) {
        download.status = 'completed';
        download.progress = 100;
        download.endTime = Date.now();
        state.activeDownloads = state.activeDownloads.filter((id: string) => id !== action.payload);
      }
    },
    failDownload: (state, action: PayloadAction<{ id: string; error: string }>) => {
      const { id, error } = action.payload;
      const download = state.downloads.find((d: Download) => d.id === id);
      if (download) {
        download.status = 'error';
        download.error = error;
        download.endTime = Date.now();
        state.activeDownloads = state.activeDownloads.filter(
          (downloadId: string) => downloadId !== id
        );
      }
    },
    removeDownload: (state, action: PayloadAction<string>) => {
      state.downloads = state.downloads.filter((d: Download) => d.id !== action.payload);
      state.activeDownloads = state.activeDownloads.filter((id: string) => id !== action.payload);
    },
    clearCompletedDownloads: state => {
      state.downloads = state.downloads.filter((d: Download) => d.status !== 'completed');
    },
  },
});

export const {
  addDownload,
  startDownload,
  pauseDownload,
  updateDownloadProgress,
  completeDownload,
  failDownload,
  removeDownload,
  clearCompletedDownloads,
} = downloadSlice.actions;

export default downloadSlice.reducer;
