/**
 * Advanced Performance slice для детального мониторинга производительности
 */

import { PayloadAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';

export interface MemoryMetrics {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  usedPercent: number;
  timestamp: number;
}

export interface CPUMetrics {
  usage: number;
  loadAverage: number[];
  cores: number;
  timestamp: number;
}

export interface NetworkMetrics {
  downloadSpeed: number;
  uploadSpeed: number;
  latency: number;
  packetLoss: number;
  connectionType: string;
  timestamp: number;
}

export interface RenderingMetrics {
  fps: number;
  frameTime: number;
  droppedFrames: number;
  renderTime: number;
  paintTime: number;
  layoutTime: number;
  timestamp: number;
}

export interface ResourceMetrics {
  name: string;
  type: 'script' | 'stylesheet' | 'image' | 'font' | 'document' | 'other';
  size: number;
  loadTime: number;
  cacheHit: boolean;
  timestamp: number;
}

export interface PerformanceAlert {
  id: string;
  type: 'memory' | 'cpu' | 'network' | 'rendering' | 'resource';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
  acknowledged: boolean;
}

export interface PerformanceThresholds {
  memory: {
    warning: number;
    critical: number;
  };
  cpu: {
    warning: number;
    critical: number;
  };
  fps: {
    warning: number;
    critical: number;
  };
  loadTime: {
    warning: number;
    critical: number;
  };
}

export interface AdvancedPerformanceState {
  memory: MemoryMetrics[];
  cpu: CPUMetrics[];
  network: NetworkMetrics[];
  rendering: RenderingMetrics[];
  resources: ResourceMetrics[];
  alerts: PerformanceAlert[];
  thresholds: PerformanceThresholds;
  isMonitoring: boolean;
  monitoringInterval: number;
  retentionPeriod: number; // in milliseconds
  isLoading: boolean;
  error: string | null;
  config: {
    enableMemoryMonitoring: boolean;
    enableCPUMonitoring: boolean;
    enableNetworkMonitoring: boolean;
    enableRenderingMonitoring: boolean;
    enableResourceMonitoring: boolean;
    enableAlerts: boolean;
    autoOptimize: boolean;
    samplingRate: number;
  };
}

const initialState: AdvancedPerformanceState = {
  memory: [],
  cpu: [],
  network: [],
  rendering: [],
  resources: [],
  alerts: [],
  thresholds: {
    memory: {
      warning: 70, // 70% of heap limit
      critical: 90, // 90% of heap limit
    },
    cpu: {
      warning: 70, // 70% CPU usage
      critical: 90, // 90% CPU usage
    },
    fps: {
      warning: 30, // Below 30 FPS
      critical: 15, // Below 15 FPS
    },
    loadTime: {
      warning: 3000, // 3 seconds
      critical: 5000, // 5 seconds
    },
  },
  isMonitoring: false,
  monitoringInterval: 1000, // 1 second
  retentionPeriod: 5 * 60 * 1000, // 5 minutes
  isLoading: false,
  error: null,
  config: {
    enableMemoryMonitoring: true,
    enableCPUMonitoring: true,
    enableNetworkMonitoring: true,
    enableRenderingMonitoring: true,
    enableResourceMonitoring: true,
    enableAlerts: true,
    autoOptimize: false,
    samplingRate: 1.0,
  },
};

// Async thunks
export const collectMemoryMetrics = createAsyncThunk(
  'advancedPerformance/collectMemoryMetrics',
  async () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usedPercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        timestamp: Date.now(),
      };
    }
    throw new Error('Memory API not available');
  }
);

export const collectNetworkMetrics = createAsyncThunk(
  'advancedPerformance/collectNetworkMetrics',
  async () => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return {
        downloadSpeed: connection.downlink || 0,
        uploadSpeed: connection.uplink || 0,
        latency: connection.rtt || 0,
        packetLoss: 0, // Not available in browser
        connectionType: connection.effectiveType || 'unknown',
        timestamp: Date.now(),
      };
    }
    throw new Error('Network Information API not available');
  }
);

export const collectRenderingMetrics = createAsyncThunk(
  'advancedPerformance/collectRenderingMetrics',
  async () => {
    return new Promise<RenderingMetrics>(resolve => {
      let frameCount = 0;
      let lastTime = performance.now();
      let fps = 0;

      const measureFrame = (currentTime: number) => {
        frameCount++;
        const deltaTime = currentTime - lastTime;

        if (deltaTime >= 1000) {
          fps = Math.round((frameCount * 1000) / deltaTime);
          frameCount = 0;
          lastTime = currentTime;

          resolve({
            fps,
            frameTime: deltaTime / frameCount || 0,
            droppedFrames: Math.max(0, 60 - fps), // Assuming 60 FPS target
            renderTime: 0, // Would need more complex measurement
            paintTime: 0, // Would need more complex measurement
            layoutTime: 0, // Would need more complex measurement
            timestamp: Date.now(),
          });
        } else {
          requestAnimationFrame(measureFrame);
        }
      };

      requestAnimationFrame(measureFrame);
    });
  }
);

export const optimizePerformance = createAsyncThunk(
  'advancedPerformance/optimize',
  async (_, { getState }) => {
    const state = getState() as { advancedPerformance: AdvancedPerformanceState };
    const { memory, alerts } = state.advancedPerformance;

    const optimizations: string[] = [];

    // Memory optimization
    const latestMemory = memory[memory.length - 1];
    if (latestMemory && latestMemory.usedPercent > 80) {
      // Trigger garbage collection if available
      if ('gc' in window) {
        (window as any).gc();
        optimizations.push('Triggered garbage collection');
      }
    }

    // Clear old data
    optimizations.push('Cleared old performance data');

    // Check for critical alerts
    const criticalAlerts = alerts.filter(
      alert => alert.severity === 'critical' && !alert.acknowledged
    );

    if (criticalAlerts.length > 0) {
      optimizations.push(`Addressed ${criticalAlerts.length} critical alerts`);
    }

    return optimizations;
  }
);

const advancedPerformanceSlice = createSlice({
  name: 'advancedPerformance',
  initialState,
  reducers: {
    addMemoryMetrics: (state, action: PayloadAction<MemoryMetrics>) => {
      state.memory.push(action.payload);

      // Check thresholds
      if (action.payload.usedPercent > state.thresholds.memory.critical) {
        state.alerts.push({
          id: `memory-${Date.now()}`,
          type: 'memory',
          severity: 'critical',
          message: `Memory usage critical: ${action.payload.usedPercent.toFixed(1)}%`,
          value: action.payload.usedPercent,
          threshold: state.thresholds.memory.critical,
          timestamp: Date.now(),
          acknowledged: false,
        });
      } else if (action.payload.usedPercent > state.thresholds.memory.warning) {
        state.alerts.push({
          id: `memory-${Date.now()}`,
          type: 'memory',
          severity: 'high',
          message: `Memory usage high: ${action.payload.usedPercent.toFixed(1)}%`,
          value: action.payload.usedPercent,
          threshold: state.thresholds.memory.warning,
          timestamp: Date.now(),
          acknowledged: false,
        });
      }

      // Cleanup old data
      const cutoff = Date.now() - state.retentionPeriod;
      state.memory = state.memory.filter(metric => metric.timestamp > cutoff);
    },

    addCPUMetrics: (state, action: PayloadAction<CPUMetrics>) => {
      state.cpu.push(action.payload);

      // Check thresholds
      if (action.payload.usage > state.thresholds.cpu.critical) {
        state.alerts.push({
          id: `cpu-${Date.now()}`,
          type: 'cpu',
          severity: 'critical',
          message: `CPU usage critical: ${action.payload.usage.toFixed(1)}%`,
          value: action.payload.usage,
          threshold: state.thresholds.cpu.critical,
          timestamp: Date.now(),
          acknowledged: false,
        });
      }

      // Cleanup old data
      const cutoff = Date.now() - state.retentionPeriod;
      state.cpu = state.cpu.filter(metric => metric.timestamp > cutoff);
    },

    addNetworkMetrics: (state, action: PayloadAction<NetworkMetrics>) => {
      state.network.push(action.payload);

      // Cleanup old data
      const cutoff = Date.now() - state.retentionPeriod;
      state.network = state.network.filter(metric => metric.timestamp > cutoff);
    },

    addRenderingMetrics: (state, action: PayloadAction<RenderingMetrics>) => {
      state.rendering.push(action.payload);

      // Check FPS thresholds
      if (action.payload.fps < state.thresholds.fps.critical) {
        state.alerts.push({
          id: `fps-${Date.now()}`,
          type: 'rendering',
          severity: 'critical',
          message: `FPS critical: ${action.payload.fps} FPS`,
          value: action.payload.fps,
          threshold: state.thresholds.fps.critical,
          timestamp: Date.now(),
          acknowledged: false,
        });
      }

      // Cleanup old data
      const cutoff = Date.now() - state.retentionPeriod;
      state.rendering = state.rendering.filter(metric => metric.timestamp > cutoff);
    },

    addResourceMetrics: (state, action: PayloadAction<ResourceMetrics>) => {
      state.resources.push(action.payload);

      // Check load time thresholds
      if (action.payload.loadTime > state.thresholds.loadTime.critical) {
        state.alerts.push({
          id: `resource-${Date.now()}`,
          type: 'resource',
          severity: 'critical',
          message: `Slow resource load: ${action.payload.name} (${action.payload.loadTime}ms)`,
          value: action.payload.loadTime,
          threshold: state.thresholds.loadTime.critical,
          timestamp: Date.now(),
          acknowledged: false,
        });
      }

      // Cleanup old data
      const cutoff = Date.now() - state.retentionPeriod;
      state.resources = state.resources.filter(metric => metric.timestamp > cutoff);
    },

    acknowledgeAlert: (state, action: PayloadAction<string>) => {
      const alert = state.alerts.find(a => a.id === action.payload);
      if (alert) {
        alert.acknowledged = true;
      }
    },

    clearAlert: (state, action: PayloadAction<string>) => {
      state.alerts = state.alerts.filter(alert => alert.id !== action.payload);
    },

    clearAllAlerts: state => {
      state.alerts = [];
    },

    updateThresholds: (state, action: PayloadAction<Partial<PerformanceThresholds>>) => {
      state.thresholds = { ...state.thresholds, ...action.payload };
    },

    setMonitoring: (state, action: PayloadAction<boolean>) => {
      state.isMonitoring = action.payload;
    },

    setMonitoringInterval: (state, action: PayloadAction<number>) => {
      state.monitoringInterval = action.payload;
    },

    setRetentionPeriod: (state, action: PayloadAction<number>) => {
      state.retentionPeriod = action.payload;
    },

    updateConfig: (state, action: PayloadAction<Partial<AdvancedPerformanceState['config']>>) => {
      state.config = { ...state.config, ...action.payload };
    },

    clearAllMetrics: state => {
      state.memory = [];
      state.cpu = [];
      state.network = [];
      state.rendering = [];
      state.resources = [];
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(collectMemoryMetrics.fulfilled, (state, action) => {
        state.memory.push(action.payload);
      })
      .addCase(collectNetworkMetrics.fulfilled, (state, action) => {
        state.network.push(action.payload);
      })
      .addCase(collectRenderingMetrics.fulfilled, (state, action) => {
        state.rendering.push(action.payload);
      })
      .addCase(optimizePerformance.pending, state => {
        state.isLoading = true;
      })
      .addCase(optimizePerformance.fulfilled, state => {
        state.isLoading = false;
        // Clear old data as part of optimization
        const cutoff = Date.now() - state.retentionPeriod;
        state.memory = state.memory.filter(metric => metric.timestamp > cutoff);
        state.cpu = state.cpu.filter(metric => metric.timestamp > cutoff);
        state.network = state.network.filter(metric => metric.timestamp > cutoff);
        state.rendering = state.rendering.filter(metric => metric.timestamp > cutoff);
        state.resources = state.resources.filter(metric => metric.timestamp > cutoff);
      })
      .addCase(optimizePerformance.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Optimization failed';
      });
  },
});

export const {
  addMemoryMetrics,
  addCPUMetrics,
  addNetworkMetrics,
  addRenderingMetrics,
  addResourceMetrics,
  acknowledgeAlert,
  clearAlert,
  clearAllAlerts,
  updateThresholds,
  setMonitoring,
  setMonitoringInterval,
  setRetentionPeriod,
  updateConfig,
  clearAllMetrics,
  setError,
} = advancedPerformanceSlice.actions;

export default advancedPerformanceSlice;
