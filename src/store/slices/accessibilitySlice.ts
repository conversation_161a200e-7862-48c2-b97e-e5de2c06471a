import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface AccessibilityState {
  highContrast: boolean;
  fontSize: number;
  fontFamily: string;
  lineSpacing: number;
  letterSpacing: number;
  wordSpacing: number;
  textAlign: 'left' | 'center' | 'right' | 'justify';
  colorScheme: 'light' | 'dark' | 'system';
  animations: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  focusHighlight: boolean;
  textToSpeech: {
    enabled: boolean;
    rate: number;
    pitch: number;
    voice: string;
  };
  shortcuts: {
    enabled: boolean;
    custom: Record<string, string>;
  };
  zoom: {
    level: number;
    textOnly: boolean;
  };
  cursor: {
    size: number;
    color: string;
    highlight: boolean;
  };
  notifications: {
    sound: boolean;
    vibration: boolean;
    visual: boolean;
  };
}

const initialState: AccessibilityState = {
  highContrast: false,
  fontSize: 16,
  fontFamily: 'system-ui',
  lineSpacing: 1.5,
  letterSpacing: 0,
  wordSpacing: 0,
  textAlign: 'left',
  colorScheme: 'system',
  animations: true,
  reducedMotion: false,
  screenReader: false,
  keyboardNavigation: true,
  focusHighlight: true,
  textToSpeech: {
    enabled: false,
    rate: 1,
    pitch: 1,
    voice: 'default',
  },
  shortcuts: {
    enabled: true,
    custom: {},
  },
  zoom: {
    level: 100,
    textOnly: false,
  },
  cursor: {
    size: 1,
    color: '#000000',
    highlight: false,
  },
  notifications: {
    sound: true,
    vibration: true,
    visual: true,
  },
};

const accessibilitySlice = createSlice({
  name: 'accessibility',
  initialState,
  reducers: {
    toggleHighContrast: state => {
      state.highContrast = !state.highContrast;
    },
    setFontSize: (state, action: PayloadAction<number>) => {
      state.fontSize = action.payload;
    },
    setFontFamily: (state, action: PayloadAction<string>) => {
      state.fontFamily = action.payload;
    },
    setLineSpacing: (state, action: PayloadAction<number>) => {
      state.lineSpacing = action.payload;
    },
    setLetterSpacing: (state, action: PayloadAction<number>) => {
      state.letterSpacing = action.payload;
    },
    setWordSpacing: (state, action: PayloadAction<number>) => {
      state.wordSpacing = action.payload;
    },
    setTextAlign: (state, action: PayloadAction<AccessibilityState['textAlign']>) => {
      state.textAlign = action.payload;
    },
    setColorScheme: (state, action: PayloadAction<AccessibilityState['colorScheme']>) => {
      state.colorScheme = action.payload;
    },
    toggleAnimations: state => {
      state.animations = !state.animations;
    },
    toggleReducedMotion: state => {
      state.reducedMotion = !state.reducedMotion;
    },
    toggleScreenReader: state => {
      state.screenReader = !state.screenReader;
    },
    toggleKeyboardNavigation: state => {
      state.keyboardNavigation = !state.keyboardNavigation;
    },
    toggleFocusHighlight: state => {
      state.focusHighlight = !state.focusHighlight;
    },
    updateTextToSpeech: (
      state,
      action: PayloadAction<Partial<AccessibilityState['textToSpeech']>>
    ) => {
      state.textToSpeech = { ...state.textToSpeech, ...action.payload };
    },
    updateShortcuts: (state, action: PayloadAction<Partial<AccessibilityState['shortcuts']>>) => {
      state.shortcuts = { ...state.shortcuts, ...action.payload };
    },
    updateZoom: (state, action: PayloadAction<Partial<AccessibilityState['zoom']>>) => {
      state.zoom = { ...state.zoom, ...action.payload };
    },
    updateCursor: (state, action: PayloadAction<Partial<AccessibilityState['cursor']>>) => {
      state.cursor = { ...state.cursor, ...action.payload };
    },
    updateNotifications: (
      state,
      action: PayloadAction<Partial<AccessibilityState['notifications']>>
    ) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
  },
});

export const {
  toggleHighContrast,
  setFontSize,
  setFontFamily,
  setLineSpacing,
  setLetterSpacing,
  setWordSpacing,
  setTextAlign,
  setColorScheme,
  toggleAnimations,
  toggleReducedMotion,
  toggleScreenReader,
  toggleKeyboardNavigation,
  toggleFocusHighlight,
  updateTextToSpeech,
  updateShortcuts,
  updateZoom,
  updateCursor,
  updateNotifications,
} = accessibilitySlice.actions;

export default accessibilitySlice.reducer;
