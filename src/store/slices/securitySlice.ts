import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { securityManager } from '../../security/SecurityManager';
import { RootState } from '../index';

export interface SecurityState {
  enabled: boolean;
  config: {
    encryption: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  status: {
    isInitialized: boolean;
    isConfigured: boolean;
    lastCheck: number | null;
    lastUpdate: number | null;
    errors: {
      count: number;
      lastError: string | null;
    };
  };
  alerts: {
    enabled: boolean;
    channels: {
      type: 'email' | 'slack' | 'custom';
      config: Record<string, unknown>;
    }[];
  };
}

const initialState: SecurityState = {
  enabled: true,
  config: {
    encryption: {
      enabled: true,
      algorithm: 'aes-256-gcm',
    },
    backup: {
      enabled: true,
      schedule: '0 0 * * *',
      retention: 30,
    },
    versioning: {
      enabled: true,
      maxVersions: 5,
      strategy: 'major',
    },
  },
  status: {
    isInitialized: false,
    isConfigured: false,
    lastCheck: null,
    lastUpdate: null,
    errors: {
      count: 0,
      lastError: null,
    },
  },
  alerts: {
    enabled: true,
    channels: [],
  },
};

const securitySlice = createSlice({
  name: 'security',
  initialState,
  reducers: {
    setEnabled: (state, action: PayloadAction<boolean>) => {
      state.enabled = action.payload;
    },
    updateConfig: (state, action: PayloadAction<Partial<SecurityState['config']>>) => {
      state.config = {
        ...state.config,
        ...action.payload,
      };
    },
    setStatus: (state, action: PayloadAction<Partial<SecurityState['status']>>) => {
      state.status = {
        ...state.status,
        ...action.payload,
      };
    },
    addAlertChannel: (state, action: PayloadAction<SecurityState['alerts']['channels'][0]>) => {
      state.alerts.channels.push(action.payload);
    },
    removeAlertChannel: (state, action: PayloadAction<string>) => {
      state.alerts.channels = state.alerts.channels.filter(
        channel => channel.type !== action.payload
      );
    },
    setAlertsEnabled: (state, action: PayloadAction<boolean>) => {
      state.alerts.enabled = action.payload;
    },
    recordError: (state, action: PayloadAction<string>) => {
      state.status.errors.count += 1;
      state.status.errors.lastError = action.payload;
    },
    clearErrors: state => {
      state.status.errors.count = 0;
      state.status.errors.lastError = null;
    },
  },
});

export const {
  setEnabled,
  updateConfig,
  setStatus,
  addAlertChannel,
  removeAlertChannel,
  setAlertsEnabled,
  recordError,
  clearErrors,
} = securitySlice.actions;

export const initializeSecurity = () => async (dispatch: any) => {
  try {
    await securityManager.initialize();
    dispatch(
      setStatus({
        isInitialized: true,
        isConfigured: true,
        lastCheck: Date.now(),
        lastUpdate: Date.now(),
      })
    );
  } catch (error) {
    dispatch(
      recordError(error instanceof Error ? error.message : 'Failed to initialize security settings')
    );
    throw error;
  }
};

export const validateSecurity = () => async (dispatch: any) => {
  try {
    const isValid = await securityManager.validate();
    if (!isValid) {
      throw new Error('Security configuration validation failed');
    }
    dispatch(
      setStatus({
        lastCheck: Date.now(),
      })
    );
  } catch (error) {
    dispatch(recordError(error instanceof Error ? error.message : 'Security validation failed'));
    throw error;
  }
};

export const selectSecurity = (state: RootState) => state.security;
export const selectSecurityConfig = (state: RootState) => state.security.config;
export const selectSecurityStatus = (state: RootState) => state.security.status;
export const selectSecurityAlerts = (state: RootState) => state.security.alerts;

export default securitySlice.reducer;
