import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { RootState } from '../index';

export interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isLoading: boolean;
  isActive: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isIncognito: boolean;
  createdAt: number;
  lastAccessed: number;
}

export interface TabGroup {
  id: string;
  name: string;
  color: string;
  tabIds: string[];
}

interface TabsState {
  tabs: Tab[];
  activeTabId: string | null;
  groups: TabGroup[];
  lastClosedTabs: Tab[];
  maxClosedTabs: number;
}

const initialState: TabsState = {
  tabs: [],
  activeTabId: null,
  groups: [],
  lastClosedTabs: [],
  maxClosedTabs: 20,
};

const tabsSlice = createSlice({
  name: 'tabs',
  initialState,
  reducers: {
    addTab: (state, action: PayloadAction<Omit<Tab, 'id' | 'createdAt' | 'lastAccessed'>>) => {
      const newTab: Tab = {
        ...action.payload,
        id: crypto.randomUUID(),
        createdAt: Date.now(),
        lastAccessed: Date.now(),
      };
      state.tabs.push(newTab);
      state.activeTabId = newTab.id;
    },
    removeTab: (state, action: PayloadAction<string>) => {
      const index = state.tabs.findIndex(tab => tab.id === action.payload);
      if (index !== -1) {
        state.tabs.splice(index, 1);
        if (state.activeTabId === action.payload) {
          state.activeTabId = state.tabs[index - 1]?.id || state.tabs[0]?.id || null;
        }
      }
    },
    updateTab: (state, action: PayloadAction<{ id: string; updates: Partial<Tab> }>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload.id);
      if (tab) {
        Object.assign(tab, action.payload.updates);
        tab.lastAccessed = Date.now();
      }
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload);
      if (tab) {
        state.activeTabId = action.payload;
        tab.lastAccessed = Date.now();
      }
    },
    moveTab: (state, action: PayloadAction<{ id: string; newIndex: number }>) => {
      const { id, newIndex } = action.payload;
      const oldIndex = state.tabs.findIndex(tab => tab.id === id);
      if (oldIndex !== -1 && newIndex >= 0 && newIndex < state.tabs.length) {
        const [tab] = state.tabs.splice(oldIndex, 1);
        state.tabs.splice(newIndex, 0, tab);
      }
    },
    pinTab: (state, action: PayloadAction<string>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload);
      if (tab) {
        tab.isPinned = !tab.isPinned;
      }
    },
    muteTab: (state, action: PayloadAction<string>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload);
      if (tab) {
        tab.isMuted = !tab.isMuted;
      }
    },
    closeTab: (state, action: PayloadAction<string>) => {
      const idx = state.tabs.findIndex(tab => tab.id === action.payload);
      if (idx !== -1) {
        const [closed] = state.tabs.splice(idx, 1);
        state.lastClosedTabs.unshift(closed);
        if (state.lastClosedTabs.length > state.maxClosedTabs) {
          state.lastClosedTabs.pop();
        }
        if (closed.isActive && state.tabs.length > 0) {
          const nextTab = state.tabs[Math.max(0, idx - 1)];
          nextTab.isActive = true;
          state.activeTabId = nextTab.id;
        } else if (state.tabs.length === 0) {
          state.activeTabId = null;
        }
      }
    },
    restoreClosedTab: state => {
      const tab = state.lastClosedTabs.shift();
      if (tab) {
        state.tabs.push({ ...tab, isActive: false });
      }
    },
    addTabGroup: (state, action: PayloadAction<Omit<TabGroup, 'tabIds'>>) => {
      const id = action.payload.id || Math.random().toString(36).substr(2, 9);
      state.groups.push({ ...action.payload, id, tabIds: [] });
    },
    removeTabGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter(group => group.id !== action.payload);
      state.tabs.forEach(tab => {
        if (tab.groupId === action.payload) tab.groupId = undefined;
      });
    },
    assignTabToGroup: (state, action: PayloadAction<{ tabId: string; groupId: string }>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload.tabId);
      const group = state.groups.find(group => group.id === action.payload.groupId);
      if (tab && group) {
        tab.groupId = group.id;
        if (!group.tabIds.includes(tab.id)) group.tabIds.push(tab.id);
      }
    },
    unassignTabFromGroup: (state, action: PayloadAction<string>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload);
      if (tab && tab.groupId) {
        const group = state.groups.find(g => g.id === tab.groupId);
        if (group) group.tabIds = group.tabIds.filter(id => id !== tab.id);
        tab.groupId = undefined;
      }
    },
    setTabLoading: (state, action: PayloadAction<{ id: string; isLoading: boolean }>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload.id);
      if (tab) tab.isLoading = action.payload.isLoading;
    },
    addTabHistory: (state, action: PayloadAction<{ id: string; url: string }>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload.id);
      if (tab) {
        tab.history.push(action.payload.url);
        tab.canGoBack = tab.history.length > 1;
        tab.canGoForward = false;
      }
    },
    goBack: (state, action: PayloadAction<string>) => {
      const tab = state.tabs.find(tab => tab.id === action.payload);
      if (tab && tab.history.length > 1) {
        tab.history.pop();
        tab.canGoBack = tab.history.length > 1;
        tab.canGoForward = true;
      }
    },
    goForward: (state, action: PayloadAction<string>) => {
      // Реализация зависит от хранения истории вперед
    },
    clearTabs: state => {
      state.tabs = [];
      state.activeTabId = null;
      state.groups = [];
      state.lastClosedTabs = [];
    },
  },
});

export const selectTabs = (state: RootState) => state.tabs.tabs;
export const selectActiveTab = (state: RootState) =>
  state.tabs.tabs.find(tab => tab.id === state.tabs.activeTabId);
export const selectTabGroups = (state: RootState) => state.tabs.groups;
export const selectLastClosedTabs = (state: RootState) => state.tabs.lastClosedTabs;

export const {
  addTab,
  removeTab,
  updateTab,
  setActiveTab,
  moveTab,
  pinTab,
  muteTab,
  closeTab,
  restoreClosedTab,
  addTabGroup,
  removeTabGroup,
  assignTabToGroup,
  unassignTabFromGroup,
  setTabLoading,
  addTabHistory,
  goBack,
  goForward,
  clearTabs,
} = tabsSlice.actions;

export default tabsSlice.reducer;
