import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface PerformanceMetrics {
  fps: number;
  memory: {
    used: number;
    total: number;
  };
  cpu: {
    usage: number;
    temperature: number;
  };
  network: {
    download: number;
    upload: number;
    latency: number;
  };
  render: {
    timeToFirstByte: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    timeToInteractive: number;
  };
  errors: {
    count: number;
    lastError: string | null;
  };
}

export interface PerformanceSettings {
  monitoring: {
    enabled: boolean;
    interval: number;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  optimization: {
    hardwareAcceleration: boolean;
    backgroundThrottling: boolean;
    processPriority: 'low' | 'normal' | 'high';
    maxMemoryUsage: number;
  };
  caching: {
    enabled: boolean;
    maxSize: number;
    strategy: 'lru' | 'fifo' | 'lfu';
  };
  network: {
    compression: boolean;
    prefetching: boolean;
    maxConcurrentRequests: number;
  };
}

interface PerformanceState {
  metrics: PerformanceMetrics;
  settings: PerformanceSettings;
  history: {
    timestamps: number[];
    metrics: Partial<PerformanceMetrics>[];
  };
  alerts: Array<{
    id: string;
    type: 'warning' | 'error' | 'critical';
    message: string;
    timestamp: number;
  }>;
  lastUpdate: number | null;
}

const initialState: PerformanceState = {
  metrics: {
    fps: 60,
    memory: {
      used: 0,
      total: 0,
    },
    cpu: {
      usage: 0,
      temperature: 0,
    },
    network: {
      download: 0,
      upload: 0,
      latency: 0,
    },
    render: {
      timeToFirstByte: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      timeToInteractive: 0,
    },
    errors: {
      count: 0,
      lastError: null,
    },
  },
  settings: {
    monitoring: {
      enabled: true,
      interval: 1000,
      logLevel: 'info',
    },
    optimization: {
      hardwareAcceleration: true,
      backgroundThrottling: false,
      processPriority: 'normal',
      maxMemoryUsage: 1024 * 1024 * 1024, // 1GB
    },
    caching: {
      enabled: true,
      maxSize: 100 * 1024 * 1024, // 100MB
      strategy: 'lru',
    },
    network: {
      compression: true,
      prefetching: true,
      maxConcurrentRequests: 6,
    },
  },
  history: {
    timestamps: [],
    metrics: [],
  },
  alerts: [],
  lastUpdate: null,
};

const performanceSlice = createSlice({
  name: 'performance',
  initialState,
  reducers: {
    updateMetrics: (state, action: PayloadAction<Partial<PerformanceMetrics>>) => {
      state.metrics = {
        ...state.metrics,
        ...action.payload,
      };
      state.lastUpdate = Date.now();

      // Add to history
      state.history.timestamps.push(Date.now());
      state.history.metrics.push(action.payload);

      // Keep only last 100 entries
      if (state.history.timestamps.length > 100) {
        state.history.timestamps.shift();
        state.history.metrics.shift();
      }
    },
    updateSettings: (state, action: PayloadAction<Partial<PerformanceSettings>>) => {
      state.settings = {
        ...state.settings,
        ...action.payload,
      };
    },
    addAlert: (
      state,
      action: PayloadAction<Omit<PerformanceState['alerts'][0], 'id' | 'timestamp'>>
    ) => {
      state.alerts.push({
        ...action.payload,
        id: Date.now().toString(),
        timestamp: Date.now(),
      });
      // Keep only last 100 alerts
      if (state.alerts.length > 100) {
        state.alerts.shift();
      }
    },
    clearAlerts: state => {
      state.alerts = [];
    },
    resetMetrics: state => {
      state.metrics = initialState.metrics;
      state.history = initialState.history;
      state.lastUpdate = null;
    },
  },
});

export const { updateMetrics, updateSettings, addAlert, clearAlerts, resetMetrics } =
  performanceSlice.actions;

// Selectors
export const selectPerformanceMetrics = (state: { performance: PerformanceState }) =>
  state.performance.metrics;
export const selectPerformanceSettings = (state: { performance: PerformanceState }) =>
  state.performance.settings;
export const selectPerformanceHistory = (state: { performance: PerformanceState }) =>
  state.performance.history;
export const selectPerformanceAlerts = (state: { performance: PerformanceState }) =>
  state.performance.alerts;
export const selectLastUpdate = (state: { performance: PerformanceState }) =>
  state.performance.lastUpdate;

export default performanceSlice.reducer;
