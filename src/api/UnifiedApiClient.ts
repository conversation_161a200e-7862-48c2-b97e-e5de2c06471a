/**
 * Unified API Client
 * Consolidates all API clients and network functionality
 */

import { EventEmitter } from 'events';
import { logger } from '../logging/Logger';
import { cacheManager } from '../core/cache/CacheManager';
import { errorManager } from '../core/ErrorManager';

// Unified API types
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  headers?: Record<string, string>;
  authentication?: {
    type: 'bearer' | 'basic' | 'api-key' | 'oauth' | 'custom';
    token?: string;
    credentials?: {
      username: string;
      password: string;
    };
    oauth?: {
      clientId: string;
      clientSecret: string;
      redirectUri: string;
      scopes: string[];
    };
  };
  cache?: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
  compression?: boolean;
  validateStatus?: (status: number) => boolean;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestConfig;
  cached?: boolean;
}

export interface ApiError {
  message: string;
  code: string;
  status: number;
  details?: any;
  config?: RequestConfig;
}

export interface RequestConfig extends Omit<ApiConfig, 'baseUrl'> {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';
  data?: any;
  params?: Record<string, any>;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: {
    enabled: boolean;
    maxAttempts: number;
    delay: number;
    backoff: number;
  };
  heartbeat?: {
    enabled: boolean;
    interval: number;
    message: string;
  };
}

export class UnifiedApiClient extends EventEmitter {
  private config: ApiConfig;
  private requestQueue: Array<() => Promise<any>> = [];
  private activeRequests = new Map<string, AbortController>();
  private interceptors = {
    request: new Set<(config: RequestConfig) => RequestConfig | Promise<RequestConfig>>(),
    response: new Set<(response: ApiResponse) => ApiResponse | Promise<ApiResponse>>(),
    error: new Set<(error: ApiError) => ApiError | Promise<ApiError>>(),
  };

  constructor(config: Partial<ApiConfig> = {}) {
    super();
    this.config = {
      baseUrl: '',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: {
        enabled: true,
        ttl: 300000, // 5 minutes
        maxSize: 100,
      },
      compression: true,
      validateStatus: (status) => status >= 200 && status < 300,
      ...config,
    };
  }

  // HTTP Methods
  public async get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'GET' });
  }

  public async post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'POST', data });
  }

  public async put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'PUT', data });
  }

  public async delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'DELETE' });
  }

  public async patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'PATCH', data });
  }

  // Core request method
  public async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const requestId = this.generateRequestId();
    
    try {
      // Apply request interceptors
      let processedConfig = config;
      for (const interceptor of this.interceptors.request) {
        processedConfig = await interceptor(processedConfig);
      }

      // Check cache
      if (this.config.cache?.enabled && processedConfig.method === 'GET') {
        const cacheKey = this.generateCacheKey(processedConfig);
        const cachedResponse = cacheManager.getItem<ApiResponse<T>>(cacheKey);
        if (cachedResponse) {
          this.emit('cache-hit', { requestId, config: processedConfig });
          return { ...cachedResponse, cached: true };
        }
      }

      // Create abort controller
      const abortController = new AbortController();
      this.activeRequests.set(requestId, abortController);

      // Build request
      const fullUrl = this.buildUrl(processedConfig.url, processedConfig.params);
      const requestOptions = await this.buildRequestOptions(processedConfig, abortController.signal);

      // Execute request with retry logic
      const response = await this.executeWithRetry(fullUrl, requestOptions, processedConfig);

      // Process response
      const apiResponse = await this.processResponse<T>(response, processedConfig);

      // Apply response interceptors
      let processedResponse = apiResponse;
      for (const interceptor of this.interceptors.response) {
        processedResponse = await interceptor(processedResponse);
      }

      // Cache response
      if (this.config.cache?.enabled && processedConfig.method === 'GET') {
        const cacheKey = this.generateCacheKey(processedConfig);
        cacheManager.setItem(cacheKey, processedResponse, {
          maxAge: this.config.cache.ttl,
          type: 'api-response',
        });
      }

      this.emit('request-success', { requestId, config: processedConfig, response: processedResponse });
      return processedResponse;

    } catch (error) {
      const apiError = this.createApiError(error, config);
      
      // Apply error interceptors
      let processedError = apiError;
      for (const interceptor of this.interceptors.error) {
        processedError = await interceptor(processedError);
      }

      this.emit('request-error', { requestId, config, error: processedError });
      errorManager.handleError(new Error(processedError.message), {
        component: 'UnifiedApiClient',
        method: 'request',
        config,
      });

      throw processedError;
    } finally {
      this.activeRequests.delete(requestId);
    }
  }

  // WebSocket support
  public createWebSocket(config: WebSocketConfig): WebSocket {
    const ws = new WebSocket(config.url, config.protocols);

    // Setup reconnection logic
    if (config.reconnect?.enabled) {
      this.setupWebSocketReconnection(ws, config);
    }

    // Setup heartbeat
    if (config.heartbeat?.enabled) {
      this.setupWebSocketHeartbeat(ws, config.heartbeat);
    }

    return ws;
  }

  // Interceptors
  public addRequestInterceptor(interceptor: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>): void {
    this.interceptors.request.add(interceptor);
  }

  public addResponseInterceptor(interceptor: (response: ApiResponse) => ApiResponse | Promise<ApiResponse>): void {
    this.interceptors.response.add(interceptor);
  }

  public addErrorInterceptor(interceptor: (error: ApiError) => ApiError | Promise<ApiError>): void {
    this.interceptors.error.add(interceptor);
  }

  // Utility methods
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(config: RequestConfig): string {
    const key = `${config.method}_${config.url}_${JSON.stringify(config.params || {})}`;
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '');
  }

  private buildUrl(url: string, params?: Record<string, any>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;
    
    if (!params) return fullUrl;

    const urlObj = new URL(fullUrl);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObj.searchParams.append(key, String(value));
      }
    });

    return urlObj.toString();
  }

  private async buildRequestOptions(config: RequestConfig, signal: AbortSignal): Promise<RequestInit> {
    const options: RequestInit = {
      method: config.method,
      headers: { ...this.config.headers, ...config.headers },
      signal,
    };

    // Add authentication
    if (this.config.authentication) {
      await this.addAuthentication(options, this.config.authentication);
    }

    // Add body
    if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      if (config.data instanceof FormData) {
        options.body = config.data;
        delete (options.headers as any)['Content-Type']; // Let browser set it
      } else {
        options.body = JSON.stringify(config.data);
      }
    }

    return options;
  }

  private async addAuthentication(options: RequestInit, auth: NonNullable<ApiConfig['authentication']>): Promise<void> {
    const headers = options.headers as Record<string, string>;

    switch (auth.type) {
      case 'bearer':
        if (auth.token) {
          headers['Authorization'] = `Bearer ${auth.token}`;
        }
        break;
      case 'basic':
        if (auth.credentials) {
          const encoded = btoa(`${auth.credentials.username}:${auth.credentials.password}`);
          headers['Authorization'] = `Basic ${encoded}`;
        }
        break;
      case 'api-key':
        if (auth.token) {
          headers['X-API-Key'] = auth.token;
        }
        break;
      case 'oauth':
        // OAuth implementation would go here
        break;
    }
  }

  private async executeWithRetry(url: string, options: RequestInit, config: RequestConfig): Promise<Response> {
    let lastError: Error;

    for (let attempt = 0; attempt <= (config.retryAttempts || this.config.retryAttempts); attempt++) {
      try {
        const response = await fetch(url, {
          ...options,
          signal: AbortSignal.timeout(config.timeout || this.config.timeout),
        });

        if (this.config.validateStatus!(response.status)) {
          return response;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < (config.retryAttempts || this.config.retryAttempts)) {
          const delay = (config.retryDelay || this.config.retryDelay) * Math.pow(2, attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  private async processResponse<T>(response: Response, config: RequestConfig): Promise<ApiResponse<T>> {
    let data: T;

    switch (config.responseType || 'json') {
      case 'json':
        data = await response.json();
        break;
      case 'text':
        data = await response.text() as unknown as T;
        break;
      case 'blob':
        data = await response.blob() as unknown as T;
        break;
      case 'arraybuffer':
        data = await response.arrayBuffer() as unknown as T;
        break;
      default:
        data = await response.json();
    }

    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      config,
    };
  }

  private createApiError(error: any, config: RequestConfig): ApiError {
    return {
      message: error.message || 'Request failed',
      code: error.code || 'UNKNOWN_ERROR',
      status: error.status || 0,
      details: error,
      config,
    };
  }

  private setupWebSocketReconnection(ws: WebSocket, config: WebSocketConfig): void {
    // WebSocket reconnection logic would go here
  }

  private setupWebSocketHeartbeat(ws: WebSocket, heartbeat: NonNullable<WebSocketConfig['heartbeat']>): void {
    // WebSocket heartbeat logic would go here
  }

  // Cleanup
  public cancelAllRequests(): void {
    this.activeRequests.forEach(controller => controller.abort());
    this.activeRequests.clear();
  }

  public cancelRequest(requestId: string): void {
    const controller = this.activeRequests.get(requestId);
    if (controller) {
      controller.abort();
      this.activeRequests.delete(requestId);
    }
  }
}

// Create default instance
export const apiClient = new UnifiedApiClient();

// Export factory function
export function createApiClient(config: Partial<ApiConfig>): UnifiedApiClient {
  return new UnifiedApiClient(config);
}
