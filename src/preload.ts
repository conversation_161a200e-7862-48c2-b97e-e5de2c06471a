import { contextBridge, ipc<PERSON>enderer } from 'electron';

import { Bookmark } from './renderer/stores/useBookmarkStore';
import { HistoryItem } from './renderer/stores/useHistoryStore';
// Определяем безопасный, типизированный API для рендерера
export const electronAPI = {
  // Settings
  getSettings: (): Promise<object> => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings: object): Promise<void> => ipcRenderer.invoke('save-settings', settings),
  onSettingsChanged: (callback: (settings: object) => void) =>
    ipcRenderer.on('settings-changed', (_event, value) => callback(value)),

  // Session
  saveSession: (urls: string[]): void => ipcRenderer.send('save-session', urls),
  restoreSession: (): Promise<string[] | null> => ipcRenderer.invoke('restore-session'),

  // Bookmarks
  getBookmarks: (): Promise<Bookmark[]> => ipcRenderer.invoke('get-bookmarks'),
  addBookmark: (bookmark: { url: string; title: string }): Promise<Bookmark> => ipcRenderer.invoke('add-bookmark', bookmark),
  removeBookmark: (id: string): Promise<boolean> => ipcRenderer.invoke('remove-bookmark', id),

  // History
  getHistory: (): Promise<HistoryItem[]> => ipcRenderer.invoke('get-history'),
  addHistoryItem: (item: Omit<HistoryItem, 'id'>): Promise<HistoryItem> => ipcRenderer.invoke('add-history-item', item),
  clearHistory: (): Promise<boolean> => ipcRenderer.invoke('clear-history'),
  clearHistoryByRange: (sinceTimestamp: number): Promise<boolean> => ipcRenderer.invoke('clear-history-range', sinceTimestamp),

  // Tabs
  requestNewTab: (callback: (url?: string) => void) =>
    ipcRenderer.on('request-new-tab', (_event, url) => callback(url)),

  // Context Menu
  showContextMenu: (context: { type: string; data: any }): void => ipcRenderer.send('show-context-menu', context),
  onContextMenuCommand: (callback: (command: { command: string; [key: string]: any }) => void) => {
    const handler = (_event: any, command: { command: string; [key: string]: any }) => callback(command);
    ipcRenderer.on('context-menu-command', handler);
    // Return a cleanup function
    return () => ipcRenderer.removeListener('context-menu-command', handler);
  },
};

contextBridge.exposeInMainWorld('electronAPI', electronAPI);