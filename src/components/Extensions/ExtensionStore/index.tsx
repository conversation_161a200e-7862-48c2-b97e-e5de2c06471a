import {
  ShoppingCart as CartIcon,
  Category as CategoryIcon,
  Download as DownloadIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Favorite as FavoriteIcon,
  FilterList as FilterIcon,
  Info as InfoIcon,
  AttachMoney as MoneyIcon,
  Report as ReportIcon,
  RateReview as ReviewIcon,
  Search as SearchIcon,
  Security as SecurityIcon,
  Share as ShareIcon,
  Sort as SortIcon,
  Star as StarIcon,
  LocalOffer as TagIcon,
  Verified as VerifiedIcon,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Avatar,
  Badge,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CardMedia,
  Checkbox,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Drawer,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Pagination,
  Rating,
  Select,
  Slider,
  Snackbar,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import {
  installExtension,
  uninstallExtension,
  updateExtension,
} from '../../../store/slices/extensionsSlice';
import { RootState } from '../../../store/types';

interface Extension {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  icon: string;
  category: string;
  tags: string[];
  price: number;
  rating: number;
  reviewCount: number;
  downloads: number;
  verified: boolean;
  featured: boolean;
  promoted: boolean;
  compatibility: {
    isCompatible: boolean;
    minVersion?: string;
    maxVersion?: string;
  };
  trial?: {
    enabled: boolean;
    days: number;
  };
  lastUpdated: string;
}

interface ExtensionStoreProps {
  onInstall?: (extension: Extension) => void;
  onUninstall?: (extension: Extension) => void;
  onUpdate?: (extension: Extension) => void;
}

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const FilterDrawer = styled(Drawer)(({ theme }) => ({
  width: 300,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: 300,
    boxSizing: 'border-box',
  },
}));

const PriceRange = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
}));

const ExtensionStore: React.FC<ExtensionStoreProps> = ({
  onInstall,
  onUninstall,
  onUpdate,
}): JSX.Element => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const extensions = useSelector((state: RootState) => state.extensions.store.extensions);
  const installedExtensions = useSelector((state: RootState) => state.extensions.installed);
  const loading = useSelector((state: RootState) => state.extensions.store.loading);
  const error = useSelector((state: RootState) => state.extensions.store.error);

  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOpen, setFilterOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [minRating, setMinRating] = useState<number>(0);
  const [compatibleOnly, setCompatibleOnly] = useState(true);
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [featuredOnly, setFeaturedOnly] = useState(false);
  const [promotedOnly, setPromotedOnly] = useState(false);
  const [sortBy, setSortBy] = useState<string>('rating');
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(12);
  const [selectedExtension, setSelectedExtension] = useState<Extension | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Filter and sort extensions
  const filteredExtensions = extensions.filter((extension: Extension) => {
    const matchesSearch =
      extension.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      extension.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || extension.category === selectedCategory;
    const matchesTags =
      selectedTags.length === 0 ||
      selectedTags.every((tag: string) => extension.tags.includes(tag));
    const matchesPrice = extension.price >= priceRange[0] && extension.price <= priceRange[1];
    const matchesRating = extension.rating >= minRating;
    const matchesCompatibility = !compatibleOnly || extension.compatibility.isCompatible;
    const matchesVerified = !verifiedOnly || extension.verified;
    const matchesFeatured = !featuredOnly || extension.featured;
    const matchesPromoted = !promotedOnly || extension.promoted;

    return (
      matchesSearch &&
      matchesCategory &&
      matchesTags &&
      matchesPrice &&
      matchesRating &&
      matchesCompatibility &&
      matchesVerified &&
      matchesFeatured &&
      matchesPromoted
    );
  });

  // Sort extensions
  const sortedExtensions = [...filteredExtensions].sort((a: Extension, b: Extension) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'downloads':
        return b.downloads - a.downloads;
      case 'reviews':
        return b.reviewCount - a.reviewCount;
      case 'price':
        return a.price - b.price;
      case 'date':
        return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
      case 'name':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  // Pagination
  const totalPages = Math.ceil(sortedExtensions.length / itemsPerPage);
  const paginatedExtensions = sortedExtensions.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage
  );

  // Handlers
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(1);
  };

  const handleFilterToggle = () => {
    setFilterOpen(!filterOpen);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPage(1);
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => (prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]));
    setPage(1);
  };

  const handlePriceRangeChange = (event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as [number, number]);
    setPage(1);
  };

  const handleRatingChange = (event: Event, newValue: number | number[]) => {
    setMinRating(newValue as number);
    setPage(1);
  };

  const handleSortChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSortBy(event.target.value as string);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const handleExtensionClick = (extension: Extension) => {
    setSelectedExtension(extension);
    setDetailsOpen(true);
  };

  const handleInstall = async (extension: Extension) => {
    try {
      await dispatch(installExtension(extension));
      setSnackbar({
        open: true,
        message: t('extensions.store.notifications.installSuccess'),
        severity: 'success',
      });
      onInstall?.(extension);
    } catch (error) {
      setSnackbar({
        open: true,
        message: t('extensions.store.notifications.installError'),
        severity: 'error',
      });
    }
  };

  const handleUninstall = async (extension: Extension) => {
    try {
      await dispatch(uninstallExtension(extension));
      setSnackbar({
        open: true,
        message: t('extensions.store.notifications.uninstallSuccess'),
        severity: 'success',
      });
      onUninstall?.(extension);
    } catch (error) {
      setSnackbar({
        open: true,
        message: t('extensions.store.notifications.uninstallError'),
        severity: 'error',
      });
    }
  };

  const handleUpdate = async (extension: Extension) => {
    try {
      await dispatch(updateExtension(extension));
      setSnackbar({
        open: true,
        message: t('extensions.store.notifications.updateSuccess'),
        severity: 'success',
      });
      onUpdate?.(extension);
    } catch (error) {
      setSnackbar({
        open: true,
        message: t('extensions.store.notifications.updateError'),
        severity: 'error',
      });
    }
  };

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading) {
    return (
      <Box
        sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Typography variant="h4" component="h1">
          {t('extensions.store.title')}
        </Typography>
        <TextField
          placeholder={t('extensions.store.search')}
          value={searchQuery}
          onChange={handleSearch}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
          }}
          sx={{ flexGrow: 1 }}
        />
        <IconButton onClick={handleFilterToggle}>
          <FilterIcon />
        </IconButton>
        <FormControl sx={{ minWidth: 120 }}>
          <Select
            value={sortBy}
            onChange={handleSortChange}
            startAdornment={<SortIcon sx={{ mr: 1, color: 'text.secondary' }} />}
          >
            <MenuItem value="rating">{t('extensions.store.sortBy.rating')}</MenuItem>
            <MenuItem value="downloads">{t('extensions.store.sortBy.downloads')}</MenuItem>
            <MenuItem value="reviews">{t('extensions.store.sortBy.reviews')}</MenuItem>
            <MenuItem value="price">{t('extensions.store.sortBy.price')}</MenuItem>
            <MenuItem value="date">{t('extensions.store.sortBy.date')}</MenuItem>
            <MenuItem value="name">{t('extensions.store.sortBy.name')}</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Filter Drawer */}
      <FilterDrawer anchor="right" open={filterOpen} onClose={handleFilterToggle}>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            {t('extensions.store.filters')}
          </Typography>

          {/* Categories */}
          <Typography variant="subtitle1" gutterBottom>
            {t('extensions.store.categories')}
          </Typography>
          <List>
            <ListItem button onClick={() => handleCategoryChange('all')}>
              <ListItemText primary={t('extensions.store.all')} />
            </ListItem>
            {Object.entries(t('extensions.store.categories', { returnObjects: true })).map(
              ([key, value]) => (
                <ListItem
                  key={key}
                  button
                  onClick={() => handleCategoryChange(key)}
                  selected={selectedCategory === key}
                >
                  <ListItemText primary={value} />
                </ListItem>
              )
            )}
          </List>

          <Divider sx={{ my: 2 }} />

          {/* Tags */}
          <Typography variant="subtitle1" gutterBottom>
            {t('extensions.store.tags')}
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {Object.entries(t('extensions.store.tags', { returnObjects: true })).map(
              ([key, value]) => (
                <Chip
                  key={key}
                  label={value}
                  onClick={() => handleTagToggle(key)}
                  color={selectedTags.includes(key) ? 'primary' : 'default'}
                  icon={<TagIcon />}
                />
              )
            )}
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Price Range */}
          <Typography variant="subtitle1" gutterBottom>
            {t('extensions.store.priceRange')}
          </Typography>
          <PriceRange>
            <Slider
              value={priceRange}
              onChange={handlePriceRangeChange}
              valueLabelDisplay="auto"
              min={0}
              max={100}
            />
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">${priceRange[0]}</Typography>
              <Typography variant="body2">${priceRange[1]}</Typography>
            </Box>
          </PriceRange>

          <Divider sx={{ my: 2 }} />

          {/* Rating Filter */}
          <Typography variant="subtitle1" gutterBottom>
            {t('extensions.store.minimumRating')}
          </Typography>
          <Box sx={{ px: 2 }}>
            <Slider
              value={minRating}
              onChange={handleRatingChange}
              valueLabelDisplay="auto"
              min={0}
              max={5}
              step={0.5}
            />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Rating value={minRating} readOnly />
              <Typography variant="body2">{minRating}+</Typography>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Additional Filters */}
          <FormControlLabel
            control={
              <Checkbox
                checked={compatibleOnly}
                onChange={e => setCompatibleOnly(e.target.checked)}
              />
            }
            label={t('extensions.store.compatibleOnly')}
          />
          <FormControlLabel
            control={
              <Checkbox checked={verifiedOnly} onChange={e => setVerifiedOnly(e.target.checked)} />
            }
            label={t('extensions.store.verifiedOnly')}
          />
          <FormControlLabel
            control={
              <Checkbox checked={featuredOnly} onChange={e => setFeaturedOnly(e.target.checked)} />
            }
            label={t('extensions.store.featuredOnly')}
          />
          <FormControlLabel
            control={
              <Checkbox checked={promotedOnly} onChange={e => setPromotedOnly(e.target.checked)} />
            }
            label={t('extensions.store.promotedOnly')}
          />
        </Box>
      </FilterDrawer>

      {/* Extension Grid */}
      <Grid container spacing={3}>
        {paginatedExtensions.map((extension: Extension) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={extension.id}>
            <StyledCard>
              <CardMedia component="img" height="140" image={extension.icon} alt={extension.name} />
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h6" component="h2" sx={{ flexGrow: 1 }}>
                    {extension.name}
                  </Typography>
                  {extension.verified && (
                    <Tooltip title={t('extensions.store.verified')}>
                      <VerifiedIcon color="primary" />
                    </Tooltip>
                  )}
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {extension.description}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Rating value={extension.rating} readOnly size="small" />
                  <Typography variant="body2" color="text.secondary">
                    ({extension.reviewCount})
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {extension.tags.map(tag => (
                    <Chip key={tag} label={t(`extensions.store.tags.${tag}`)} size="small" />
                  ))}
                </Box>
              </CardContent>
              <CardActions>
                {installedExtensions.includes(extension.id) ? (
                  <>
                    <Button size="small" color="primary" onClick={() => handleUpdate(extension)}>
                      {t('extensions.store.actions.update')}
                    </Button>
                    <Button size="small" color="error" onClick={() => handleUninstall(extension)}>
                      {t('extensions.store.actions.uninstall')}
                    </Button>
                  </>
                ) : (
                  <Button size="small" color="primary" onClick={() => handleInstall(extension)}>
                    {extension.price > 0
                      ? t('extensions.store.actions.buy')
                      : t('extensions.store.actions.install')}
                  </Button>
                )}
                <Button size="small" onClick={() => handleExtensionClick(extension)}>
                  {t('extensions.store.actions.details')}
                </Button>
              </CardActions>
            </StyledCard>
          </Grid>
        ))}
      </Grid>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Pagination count={totalPages} page={page} onChange={handlePageChange} color="primary" />
      </Box>

      {/* Extension Details Dialog */}
      <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="md" fullWidth>
        {selectedExtension && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="h6">{selectedExtension.name}</Typography>
                {selectedExtension.verified && <VerifiedIcon color="primary" />}
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <img
                    src={selectedExtension.icon}
                    alt={selectedExtension.name}
                    style={{ width: '100%', borderRadius: 8 }}
                  />
                </Grid>
                <Grid item xs={12} md={8}>
                  <Typography variant="body1" paragraph>
                    {selectedExtension.description}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Rating value={selectedExtension.rating} readOnly />
                    <Typography variant="body2" color="text.secondary">
                      ({selectedExtension.reviewCount} {t('extensions.store.sortBy.reviews')})
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {selectedExtension.tags.map(tag => (
                      <Chip key={tag} label={t(`extensions.store.tags.${tag}`)} size="small" />
                    ))}
                  </Box>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('extensions.store.categories')}
                  </Typography>
                  <Typography variant="body2" paragraph>
                    {t(`extensions.store.categories.${selectedExtension.category}`)}
                  </Typography>
                  {selectedExtension.trial && (
                    <Typography variant="body2" color="primary" paragraph>
                      {t('extensions.store.trial', { days: selectedExtension.trial.days })}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsOpen(false)}>{t('common.close')}</Button>
              {installedExtensions.includes(selectedExtension.id) ? (
                <>
                  <Button color="primary" onClick={() => handleUpdate(selectedExtension)}>
                    {t('extensions.store.actions.update')}
                  </Button>
                  <Button color="error" onClick={() => handleUninstall(selectedExtension)}>
                    {t('extensions.store.actions.uninstall')}
                  </Button>
                </>
              ) : (
                <Button color="primary" onClick={() => handleInstall(selectedExtension)}>
                  {selectedExtension.price > 0
                    ? t('extensions.store.actions.buy')
                    : t('extensions.store.actions.install')}
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Snackbar */}
      <Snackbar open={snackbar.open} autoHideDuration={6000} onClose={handleSnackbarClose}>
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ExtensionStore;
