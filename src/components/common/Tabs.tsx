import React, { useState } from 'react';
import { twMerge } from 'tailwind-merge';

export interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
  icon?: React.ReactNode;
}

export interface TabsProps {
  tabs: Tab[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  variant?: 'line' | 'enclosed' | 'soft-rounded' | 'solid-rounded';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

export const Tabs: React.FC<TabsProps> = ({
  tabs,
  defaultTab,
  onChange,
  className,
  variant = 'line',
  size = 'md',
  fullWidth = false,
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    onChange?.(tabId);
  };

  const variants = {
    line: 'border-b border-gray-200',
    enclosed: 'border border-gray-200 rounded-t-lg',
    'soft-rounded': 'space-x-2',
    'solid-rounded': 'space-x-2',
  };

  const sizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const tabVariants = {
    line: {
      active: 'border-b-2 border-blue-500 text-blue-600',
      inactive: 'text-gray-500 hover:text-gray-700 hover:border-gray-300',
    },
    enclosed: {
      active: 'bg-white border-b-0 text-blue-600',
      inactive: 'bg-gray-50 text-gray-500 hover:text-gray-700',
    },
    'soft-rounded': {
      active: 'bg-blue-100 text-blue-700',
      inactive: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100',
    },
    'solid-rounded': {
      active: 'bg-blue-500 text-white',
      inactive: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100',
    },
  };

  return (
    <div className={twMerge('w-full', className)}>
      <div className={twMerge('flex', variants[variant], fullWidth && 'justify-between')}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={twMerge(
              'flex items-center px-4 py-2 font-medium transition-colors duration-200',
              sizes[size],
              tabVariants[variant][activeTab === tab.id ? 'active' : 'inactive'],
              tab.disabled && 'opacity-50 cursor-not-allowed',
              variant === 'soft-rounded' || variant === 'solid-rounded' ? 'rounded-full' : '',
              fullWidth && 'flex-1 justify-center'
            )}
            onClick={() => !tab.disabled && handleTabClick(tab.id)}
            disabled={tab.disabled}
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            {tab.label}
          </button>
        ))}
      </div>
      <div className="mt-4">{tabs.find(tab => tab.id === activeTab)?.content}</div>
    </div>
  );
};
