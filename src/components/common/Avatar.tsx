import React from 'react';
import { twMerge } from 'tailwind-merge';

export interface AvatarProps {
  src?: string;
  alt?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'circle' | 'square';
  status?: 'online' | 'offline' | 'away' | 'busy';
  className?: string;
  fallback?: React.ReactNode;
  onClick?: () => void;
}

export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = '',
  size = 'md',
  shape = 'circle',
  status,
  className,
  fallback,
  onClick,
}) => {
  const [error, setError] = React.useState(false);

  const sizes = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl',
  };

  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-500',
    away: 'bg-yellow-500',
    busy: 'bg-red-500',
  };

  const statusSizes = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4',
  };

  const handleError = () => {
    setError(true);
  };

  const renderFallback = () => {
    if (fallback) return fallback;
    return (
      <div className="flex items-center justify-center w-full h-full bg-gray-200 text-gray-600">
        {alt.charAt(0).toUpperCase()}
      </div>
    );
  };

  return (
    <div className="relative inline-block">
      <div
        className={twMerge(
          'relative overflow-hidden',
          sizes[size],
          shape === 'circle' ? 'rounded-full' : 'rounded-lg',
          onClick && 'cursor-pointer',
          className
        )}
        onClick={onClick}
      >
        {src && !error ? (
          <img src={src} alt={alt} className="w-full h-full object-cover" onError={handleError} />
        ) : (
          renderFallback()
        )}
      </div>
      {status && (
        <span
          className={twMerge(
            'absolute bottom-0 right-0 block rounded-full ring-2 ring-white',
            statusColors[status],
            statusSizes[size]
          )}
        />
      )}
    </div>
  );
};
