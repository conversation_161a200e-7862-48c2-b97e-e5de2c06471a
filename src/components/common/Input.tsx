import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { twMerge } from 'tailwind-merge';

import { logger } from '../../core/EnhancedLogger';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outlined' | 'underlined';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  clearable?: boolean;
  showCharacterCount?: boolean;
  maxLength?: number;
  validation?: 'none' | 'email' | 'url' | 'phone' | 'number';
  debounceMs?: number;
  'data-testid'?: string;
  'aria-describedby'?: string;
}

export interface InputRef {
  focus: () => void;
  blur: () => void;
  select: () => void;
  clear: () => void;
  getValue: () => string;
}

export const Input = forwardRef<InputRef, InputProps>(
  (
    {
      label,
      error,
      helperText,
      leftIcon,
      rightIcon,
      variant = 'default',
      size = 'md',
      fullWidth = false,
      clearable = false,
      showCharacterCount = false,
      maxLength,
      validation = 'none',
      debounceMs = 0,
      className,
      value,
      onChange,
      onFocus,
      onBlur,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [internalValue, setInternalValue] = useState(value || '');
    const [isFocused, setIsFocused] = useState(false);
    const [validationError, setValidationError] = useState<string>('');
    const debounceRef = useRef<NodeJS.Timeout>();

    useImperativeHandle(ref, () => ({
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
      select: () => inputRef.current?.select(),
      clear: () => {
        setInternalValue('');
        if (onChange) {
          const event = { target: { value: '' } } as React.ChangeEvent<HTMLInputElement>;
          onChange(event);
        }
      },
      getValue: () => internalValue.toString(),
    }));

    const validateInput = useCallback(
      (val: string) => {
        if (validation === 'none') return '';

        switch (validation) {
          case 'email':
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)
              ? ''
              : 'Please enter a valid email address';
          case 'url':
            try {
              new URL(val);
              return '';
            } catch {
              return 'Please enter a valid URL';
            }
          case 'phone':
            return /^[\+]?[1-9][\d]{0,15}$/.test(val) ? '' : 'Please enter a valid phone number';
          case 'number':
            return /^\d+$/.test(val) ? '' : 'Please enter a valid number';
          default:
            return '';
        }
      },
      [validation]
    );

    const handleChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = event.target.value;
        setInternalValue(newValue);

        // Validate input
        const validationErr = validateInput(newValue);
        setValidationError(validationErr);

        // Debounced onChange
        if (debounceMs > 0) {
          if (debounceRef.current) {
            clearTimeout(debounceRef.current);
          }
          debounceRef.current = setTimeout(() => {
            onChange?.(event);
            logger.debug('Input value changed (debounced)', {
              value: newValue,
              validation,
              hasError: !!validationErr,
            });
          }, debounceMs);
        } else {
          onChange?.(event);
          logger.debug('Input value changed', {
            value: newValue,
            validation,
            hasError: !!validationErr,
          });
        }
      },
      [onChange, debounceMs, validateInput, validation]
    );

    const handleFocus = useCallback(
      (event: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(true);
        onFocus?.(event);
      },
      [onFocus]
    );

    const handleBlur = useCallback(
      (event: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        onBlur?.(event);
      },
      [onBlur]
    );

    const handleClear = useCallback(() => {
      setInternalValue('');
      if (onChange) {
        const event = { target: { value: '' } } as React.ChangeEvent<HTMLInputElement>;
        onChange(event);
      }
      inputRef.current?.focus();
    }, [onChange]);

    useEffect(() => {
      if (value !== undefined) {
        setInternalValue(value.toString());
      }
    }, [value]);

    const variants = {
      default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
      filled:
        'bg-gray-50 border-transparent focus:bg-white focus:border-blue-500 focus:ring-blue-500',
      outlined: 'border-2 border-gray-300 focus:border-blue-500 focus:ring-0',
      underlined:
        'border-0 border-b-2 border-gray-300 focus:border-blue-500 focus:ring-0 rounded-none',
    };

    const sizes = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-3 py-2 text-base',
      lg: 'px-4 py-3 text-lg',
      xl: 'px-5 py-4 text-xl',
    };

    const baseStyles = [
      'block w-full rounded-md shadow-sm transition-colors duration-200',
      'disabled:opacity-50 disabled:bg-gray-100 disabled:cursor-not-allowed',
      'placeholder:text-gray-400',
    ].join(' ');

    const currentError = error || validationError;
    const width = fullWidth ? 'w-full' : '';
    const characterCount = internalValue.toString().length;

    return (
      <div className={width}>
        {label && (
          <label
            htmlFor={props.id}
            className={twMerge(
              'block text-sm font-medium mb-1 transition-colors',
              currentError ? 'text-red-600' : 'text-gray-700',
              isFocused && !currentError && 'text-blue-600'
            )}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span
                className={twMerge(
                  'transition-colors',
                  currentError ? 'text-red-400' : 'text-gray-400',
                  isFocused && !currentError && 'text-blue-400'
                )}
              >
                {leftIcon}
              </span>
            </div>
          )}
          <input
            ref={inputRef}
            className={twMerge(
              baseStyles,
              variants[variant],
              sizes[size],
              leftIcon && 'pl-10',
              (rightIcon || clearable) && 'pr-10',
              currentError && 'border-red-500 focus:border-red-500 focus:ring-red-500',
              className
            )}
            value={internalValue}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            maxLength={maxLength}
            data-testid={testId}
            aria-invalid={!!currentError}
            aria-describedby={currentError ? `${props.id}-error` : props['aria-describedby']}
            {...props}
          />
          {(rightIcon || clearable) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {clearable && internalValue && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                  aria-label="Clear input"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
              {rightIcon && !clearable && (
                <span
                  className={twMerge(
                    'transition-colors pointer-events-none',
                    currentError ? 'text-red-400' : 'text-gray-400',
                    isFocused && !currentError && 'text-blue-400'
                  )}
                >
                  {rightIcon}
                </span>
              )}
            </div>
          )}
        </div>
        <div className="flex justify-between items-start mt-1">
          <div className="flex-1">
            {currentError && (
              <p id={`${props.id}-error`} className="text-sm text-red-600" role="alert">
                {currentError}
              </p>
            )}
            {helperText && !currentError && <p className="text-sm text-gray-500">{helperText}</p>}
          </div>
          {showCharacterCount && maxLength && (
            <p
              className={twMerge(
                'text-xs ml-2 flex-shrink-0',
                characterCount > maxLength * 0.9 ? 'text-yellow-600' : 'text-gray-500',
                characterCount >= maxLength && 'text-red-600'
              )}
            >
              {characterCount}/{maxLength}
            </p>
          )}
        </div>
      </div>
    );
  }
);

Input.displayName = 'Input';
