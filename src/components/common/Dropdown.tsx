import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { twMerge } from 'tailwind-merge';

export interface DropdownItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  divider?: boolean;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  onSelect?: (item: DropdownItem) => void;
  className?: string;
  align?: 'left' | 'right';
  width?: 'auto' | 'full';
  maxHeight?: string;
}

export const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  onSelect,
  className,
  align = 'left',
  width = 'auto',
  maxHeight = '300px',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen && triggerRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const dropdownRect = dropdownRef.current?.getBoundingClientRect();
      const spaceBelow = window.innerHeight - triggerRect.bottom;
      const spaceAbove = triggerRect.top;
      const spaceRight = window.innerWidth - triggerRect.right;
      const spaceLeft = triggerRect.left;

      const top =
        spaceBelow >= (dropdownRect?.height || 0)
          ? triggerRect.bottom
          : triggerRect.top - (dropdownRect?.height || 0);

      const left =
        align === 'left'
          ? triggerRect.left
          : Math.min(triggerRect.right, window.innerWidth - (dropdownRect?.width || 0));

      setPosition({ top, left });
    }
  }, [isOpen, align]);

  const handleSelect = (item: DropdownItem) => {
    if (!item.disabled) {
      onSelect?.(item);
      setIsOpen(false);
    }
  };

  return (
    <div className="relative inline-block" ref={triggerRef}>
      <div onClick={() => setIsOpen(!isOpen)}>{trigger}</div>
      {isOpen &&
        createPortal(
          <div
            ref={dropdownRef}
            className={twMerge(
              'absolute z-50 mt-1 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
              width === 'full' && 'w-full',
              className
            )}
            style={{
              top: `${position.top}px`,
              left: `${position.left}px`,
              maxHeight,
            }}
          >
            <div
              className="py-1 overflow-auto"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="options-menu"
            >
              {items.map(item =>
                item.divider ? (
                  <div key={item.id} className="border-t border-gray-100 my-1" />
                ) : (
                  <button
                    key={item.id}
                    className={twMerge(
                      'w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none',
                      item.disabled && 'opacity-50 cursor-not-allowed'
                    )}
                    onClick={() => handleSelect(item)}
                    disabled={item.disabled}
                    role="menuitem"
                  >
                    <div className="flex items-center">
                      {item.icon && <span className="mr-2">{item.icon}</span>}
                      {item.label}
                    </div>
                  </button>
                )
              )}
            </div>
          </div>,
          document.body
        )}
    </div>
  );
};
