import {
  Close as CloseIcon,
  Error as ErrorIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import {
  Badge,
  Box,
  Button,
  CircularProgress,
  Collapse,
  IconButton,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import type { Theme } from '@mui/material/styles';
import {
  AnimatePresence,
  PanInfo,
  Transition,
  Variants,
  motion,
  useAnimation,
  useMotionValue,
  useReducedMotion,
  useTransform,
} from 'framer-motion';
import React, {
  KeyboardEvent,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useInView } from 'react-intersection-observer';

import {
  Notification,
  NotificationAnimation,
  NotificationPosition,
  NotificationSound,
  NotificationTheme,
  NotificationType,
  playNotificationSound as playSound,
  useNotification,
} from '../providers/NotificationProvider';
import {
  NotificationAccessibility,
  NotificationAction,
  NotificationBehavior,
  NotificationGroup,
  NotificationLayout,
} from '../types/notifications';

// Helper functions
const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'error':
      return <ErrorIcon />;
    case 'success':
      return <SuccessIcon />;
    case 'warning':
      return <WarningIcon />;
    case 'info':
      return <InfoIcon />;
    default:
      return <InfoIcon />;
  }
};

// Default animation functions
const getInitialAnimation = (position: NotificationPosition): Record<string, number> => {
  switch (position) {
    case 'top-left':
    case 'top-right':
    case 'top-center':
      return { opacity: 0, y: -20, scale: 0.9 };
    case 'bottom-left':
    case 'bottom-right':
    case 'bottom-center':
      return { opacity: 0, y: 20, scale: 0.9 };
    default:
      return { opacity: 0, scale: 0.9 };
  }
};

const getExitAnimation = (position: NotificationPosition): Record<string, number> => {
  switch (position) {
    case 'top-left':
    case 'top-right':
    case 'top-center':
      return { opacity: 0, y: -20, scale: 0.9 };
    case 'bottom-left':
    case 'bottom-right':
    case 'bottom-center':
      return { opacity: 0, y: 20, scale: 0.9 };
    default:
      return { opacity: 0, scale: 0.9 };
  }
};

// Icon animation variants
const iconVariants: Variants = {
  initial: { scale: 0, rotate: -180 },
  animate: {
    scale: 1,
    rotate: 0,
    transition: {
      type: 'spring' as const,
      stiffness: 260,
      damping: 20,
    },
  },
  hover: {
    scale: 1.2,
    rotate: 360,
    transition: {
      type: 'spring' as const,
      stiffness: 400,
      damping: 10,
    },
  },
};

// Memoized Notification Item Component
const NotificationItemComponent: React.FC<{
  notification: Notification;
  onClose: (id: string) => void;
  onAction: (action: () => void, id: string) => void;
  position: NotificationPosition;
  index: number;
  total: number;
  theme?: {
    background?: string;
    text?: string;
    icon?: string;
    progress?: string;
    border?: string;
  };
  animation?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    exit?: Record<string, number>;
    transition?: Transition;
  };
  sound?: {
    show?: string;
    hide?: string;
    hover?: string;
    tap?: string;
  };
}> = memo(
  ({
    notification,
    onClose,
    onAction,
    position,
    index,
    total,
    theme: customTheme,
    animation: customAnimation,
    sound,
  }) => {
    const {
      id,
      type,
      title,
      message,
      actions,
      duration,
      sound: notificationSound,
      vibration,
      allowHTML,
      icon: customIcon,
    } = notification;

    const [progress, setProgress] = React.useState(100);
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const theme = useTheme();
    const controls = useAnimation();
    const notificationRef = useRef<HTMLDivElement>(null);
    const { playSound } = useNotification();

    useEffect(() => {
      if (duration) {
        const startTime = Date.now();
        const interval = setInterval(() => {
          const elapsed = Date.now() - startTime;
          const newProgress = Math.max(0, 100 - (elapsed / duration) * 100);
          setProgress(newProgress);
        }, 10);

        return () => clearInterval(interval);
      }
    }, [duration]);

    useEffect(() => {
      if (notificationSound) {
        playSound(notificationSound);
      }
    }, [notificationSound, playSound]);

    useEffect(() => {
      if (vibration && 'vibrate' in navigator) {
        navigator.vibrate(vibration);
      }
    }, [vibration]);

    const handleKeyDown = useCallback(
      (event: KeyboardEvent<HTMLDivElement>) => {
        switch (event.key) {
          case 'Escape':
            onClose(id);
            break;
          case 'Enter':
            if (actions && actions.length > 0) {
              const firstEnabledAction = actions.find(action => !action.disabled);
              if (firstEnabledAction) {
                onAction(firstEnabledAction.onClick, id);
              }
            }
            break;
          case 'ArrowUp':
            if (index > 0) {
              const prevNotification = document.querySelector(
                `[data-notification-index="${index - 1}"]`
              );
              (prevNotification as HTMLElement)?.focus();
            }
            break;
          case 'ArrowDown':
            if (index < total - 1) {
              const nextNotification = document.querySelector(
                `[data-notification-index="${index + 1}"]`
              );
              (nextNotification as HTMLElement)?.focus();
            }
            break;
          default:
            // Check for action shortcuts
            if (actions) {
              const action = actions.find(a => a.shortcut === event.key);
              if (action && !action.disabled) {
                onAction(action.onClick, id);
              }
            }
        }
      },
      [actions, onClose, onAction, id, index, total]
    );

    const handleDragEnd = useCallback(
      (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
        const threshold = 100;
        if (Math.abs(info.offset.x) > threshold) {
          onClose(id);
        } else {
          controls.start({ x: 0 });
        }
      },
      [controls, onClose, id]
    );

    const getNotificationColor = (type: NotificationType) => {
      if (customTheme?.background) return customTheme.background;
      const isDark = theme.palette.mode === 'dark';
      switch (type) {
        case 'error':
          return isDark ? 'error.dark' : 'error.main';
        case 'success':
          return isDark ? 'success.dark' : 'success.main';
        case 'warning':
          return isDark ? 'warning.dark' : 'warning.main';
        default:
          return isDark ? 'info.dark' : 'info.main';
      }
    };

    const getGroupStyle = (group?: string) => {
      if (!group) return {};
      return {
        borderLeft: `4px solid ${getNotificationColor(type)}`,
        pl: 1,
      };
    };

    const getAnimationProps = () => {
      if (customAnimation) {
        return {
          initial: customAnimation.initial || getInitialAnimation(position),
          animate: customAnimation.animate || controls,
          exit: customAnimation.exit || getExitAnimation(position),
          transition: customAnimation.transition || { duration: 0.3 },
        };
      }
      return {
        initial: getInitialAnimation(position),
        animate: controls,
        exit: getExitAnimation(position),
        transition: { duration: 0.3 },
      };
    };

    return (
      <motion.div
        ref={notificationRef}
        {...getAnimationProps()}
        role="alert"
        aria-live="polite"
        tabIndex={0}
        onKeyDown={handleKeyDown}
        data-notification-index={index}
        drag="x"
        dragConstraints={{ left: 0, right: 0 }}
        onDragEnd={handleDragEnd}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Box
          sx={{
            bgcolor: getNotificationColor(type),
            color: customTheme?.text || 'common.white',
            borderRadius: 1,
            p: 2,
            boxShadow: theme.shadows[2],
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            position: 'relative',
            overflow: 'hidden',
            '&:hover': {
              boxShadow: theme.shadows[4],
            },
            ...getGroupStyle(notification.group),
          }}
        >
          <IconButton
            onClick={() => onClose(id)}
            aria-label="Close notification"
            sx={{
              position: 'absolute',
              top: 1,
              right: 1,
              color: customTheme?.text || 'common.white',
              opacity: 0.6,
              '&:hover': {
                opacity: 1,
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <motion.div
              variants={iconVariants}
              initial="initial"
              animate="animate"
              whileHover="hover"
            >
              <Box sx={{ mt: 0.5, color: customTheme?.icon }}>{getNotificationIcon(type)}</Box>
            </motion.div>
            <Box sx={{ flex: 1 }}>
              <Box component="h3" sx={{ m: 0, fontSize: 16, fontWeight: 600 }}>
                {title}
              </Box>
              <Box
                component="div"
                sx={{ m: 0, fontSize: 14, lineHeight: 1.4 }}
                dangerouslySetInnerHTML={allowHTML ? { __html: message } : undefined}
              >
                {!allowHTML && message}
              </Box>
            </Box>
          </Box>
          {actions && actions.length > 0 && (
            <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant="contained"
                  color={
                    action.type === 'danger'
                      ? 'error'
                      : action.type === 'secondary'
                        ? 'secondary'
                        : 'primary'
                  }
                  onClick={() => !action.disabled && onAction(action.onClick, id)}
                  disabled={action.disabled}
                  startIcon={action.icon}
                  sx={{
                    py: 0.75,
                    px: 1.5,
                    fontSize: 14,
                    fontWeight: 500,
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    '&:hover': {
                      bgcolor: 'rgba(255, 255, 255, 0.3)',
                    },
                  }}
                >
                  {action.label}
                  {action.shortcut && (
                    <Box component="span" sx={{ ml: 1, opacity: 0.7 }}>
                      ({action.shortcut})
                    </Box>
                  )}
                </Button>
              ))}
            </Box>
          )}
          {duration && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                height: 4,
                bgcolor: customTheme?.progress || 'rgba(255, 255, 255, 0.3)',
                width: `${progress}%`,
                transition: 'width 0.1s linear',
              }}
            />
          )}
        </Box>
      </motion.div>
    );
  }
);

NotificationItemComponent.displayName = 'NotificationItemComponent';

// Main Component
const NotificationList: React.FC = () => {
  const { notifications, settings, removeNotification, markAsRead, updateSettings, playSound } =
    useNotification();
  const theme = useTheme();
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  const [draggedGroup, setDraggedGroup] = useState<string | null>(null);
  const [groupOrder, setGroupOrder] = useState<string[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const groupedNotifications = useCallback(() => {
    const groups: Record<string, NotificationGroup> = {};

    notifications.forEach(notification => {
      if (notification.group) {
        if (!groups[notification.group]) {
          groups[notification.group] = {
            id: notification.group,
            notifications: [],
            type: notification.type,
            position: notification.position,
            isExpanded: expandedGroups[notification.group] || false,
            theme: notification.theme,
            sound: {
              expand: notification.sound,
              collapse: notification.sound,
              remove: notification.sound,
            },
            animation: {
              expand: notification.animation?.animate,
              collapse: notification.animation?.exit,
              drag: notification.animation?.initial,
            },
          };
        }
        groups[notification.group].notifications.push(notification);
      }
    });

    return Object.values(groups);
  }, [notifications, expandedGroups]);

  const handleGroupAction = useCallback(
    (action: () => void, groupId: string, sound?: string) => {
      const group = groupedNotifications().find(g => g.id === groupId);
      if (sound) {
        playSound(sound);
      } else if (group?.sound?.action) {
        playSound(group.sound.action);
      }
      action();
    },
    [groupedNotifications, playSound]
  );

  const toggleGroup = useCallback(
    (groupId: string) => {
      const group = groupedNotifications().find(g => g.id === groupId);
      if (group) {
        const newExpanded = !expandedGroups[groupId];
        if (newExpanded && group.sound?.expand) {
          playSound(group.sound.expand);
        } else if (!newExpanded && group.sound?.collapse) {
          playSound(group.sound.collapse);
        }
        setExpandedGroups(prev => ({
          ...prev,
          [groupId]: newExpanded,
        }));
      }
    },
    [expandedGroups, groupedNotifications, playSound]
  );

  const handleDragStart = useCallback((groupId: string) => {
    setDraggedGroup(groupId);
  }, []);

  const handleDragEnd = useCallback(
    (groupId: string, info: PanInfo) => {
      setDraggedGroup(null);
      const threshold = 100;
      const group = groupedNotifications().find(g => g.id === groupId);

      if (Math.abs(info.offset.y) > threshold) {
        if (group?.sound?.remove) {
          playSound(group.sound.remove);
        }
        if (group) {
          group.notifications.forEach(notification => {
            removeNotification(notification.id);
          });
        }
      }
    },
    [groupedNotifications, removeNotification, playSound]
  );

  const handleDragReorder = useCallback((groupId: string, newIndex: number) => {
    setGroupOrder(prev => {
      const oldIndex = prev.indexOf(groupId);
      if (oldIndex === -1) return prev;
      const newOrder = [...prev];
      newOrder.splice(oldIndex, 1);
      newOrder.splice(newIndex, 0, groupId);
      return newOrder;
    });
  }, []);

  const handleAction = useCallback(
    (action: () => void, notificationId: string) => {
      action();
      removeNotification(notificationId);
    },
    [removeNotification]
  );

  const handleClose = useCallback(
    (id: string) => {
      removeNotification(id);
    },
    [removeNotification]
  );

  useEffect(() => {
    notifications.forEach(notification => {
      if (notification.duration && !notification.read) {
        const timer = setTimeout(() => {
          removeNotification(notification.id);
        }, notification.duration);

        return () => clearTimeout(timer);
      }
    });
  }, [notifications, removeNotification]);

  // Update group order when notifications change
  useEffect(() => {
    const newOrder = groupedNotifications()
      .map(group => group.id)
      .filter(id => !groupOrder.includes(id));
    setGroupOrder(prev => [...prev, ...newOrder]);
  }, [notifications]);

  const getContainerPosition = (position: NotificationPosition) => {
    switch (position) {
      case 'top-left':
        return { top: 0, left: 0 };
      case 'top-right':
        return { top: 0, right: 0 };
      case 'bottom-left':
        return { bottom: 0, left: 0 };
      case 'bottom-right':
        return { bottom: 0, right: 0 };
      case 'top-center':
        return { top: 0, left: '50%', transform: 'translateX(-50%)' };
      case 'bottom-center':
        return { bottom: 0, left: '50%', transform: 'translateX(-50%)' };
      default:
        return { top: 0, right: 0 };
    }
  };

  const getGroupColor = (type: NotificationType) => {
    switch (type) {
      case 'error':
        return theme.palette.error.main;
      case 'success':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const playNotificationSound = (sound: NotificationSound | string | undefined) => {
    if (!sound) return;

    if (typeof sound === 'string') {
      const audio = new Audio(sound);
      audio.play().catch(console.error);
    } else if (sound.hover) {
      const audio = new Audio(sound.hover);
      audio.play().catch(console.error);
    }
  };

  const ActionButton: React.FC<ActionButtonProps> = ({
    action,
    onAction,
    groupId,
    theme,
    animation,
    sound,
    accessibility,
  }) => {
    const controls = useAnimation();
    const [ref, inView] = useInView({
      threshold: 0.1,
      triggerOnce: false,
    });

    const prefersReducedMotion = useReducedMotion();

    const buttonVariants = {
      hidden: {
        opacity: 0,
        scale: 0.9,
      },
      visible: {
        opacity: 1,
        scale: 1,
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 30,
        },
      },
    };

    const getAccessibilityProps = useCallback(() => {
      switch (accessibility) {
        case 'high-contrast':
          return {
            style: {
              filter: 'contrast(150%)',
              backgroundColor: theme?.accessibility?.highContrast?.background || '#000000',
              color: theme?.accessibility?.highContrast?.text || '#FFFFFF',
              border: theme?.accessibility?.highContrast?.border || '2px solid #FFFFFF',
            },
          };
        case 'reduced-motion':
          return {
            style: {
              animation: 'none',
              transition: 'none',
            },
          };
        case 'screen-reader':
          return {
            'aria-label': action.label,
            'aria-disabled': action.disabled,
          };
        default:
          return {};
      }
    }, [accessibility, theme, action.label, action.disabled]);

    const handleClick = useCallback(() => {
      if (!action.disabled) {
        if (sound?.action?.click) {
          playNotificationSound(sound.action.click);
        }
        onAction(action);
      }
    }, [action, onAction, sound]);

    const handleHover = useCallback(() => {
      if (sound?.action?.hover) {
        playNotificationSound(sound.action.hover);
      }
    }, [sound]);

    return (
      <motion.button
        ref={ref}
        className={`notification-action ${action.type || 'primary'}`}
        style={{
          background: action.theme?.background || theme?.action?.background || '#3B82F6',
          color: action.theme?.text || theme?.action?.text || '#FFFFFF',
          padding: action.theme?.padding || theme?.action?.padding || '0.5rem 1rem',
          borderRadius: action.theme?.borderRadius || theme?.action?.borderRadius || 4,
          border: action.theme?.border || theme?.action?.border || 'none',
          boxShadow:
            action.theme?.shadow || theme?.action?.shadow || '0 1px 2px rgba(0, 0, 0, 0.05)',
          fontSize: action.theme?.fontSize || theme?.action?.fontSize || '0.875rem',
          fontWeight: action.theme?.fontWeight || theme?.action?.fontWeight || 500,
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          cursor: action.disabled ? 'not-allowed' : 'pointer',
          opacity: action.disabled ? 0.5 : 1,
        }}
        variants={prefersReducedMotion ? {} : buttonVariants}
        initial="hidden"
        animate={inView ? 'visible' : 'hidden'}
        whileHover={
          action.disabled || prefersReducedMotion
            ? {}
            : {
                background:
                  action.theme?.hover?.background || theme?.action?.hover?.background || '#2563EB',
                boxShadow:
                  action.theme?.hover?.shadow ||
                  theme?.action?.hover?.shadow ||
                  '0 2px 4px rgba(0, 0, 0, 0.1)',
                transition: {
                  type: 'spring',
                  stiffness: 400,
                  damping: 10,
                },
              }
        }
        whileTap={
          action.disabled || prefersReducedMotion
            ? {}
            : {
                scale: 0.95,
                transition: {
                  type: 'spring',
                  stiffness: 400,
                  damping: 10,
                },
              }
        }
        onClick={handleClick}
        onHoverStart={handleHover}
        disabled={action.disabled}
        {...getAccessibilityProps()}
      >
        {action.icon && (
          <motion.span
            className="notification-action-icon"
            style={{
              color: action.theme?.icon || theme?.action?.icon || '#FFFFFF',
              fontSize: action.theme?.iconSize || theme?.action?.iconSize || '1rem',
            }}
            variants={
              prefersReducedMotion
                ? {}
                : {
                    hover: {
                      scale: 1.1,
                      rotate: 5,
                    },
                    tap: {
                      scale: 0.9,
                      rotate: -5,
                    },
                  }
            }
          >
            {action.icon}
          </motion.span>
        )}
        <span className="notification-action-label">{action.label}</span>
        {action.shortcut && (
          <span
            className="notification-action-shortcut"
            style={{
              color:
                action.theme?.shortcut || theme?.action?.shortcut || 'rgba(255, 255, 255, 0.7)',
              fontSize: action.theme?.shortcutSize || theme?.action?.shortcutSize || '0.75rem',
            }}
          >
            {action.shortcut}
          </span>
        )}
      </motion.button>
    );
  };

  const GroupHeader = memo(
    ({
      group,
      onToggle,
      onAction,
    }: {
      group: NotificationGroup;
      onToggle: (id: string) => void;
      onAction: (action: NotificationAction, groupId: string) => void;
    }) => {
      const theme = useTheme();
      const controls = useAnimation();
      const isDragging = draggedGroup === group.id;

      const handleClick = useCallback(() => {
        controls.start({
          rotate: group.isExpanded ? 0 : 180,
          transition: { type: 'spring', stiffness: 300, damping: 20 },
        });
        onToggle(group.id);
      }, [controls, group.isExpanded, group.id, onToggle]);

      const handleMouseEnter = useCallback(() => {
        if (group.sound?.hover) {
          playSound(group.sound.hover);
        }
        controls.start(group.animation?.header?.hover || { scale: 1.02 });
      }, [controls, group.animation, group.sound]);

      const handleMouseLeave = useCallback(() => {
        controls.start(group.animation?.header?.animate || { scale: 1 });
      }, [controls, group.animation]);

      return (
        <motion.div
          initial={group.animation?.header?.initial || { opacity: 0, y: -20 }}
          animate={controls}
          exit={group.animation?.header?.exit || { opacity: 0, y: 20 }}
          whileHover={group.animation?.header?.hover || { scale: 1.02 }}
          whileTap={group.animation?.header?.tap || { scale: 0.98 }}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={handleClick}
          style={{
            position: 'relative',
            zIndex: isDragging ? 1000 : 1,
          }}
        >
          <Box
            role="button"
            tabIndex={0}
            aria-expanded={group.isExpanded}
            aria-label={`${group.notifications.length} notifications in ${group.notifications[0].title} group`}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              p: 1,
              borderBottom: `2px solid ${group.theme?.header?.border || group.theme?.border || getGroupColor(group.type)}`,
              cursor: 'pointer',
              backgroundColor:
                group.theme?.header?.background ||
                group.theme?.background ||
                theme.palette.background.paper,
              color: group.theme?.header?.text || group.theme?.text || theme.palette.text.primary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.primary.main}`,
                outlineOffset: 2,
              },
              transform: isDragging ? 'scale(1.02)' : 'scale(1)',
              transition: 'transform 0.2s ease',
              boxShadow: isDragging
                ? group.theme?.header?.shadow || group.theme?.shadow || theme.shadows[8]
                : group.theme?.header?.shadow || group.theme?.shadow || theme.shadows[3],
            }}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleClick();
              }
            }}
          >
            <motion.div
              initial={group.animation?.icon?.initial || { scale: 0 }}
              animate={group.animation?.icon?.animate || { scale: 1 }}
              whileHover={group.animation?.icon?.hover || { scale: 1.1 }}
              whileTap={group.animation?.icon?.tap || { scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 300, damping: 20 }}
              style={{
                color: group.theme?.header?.icon || group.theme?.icon || getGroupColor(group.type),
              }}
            >
              <NotificationIcon
                icon={group.icon?.component}
                type={group.type}
                theme={group.icon?.theme}
                animation={group.icon?.animation}
                sound={group.sound}
                accessibility={group.icon?.accessibility}
              />
            </motion.div>
            <Box sx={{ flex: 1, typography: 'subtitle1' }}>{group.notifications[0].title}</Box>
            {group.actions && group.actions.length > 0 && (
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {group.actions.map((action, index) => (
                  <ActionButton
                    key={index}
                    action={action}
                    onAction={onAction}
                    groupId={group.id}
                    theme={group.theme}
                    animation={group.animation}
                    sound={group.sound}
                  />
                ))}
              </Box>
            )}
            <motion.div animate={controls} initial={{ rotate: 0 }}>
              <IconButton
                size="small"
                aria-label={group.isExpanded ? 'Collapse group' : 'Expand group'}
                sx={{
                  color: group.theme?.header?.icon || group.theme?.icon || 'inherit',
                  '&:hover': {
                    color: theme.palette.primary.main,
                  },
                }}
              >
                <ExpandMoreIcon />
              </IconButton>
            </motion.div>
          </Box>
        </motion.div>
      );
    }
  );

  const GroupContent = memo(({ group }: { group: NotificationGroup }) => {
    const controls = useAnimation();

    useEffect(() => {
      if (group.isExpanded) {
        controls.start(
          group.content?.animation?.animate || {
            opacity: 1,
            height: 'auto',
            transition: { type: 'spring', stiffness: 300, damping: 20 },
          }
        );
      } else {
        controls.start(
          group.content?.animation?.exit || {
            opacity: 0,
            height: 0,
            transition: { type: 'spring', stiffness: 300, damping: 20 },
          }
        );
      }
    }, [group.isExpanded, controls, group.content?.animation]);

    return (
      <motion.div
        initial={group.content?.animation?.initial || { opacity: 0, height: 0 }}
        animate={controls}
        exit={group.content?.animation?.exit || { opacity: 0, height: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        style={{
          overflow: 'hidden',
          backgroundColor:
            group.content?.theme?.background ||
            group.theme?.content?.background ||
            theme.palette.background.paper,
          color:
            group.content?.theme?.text || group.theme?.content?.text || theme.palette.text.primary,
          border:
            group.content?.theme?.border || group.theme?.content?.border
              ? `1px solid ${group.content?.theme?.border || group.theme?.content?.border}`
              : 'none',
          boxShadow:
            group.content?.theme?.shadow || group.theme?.content?.shadow || theme.shadows[1],
          borderRadius: theme.shape.borderRadius,
        }}
      >
        <AnimatePresence>
          {group.isExpanded &&
            group.notifications.map((notification, index) => (
              <NotificationContent
                key={notification.id}
                notification={notification}
                theme={group.content?.theme}
                animation={group.content?.animation}
                sound={group.content?.sound}
              />
            ))}
        </AnimatePresence>
      </motion.div>
    );
  });

  const GroupContainer = memo(
    ({
      group,
      onToggle,
      onAction,
      onDragStart,
      onDragEnd,
      onDragReorder,
    }: {
      group: NotificationGroup;
      onToggle: (id: string) => void;
      onAction: (action: NotificationAction, groupId: string) => void;
      onDragStart: (id: string) => void;
      onDragEnd: (id: string, info: PanInfo) => void;
      onDragReorder: (id: string, info: PanInfo) => void;
    }) => {
      const controls = useAnimation();
      const isDragging = draggedGroup === group.id;

      const handleDragStart = useCallback(() => {
        if (group.sound?.drag) {
          playSound(group.sound.drag);
        }
        onDragStart(group.id);
        controls.start(group.animation?.drag?.start || { scale: 1.02, zIndex: 1000 });
      }, [controls, group, onDragStart]);

      const handleDragEnd = useCallback(
        (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
          if (group.sound?.drag) {
            playSound(group.sound.drag);
          }
          onDragEnd(group.id, info);
          controls.start(group.animation?.drag?.end || { scale: 1, zIndex: 1 });
        },
        [controls, group, onDragEnd]
      );

      const handleDragReorder = useCallback(
        (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
          if (group.sound?.reorder) {
            playSound(group.sound.reorder);
          }
          onDragReorder(group.id, info);
        },
        [group, onDragReorder]
      );

      return (
        <motion.div
          initial={group.animation?.initial || { opacity: 0, y: 20 }}
          animate={controls}
          exit={group.animation?.exit || { opacity: 0, y: -20 }}
          whileHover={group.animation?.hover || { scale: 1.01 }}
          whileTap={group.animation?.tap || { scale: 0.99 }}
          drag="y"
          dragConstraints={{ top: 0, bottom: 0 }}
          dragElastic={0.1}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDrag={handleDragReorder}
          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          style={{
            position: 'relative',
            zIndex: isDragging ? 1000 : 1,
            backgroundColor: group.theme?.background || 'transparent',
            color: group.theme?.text || 'inherit',
            border: group.theme?.border ? `1px solid ${group.theme.border}` : 'none',
            borderRadius: group.theme?.borderRadius || 8,
            boxShadow: isDragging
              ? group.theme?.shadow || '0 4px 20px rgba(0, 0, 0, 0.15)'
              : group.theme?.shadow || '0 2px 8px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
          }}
        >
          <GroupHeader group={group} onToggle={onToggle} onAction={onAction} />
          <GroupContent group={group} />
        </motion.div>
      );
    }
  );

  return (
    <Box
      sx={{
        position: 'fixed',
        zIndex: theme.zIndex.snackbar,
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        p: 1,
        maxWidth: '100%',
        maxHeight: '100vh',
        overflow: 'auto',
        pointerEvents: 'none',
        '& > *': {
          pointerEvents: 'auto',
        },
      }}
    >
      <AnimatePresence>
        {groupedNotifications().map(group => (
          <motion.div
            key={group.id}
            initial={group.animation?.initial || { opacity: 0, y: -20 }}
            animate={group.animation?.animate || { opacity: 1, y: 0 }}
            exit={group.animation?.exit || { opacity: 0, y: 20 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            style={{
              width: '100%',
              maxWidth: 400,
            }}
          >
            <GroupContainer
              group={group}
              onToggle={toggleGroup}
              onAction={handleGroupAction}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              onDragReorder={handleDragReorder}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </Box>
  );
};

export default NotificationList;
