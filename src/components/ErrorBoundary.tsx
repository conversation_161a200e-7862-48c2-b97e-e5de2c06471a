import { <PERSON>, Button, Paper, Typography, Collapse, Alert } from '@mui/material';
import { styled } from '@mui/material/styles';
import React, { Component, ErrorInfo, ReactNode, createRef } from 'react';
import { WithTranslation, withTranslation } from 'react-i18next';

import { Icon } from '@shared/components/Icon';
import { errorManager } from '../core/ErrorManager';

import styles from './ErrorBoundary.module.css';

const ErrorContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
  maxWidth: 600,
  marginLeft: 'auto',
  marginRight: 'auto',
}));

const ErrorDetails = styled(Box)(({ theme }) => ({
  width: '100%',
  marginTop: theme.spacing(2),
  padding: theme.spacing(2),
  backgroundColor: theme.palette.grey[100],
  borderRadius: theme.shape.borderRadius,
  fontFamily: 'monospace',
  fontSize: '0.875rem',
  overflow: 'auto',
  maxHeight: 300,
}));

interface Props extends Partial<WithTranslation> {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  retryCount: number;
}

class ErrorBoundaryBase extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    showDetails: false,
    retryCount: 0,
  };

  private reloadButtonRef = createRef<HTMLButtonElement>();
  private retryButtonRef = createRef<HTMLButtonElement>();

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      showDetails: false,
      retryCount: 0,
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);

    // Report error to error manager
    errorManager.handleError(error, {
      component: 'ErrorBoundary',
      level: 'error',
      errorInfo,
      componentStack: errorInfo.componentStack,
    });

    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentDidUpdate(_: Props, prevState: State) {
    // Focus appropriate button when error occurs
    if (this.state.hasError && !prevState.hasError) {
      if (this.props.level === 'page' && this.reloadButtonRef.current) {
        this.reloadButtonRef.current.focus();
      } else if (this.retryButtonRef.current) {
        this.retryButtonRef.current.focus();
      }
    }
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      retryCount: prevState.retryCount + 1,
    }));
  };

  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails,
    }));
  };

  public render() {
    if (this.state.hasError) {
      return (
        <Box className={styles.container}>
          <Icon name="error" className={styles.icon} />
          <Typography variant="h5" className={styles.title}>
            Oops! Something went wrong
          </Typography>
          <Typography variant="body1" className={styles.message} align="center" color="textSecondary">
            {this.state.error?.message || (this.props.t ? this.props.t('error.boundary.message') : 'An unexpected error occurred')}
          </Typography>

          {this.state.retryCount > 0 && (
            <Alert severity="warning" sx={{ width: '100%' }}>
              {this.props.t ? this.props.t('error.boundary.retryWarning', { count: this.state.retryCount }) :
                `This error has occurred ${this.state.retryCount} time(s). Consider refreshing the page.`}
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
            {this.props.level !== 'page' && (
              <Button
                ref={this.retryButtonRef}
                variant="contained"
                color="primary"
                onClick={this.handleRetry}
                className={styles.button}
              >
                {this.props.t ? this.props.t('error.boundary.retry') : 'Try Again'}
              </Button>
            )}

            <Button
              ref={this.reloadButtonRef}
              variant={this.props.level === 'page' ? "contained" : "outlined"}
              color={this.props.level === 'page' ? "primary" : "secondary"}
              onClick={this.handleReload}
              startIcon={<Icon name="refresh" />}
              className={styles.button}
            >
              {this.props.t ? this.props.t('error.boundary.reload') : 'Reload Page'}
            </Button>

            {(this.props.showDetails !== false) && (
              <Button
                variant="text"
                onClick={this.toggleDetails}
                size="small"
              >
                {this.state.showDetails ?
                  (this.props.t ? this.props.t('error.boundary.hideDetails') : 'Hide Details') :
                  (this.props.t ? this.props.t('error.boundary.showDetails') : 'Show Details')
                }
              </Button>
            )}
          </Box>

          <Collapse in={this.state.showDetails} sx={{ width: '100%' }}>
            <ErrorDetails>
              <Typography variant="subtitle2" gutterBottom>
                {this.props.t ? this.props.t('error.boundary.errorDetails') : 'Error Details:'}
              </Typography>
              <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                {this.state.error?.stack}
              </Typography>

              {this.state.errorInfo?.componentStack && (
                <>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    {this.props.t ? this.props.t('error.boundary.componentStack') : 'Component Stack:'}
                  </Typography>
                  <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                    {this.state.errorInfo.componentStack}
                  </Typography>
                </>
              )}
            </ErrorDetails>
          </Collapse>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

// Create both translated and non-translated versions
const ErrorBoundaryWithTranslation = withTranslation()(ErrorBoundaryBase);

// Main export that can work with or without translation
const ErrorBoundary: React.FC<Props> = (props) => {
  // Try to use translation if available, otherwise use base component
  try {
    return <ErrorBoundaryWithTranslation {...props} />;
  } catch {
    return <ErrorBoundaryBase {...props} />;
  }
};

export default ErrorBoundary;
export { ErrorBoundaryBase };
