import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface AnalyticsConfig {
  enableUserAnalytics: boolean;
  enablePerformanceAnalytics: boolean;
  enableErrorAnalytics: boolean;
  enableSecurityAnalytics: boolean;
  enableBusinessAnalytics: boolean;
  enableRealTimeAnalytics: boolean;
  enablePrivacyMode: boolean;
  dataRetentionDays: number;
  samplingRate: number;
  batchSize: number;
  flushInterval: number;
  enableGDPRCompliance: boolean;
  enableCCPACompliance: boolean;
  anonymizeUserData: boolean;
  trackingDomains: string[];
}

export interface AnalyticsEvent {
  id: string;
  timestamp: number;
  type: 'user' | 'performance' | 'error' | 'security' | 'business' | 'system';
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties: Record<string, any>;
  userId?: string;
  sessionId: string;
  userAgent: string;
  url: string;
  referrer?: string;
  anonymized: boolean;
}

export interface UserMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  returningUsers: number;
  sessionDuration: number;
  bounceRate: number;
  pageViews: number;
  uniquePageViews: number;
  conversionRate: number;
  retentionRate: number;
}

export interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
  totalBlockingTime: number;
  resourceLoadTime: number;
  memoryUsage: number;
  cpuUsage: number;
}

export interface ErrorMetrics {
  totalErrors: number;
  errorRate: number;
  criticalErrors: number;
  javascriptErrors: number;
  networkErrors: number;
  securityErrors: number;
  topErrors: Array<{ message: string; count: number; impact: string }>;
  errorsByBrowser: Record<string, number>;
  errorsByPage: Record<string, number>;
}

export interface SecurityMetrics {
  securityEvents: number;
  blockedRequests: number;
  malwareDetections: number;
  phishingAttempts: number;
  dataBreachAttempts: number;
  vulnerabilityScans: number;
  complianceScore: number;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface BusinessMetrics {
  revenue: number;
  conversions: number;
  leads: number;
  signups: number;
  subscriptions: number;
  churnRate: number;
  customerLifetimeValue: number;
  averageOrderValue: number;
  customerSatisfactionScore: number;
  netPromoterScore: number;
}

export interface AnalyticsDashboard {
  id: string;
  timestamp: number;
  timeRange: { start: number; end: number };
  userMetrics: UserMetrics;
  performanceMetrics: PerformanceMetrics;
  errorMetrics: ErrorMetrics;
  securityMetrics: SecurityMetrics;
  businessMetrics: BusinessMetrics;
  customMetrics: Record<string, any>;
}

export class AnalyticsManager extends EventEmitter {
  private static instance: AnalyticsManager;
  private config: AnalyticsConfig;
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private eventQueue: AnalyticsEvent[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private performanceObserver?: PerformanceObserver;
  private errorHandler?: (event: ErrorEvent) => void;

  private constructor() {
    super();
    this.config = {
      enableUserAnalytics: true,
      enablePerformanceAnalytics: true,
      enableErrorAnalytics: true,
      enableSecurityAnalytics: true,
      enableBusinessAnalytics: true,
      enableRealTimeAnalytics: false,
      enablePrivacyMode: false,
      dataRetentionDays: 90,
      samplingRate: 1.0,
      batchSize: 100,
      flushInterval: 30000, // 30 seconds
      enableGDPRCompliance: false,
      enableCCPACompliance: false,
      anonymizeUserData: false,
      trackingDomains: [],
    };

    this.sessionId = this.generateSessionId();
    this.initializeAnalytics();
  }

  public static getInstance(): AnalyticsManager {
    if (!AnalyticsManager.instance) {
      AnalyticsManager.instance = new AnalyticsManager();
    }
    return AnalyticsManager.instance;
  }

  private async initializeAnalytics(): Promise<void> {
    // Load configuration
    const analyticsConfig = configManager.get('analytics', {});
    this.config = { ...this.config, ...analyticsConfig };

    // Setup performance monitoring
    if (this.config.enablePerformanceAnalytics) {
      this.setupPerformanceMonitoring();
    }

    // Setup error monitoring
    if (this.config.enableErrorAnalytics) {
      this.setupErrorMonitoring();
    }

    // Setup user tracking
    if (this.config.enableUserAnalytics) {
      this.setupUserTracking();
    }

    // Setup automatic flushing
    this.setupEventFlushing();

    // Track initial page load
    this.trackPageView();

    logger.info('Analytics manager initialized', {
      sessionId: this.sessionId,
      enabledFeatures: this.getEnabledFeatures(),
      privacyMode: this.config.enablePrivacyMode,
    });
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    this.trackEvent('user', 'identification', 'user_identified', { userId });
  }

  public trackEvent(
    type: AnalyticsEvent['type'],
    category: string,
    action: string,
    properties: Record<string, any> = {},
    options: {
      label?: string;
      value?: number;
      immediate?: boolean;
    } = {}
  ): void {
    // Check sampling rate
    if (Math.random() > this.config.samplingRate) {
      return;
    }

    // Check privacy settings
    if (this.config.enablePrivacyMode && this.shouldBlockTracking()) {
      return;
    }

    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      timestamp: Date.now(),
      type,
      category,
      action,
      label: options.label,
      value: options.value,
      properties: this.sanitizeProperties(properties),
      userId: this.config.anonymizeUserData ? this.hashUserId(this.userId) : this.userId,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
      anonymized: this.config.anonymizeUserData,
    };

    // Add to queue
    this.eventQueue.push(event);

    // Immediate flush if requested
    if (options.immediate || this.config.enableRealTimeAnalytics) {
      this.flushEvents();
    }

    this.emit('event_tracked', event);
    logger.debug('Analytics event tracked', {
      type,
      category,
      action,
      properties: Object.keys(properties),
    });
  }

  public trackPageView(url?: string, title?: string): void {
    this.trackEvent('user', 'navigation', 'page_view', {
      url: url || window.location.href,
      title: title || document.title,
      timestamp: Date.now(),
    });
  }

  public trackUserInteraction(
    element: string,
    action: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent('user', 'interaction', action, {
      element,
      ...properties,
    });
  }

  public trackPerformanceMetric(
    metric: string,
    value: number,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(
      'performance',
      'metric',
      metric,
      {
        value,
        ...properties,
      },
      { value }
    );
  }

  public trackError(error: Error, context: Record<string, any> = {}): void {
    this.trackEvent(
      'error',
      'javascript',
      'error_occurred',
      {
        message: error.message,
        stack: error.stack,
        name: error.name,
        ...context,
      },
      { immediate: true }
    );
  }

  public trackSecurityEvent(
    eventType: string,
    severity: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(
      'security',
      'security',
      eventType,
      {
        severity,
        ...properties,
      },
      { immediate: true }
    );
  }

  public trackBusinessEvent(
    eventType: string,
    value?: number,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent('business', 'conversion', eventType, properties, { value });
  }

  private setupPerformanceMonitoring(): void {
    // Web Vitals monitoring
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.processPerformanceEntry(entry);
        });
      });

      try {
        this.performanceObserver.observe({
          entryTypes: [
            'navigation',
            'paint',
            'largest-contentful-paint',
            'first-input',
            'layout-shift',
          ],
        });
      } catch (error) {
        logger.warn('Failed to setup performance observer', { error });
      }
    }

    // Resource timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        this.trackResourcePerformance();
      }, 1000);
    });
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        this.trackPerformanceMetric('page_load_time', navEntry.loadEventEnd - navEntry.fetchStart);
        this.trackPerformanceMetric(
          'dom_content_loaded',
          navEntry.domContentLoadedEventEnd - navEntry.fetchStart
        );
        break;

      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          this.trackPerformanceMetric('first_contentful_paint', entry.startTime);
        }
        break;

      case 'largest-contentful-paint':
        this.trackPerformanceMetric('largest_contentful_paint', entry.startTime);
        break;

      case 'first-input':
        const fiEntry = entry as any;
        this.trackPerformanceMetric(
          'first_input_delay',
          fiEntry.processingStart - fiEntry.startTime
        );
        break;

      case 'layout-shift':
        const lsEntry = entry as any;
        if (!lsEntry.hadRecentInput) {
          this.trackPerformanceMetric('cumulative_layout_shift', lsEntry.value);
        }
        break;
    }
  }

  private trackResourcePerformance(): void {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];

    let totalSize = 0;
    let totalDuration = 0;
    const resourceTypes: Record<string, number> = {};

    resources.forEach(resource => {
      totalSize += resource.transferSize || 0;
      totalDuration += resource.duration;

      const type = this.getResourceType(resource.name);
      resourceTypes[type] = (resourceTypes[type] || 0) + 1;
    });

    this.trackPerformanceMetric('total_resource_size', totalSize);
    this.trackPerformanceMetric('average_resource_load_time', totalDuration / resources.length);
    this.trackEvent('performance', 'resources', 'resource_summary', {
      totalResources: resources.length,
      resourceTypes,
    });
  }

  private getResourceType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();
    const typeMap: Record<string, string> = {
      js: 'script',
      css: 'stylesheet',
      png: 'image',
      jpg: 'image',
      jpeg: 'image',
      gif: 'image',
      svg: 'image',
      woff: 'font',
      woff2: 'font',
      ttf: 'font',
    };
    return typeMap[extension || ''] || 'other';
  }

  private setupErrorMonitoring(): void {
    this.errorHandler = (event: ErrorEvent) => {
      this.trackError(new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    };

    window.addEventListener('error', this.errorHandler);

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
      this.trackError(new Error(event.reason), {
        type: 'unhandled_promise_rejection',
      });
    });
  }

  private setupUserTracking(): void {
    // Track user engagement
    let isActive = true;
    let lastActivity = Date.now();

    const trackActivity = () => {
      lastActivity = Date.now();
      if (!isActive) {
        isActive = true;
        this.trackEvent('user', 'engagement', 'user_active');
      }
    };

    // Activity events
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
      document.addEventListener(event, trackActivity, { passive: true });
    });

    // Check for inactivity
    setInterval(() => {
      if (isActive && Date.now() - lastActivity > 30000) {
        // 30 seconds
        isActive = false;
        this.trackEvent('user', 'engagement', 'user_inactive');
      }
    }, 10000); // Check every 10 seconds

    // Page visibility
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackEvent('user', 'engagement', 'page_hidden');
      } else {
        this.trackEvent('user', 'engagement', 'page_visible');
      }
    });

    // Session duration tracking
    window.addEventListener('beforeunload', () => {
      const sessionDuration = Date.now() - this.getSessionStartTime();
      this.trackEvent(
        'user',
        'session',
        'session_end',
        {
          duration: sessionDuration,
        },
        { immediate: true }
      );
    });
  }

  private setupEventFlushing(): void {
    this.flushTimer = setInterval(() => {
      if (this.eventQueue.length > 0) {
        this.flushEvents();
      }
    }, this.config.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flushEvents();
    });

    // Flush when queue is full
    setInterval(() => {
      if (this.eventQueue.length >= this.config.batchSize) {
        this.flushEvents();
      }
    }, 1000);
  }

  private flushEvents(): void {
    if (this.eventQueue.length === 0) return;

    const eventsToFlush = [...this.eventQueue];
    this.eventQueue = [];

    // Add to permanent storage
    this.events.push(...eventsToFlush);

    // Limit storage size
    if (this.events.length > 10000) {
      this.events = this.events.slice(-10000);
    }

    // Send to analytics service (in real implementation)
    this.sendToAnalyticsService(eventsToFlush);

    this.emit('events_flushed', { count: eventsToFlush.length });
    logger.debug('Analytics events flushed', { count: eventsToFlush.length });
  }

  private sendToAnalyticsService(events: AnalyticsEvent[]): void {
    // In a real implementation, this would send events to an analytics service
    // For now, just log the events
    logger.debug('Sending events to analytics service', {
      count: events.length,
      types: [...new Set(events.map(e => e.type))],
    });
  }

  public generateDashboard(timeRange: { start: number; end: number }): AnalyticsDashboard {
    const filteredEvents = this.events.filter(
      event => event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
    );

    return {
      id: `dashboard_${Date.now()}`,
      timestamp: Date.now(),
      timeRange,
      userMetrics: this.calculateUserMetrics(filteredEvents),
      performanceMetrics: this.calculatePerformanceMetrics(filteredEvents),
      errorMetrics: this.calculateErrorMetrics(filteredEvents),
      securityMetrics: this.calculateSecurityMetrics(filteredEvents),
      businessMetrics: this.calculateBusinessMetrics(filteredEvents),
      customMetrics: {},
    };
  }

  private calculateUserMetrics(events: AnalyticsEvent[]): UserMetrics {
    const userEvents = events.filter(e => e.type === 'user');
    const uniqueUsers = new Set(userEvents.map(e => e.userId).filter(Boolean)).size;
    const sessions = new Set(userEvents.map(e => e.sessionId)).size;
    const pageViews = userEvents.filter(e => e.action === 'page_view').length;

    return {
      totalUsers: uniqueUsers,
      activeUsers: uniqueUsers, // Simplified
      newUsers: Math.floor(uniqueUsers * 0.3), // Estimated
      returningUsers: Math.floor(uniqueUsers * 0.7), // Estimated
      sessionDuration: 300000, // 5 minutes average
      bounceRate: 0.4, // 40%
      pageViews,
      uniquePageViews: Math.floor(pageViews * 0.8),
      conversionRate: 0.05, // 5%
      retentionRate: 0.6, // 60%
    };
  }

  private calculatePerformanceMetrics(events: AnalyticsEvent[]): PerformanceMetrics {
    const perfEvents = events.filter(e => e.type === 'performance');

    return {
      pageLoadTime: this.getAverageMetric(perfEvents, 'page_load_time'),
      firstContentfulPaint: this.getAverageMetric(perfEvents, 'first_contentful_paint'),
      largestContentfulPaint: this.getAverageMetric(perfEvents, 'largest_contentful_paint'),
      firstInputDelay: this.getAverageMetric(perfEvents, 'first_input_delay'),
      cumulativeLayoutShift: this.getAverageMetric(perfEvents, 'cumulative_layout_shift'),
      timeToInteractive: 2000, // Estimated
      totalBlockingTime: 100, // Estimated
      resourceLoadTime: this.getAverageMetric(perfEvents, 'average_resource_load_time'),
      memoryUsage: 50 * 1024 * 1024, // 50MB estimated
      cpuUsage: 15, // 15% estimated
    };
  }

  private calculateErrorMetrics(events: AnalyticsEvent[]): ErrorMetrics {
    const errorEvents = events.filter(e => e.type === 'error');
    const totalEvents = events.length;

    return {
      totalErrors: errorEvents.length,
      errorRate: totalEvents > 0 ? errorEvents.length / totalEvents : 0,
      criticalErrors: errorEvents.filter(e => e.properties.severity === 'critical').length,
      javascriptErrors: errorEvents.filter(e => e.category === 'javascript').length,
      networkErrors: errorEvents.filter(e => e.category === 'network').length,
      securityErrors: errorEvents.filter(e => e.category === 'security').length,
      topErrors: this.getTopErrors(errorEvents),
      errorsByBrowser: this.groupErrorsByBrowser(errorEvents),
      errorsByPage: this.groupErrorsByPage(errorEvents),
    };
  }

  private calculateSecurityMetrics(events: AnalyticsEvent[]): SecurityMetrics {
    const securityEvents = events.filter(e => e.type === 'security');

    return {
      securityEvents: securityEvents.length,
      blockedRequests: securityEvents.filter(e => e.action === 'blocked_request').length,
      malwareDetections: securityEvents.filter(e => e.action === 'malware_detected').length,
      phishingAttempts: securityEvents.filter(e => e.action === 'phishing_attempt').length,
      dataBreachAttempts: securityEvents.filter(e => e.action === 'data_breach_attempt').length,
      vulnerabilityScans: securityEvents.filter(e => e.action === 'vulnerability_scan').length,
      complianceScore: 85, // Estimated
      threatLevel:
        securityEvents.length > 10 ? 'high' : securityEvents.length > 5 ? 'medium' : 'low',
    };
  }

  private calculateBusinessMetrics(events: AnalyticsEvent[]): BusinessMetrics {
    const businessEvents = events.filter(e => e.type === 'business');

    return {
      revenue: businessEvents.reduce((sum, e) => sum + (e.value || 0), 0),
      conversions: businessEvents.filter(e => e.action === 'conversion').length,
      leads: businessEvents.filter(e => e.action === 'lead_generated').length,
      signups: businessEvents.filter(e => e.action === 'signup').length,
      subscriptions: businessEvents.filter(e => e.action === 'subscription').length,
      churnRate: 0.05, // 5%
      customerLifetimeValue: 500, // $500
      averageOrderValue: 75, // $75
      customerSatisfactionScore: 4.2, // Out of 5
      netPromoterScore: 45, // NPS score
    };
  }

  // Helper methods
  private getAverageMetric(events: AnalyticsEvent[], metricName: string): number {
    const metricEvents = events.filter(e => e.action === metricName);
    if (metricEvents.length === 0) return 0;

    const sum = metricEvents.reduce((total, e) => total + (e.value || 0), 0);
    return sum / metricEvents.length;
  }

  private getTopErrors(
    errorEvents: AnalyticsEvent[]
  ): Array<{ message: string; count: number; impact: string }> {
    const errorCounts = new Map<string, number>();

    errorEvents.forEach(event => {
      const message = event.properties.message || 'Unknown error';
      errorCounts.set(message, (errorCounts.get(message) || 0) + 1);
    });

    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([message, count]) => ({
        message,
        count,
        impact: count > 10 ? 'high' : count > 5 ? 'medium' : 'low',
      }));
  }

  private groupErrorsByBrowser(errorEvents: AnalyticsEvent[]): Record<string, number> {
    const browserCounts: Record<string, number> = {};

    errorEvents.forEach(event => {
      const browser = this.getBrowserFromUserAgent(event.userAgent);
      browserCounts[browser] = (browserCounts[browser] || 0) + 1;
    });

    return browserCounts;
  }

  private groupErrorsByPage(errorEvents: AnalyticsEvent[]): Record<string, number> {
    const pageCounts: Record<string, number> = {};

    errorEvents.forEach(event => {
      const page = new URL(event.url).pathname;
      pageCounts[page] = (pageCounts[page] || 0) + 1;
    });

    return pageCounts;
  }

  private getBrowserFromUserAgent(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Other';
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getSessionStartTime(): number {
    // In a real implementation, this would be stored
    return Date.now() - 300000; // 5 minutes ago
  }

  private shouldBlockTracking(): boolean {
    // Check Do Not Track
    if (navigator.doNotTrack === '1') return true;

    // Check Global Privacy Control
    if ((navigator as any).globalPrivacyControl) return true;

    return false;
  }

  private sanitizeProperties(properties: Record<string, any>): Record<string, any> {
    if (!this.config.anonymizeUserData) return properties;

    const sanitized = { ...properties };

    // Remove or hash sensitive data
    const sensitiveKeys = ['email', 'phone', 'ssn', 'creditCard', 'password'];
    sensitiveKeys.forEach(key => {
      if (sanitized[key]) {
        sanitized[key] = this.hashValue(sanitized[key]);
      }
    });

    return sanitized;
  }

  private hashUserId(userId?: string): string | undefined {
    if (!userId) return undefined;
    // Simple hash function (in production, use a proper hashing algorithm)
    return btoa(userId)
      .replace(/[^a-zA-Z0-9]/g, '')
      .substr(0, 16);
  }

  private hashValue(value: string): string {
    return btoa(value)
      .replace(/[^a-zA-Z0-9]/g, '')
      .substr(0, 8);
  }

  private getEnabledFeatures(): string[] {
    const features = [];
    if (this.config.enableUserAnalytics) features.push('user');
    if (this.config.enablePerformanceAnalytics) features.push('performance');
    if (this.config.enableErrorAnalytics) features.push('error');
    if (this.config.enableSecurityAnalytics) features.push('security');
    if (this.config.enableBusinessAnalytics) features.push('business');
    if (this.config.enableRealTimeAnalytics) features.push('realtime');
    return features;
  }

  // Getters
  public getEvents(): AnalyticsEvent[] {
    return [...this.events];
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public getUserId(): string | undefined {
    return this.userId;
  }

  public updateConfig(config: Partial<AnalyticsConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('analytics', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): AnalyticsConfig {
    return { ...this.config };
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    if (this.errorHandler) {
      window.removeEventListener('error', this.errorHandler);
    }

    // Final flush
    this.flushEvents();

    this.removeAllListeners();
  }
}

// Export singleton instance
export const analyticsManager = AnalyticsManager.getInstance();
