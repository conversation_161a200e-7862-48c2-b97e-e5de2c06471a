import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface DeploymentConfig {
  environment: 'development' | 'staging' | 'production';
  version: string;
  buildNumber: string;
  deploymentStrategy: 'blue-green' | 'rolling' | 'canary' | 'recreate';
  enableHealthChecks: boolean;
  enableRollback: boolean;
  enableAutoScaling: boolean;
  enableLoadBalancing: boolean;
  enableCDN: boolean;
  enableSSL: boolean;
  enableCompression: boolean;
  enableCaching: boolean;
  maxInstances: number;
  minInstances: number;
  healthCheckInterval: number;
  deploymentTimeout: number;
  rollbackTimeout: number;
  canaryPercentage: number;
}

export interface DeploymentTarget {
  id: string;
  name: string;
  type: 'server' | 'container' | 'serverless' | 'edge';
  url: string;
  region: string;
  status: 'active' | 'inactive' | 'deploying' | 'error';
  version: string;
  lastDeployed: number;
  healthStatus: 'healthy' | 'unhealthy' | 'unknown';
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    requests: number;
    errors: number;
  };
}

export interface DeploymentPlan {
  id: string;
  version: string;
  strategy: DeploymentConfig['deploymentStrategy'];
  targets: string[];
  steps: DeploymentStep[];
  estimatedDuration: number;
  rollbackPlan: DeploymentStep[];
  createdAt: number;
  createdBy: string;
}

export interface DeploymentStep {
  id: string;
  name: string;
  type: 'build' | 'test' | 'deploy' | 'verify' | 'rollback';
  command?: string;
  timeout: number;
  retries: number;
  dependencies: string[];
  conditions: string[];
  rollbackStep?: string;
}

export interface DeploymentExecution {
  id: string;
  planId: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'rolled_back';
  startTime: number;
  endTime?: number;
  duration?: number;
  currentStep?: string;
  completedSteps: string[];
  failedSteps: string[];
  logs: DeploymentLog[];
  metrics: DeploymentMetrics;
}

export interface DeploymentLog {
  timestamp: number;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  step?: string;
  target?: string;
  metadata?: Record<string, any>;
}

export interface DeploymentMetrics {
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  successRate: number;
  averageStepDuration: number;
  totalDuration: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
}

export interface HealthCheck {
  id: string;
  name: string;
  type: 'http' | 'tcp' | 'command' | 'custom';
  endpoint?: string;
  command?: string;
  timeout: number;
  interval: number;
  retries: number;
  successThreshold: number;
  failureThreshold: number;
  enabled: boolean;
}

export class DeploymentManager extends EventEmitter {
  private static instance: DeploymentManager;
  private config: DeploymentConfig;
  private targets: Map<string, DeploymentTarget> = new Map();
  private plans: Map<string, DeploymentPlan> = new Map();
  private executions: Map<string, DeploymentExecution> = new Map();
  private healthChecks: Map<string, HealthCheck> = new Map();
  private healthCheckIntervals: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {
    super();
    this.config = {
      environment: 'development',
      version: '1.0.0',
      buildNumber: '1',
      deploymentStrategy: 'rolling',
      enableHealthChecks: true,
      enableRollback: true,
      enableAutoScaling: false,
      enableLoadBalancing: false,
      enableCDN: false,
      enableSSL: true,
      enableCompression: true,
      enableCaching: true,
      maxInstances: 10,
      minInstances: 1,
      healthCheckInterval: 30000, // 30 seconds
      deploymentTimeout: 600000, // 10 minutes
      rollbackTimeout: 300000, // 5 minutes
      canaryPercentage: 10,
    };

    this.initializeDeploymentManager();
  }

  public static getInstance(): DeploymentManager {
    if (!DeploymentManager.instance) {
      DeploymentManager.instance = new DeploymentManager();
    }
    return DeploymentManager.instance;
  }

  private async initializeDeploymentManager(): Promise<void> {
    // Load configuration
    const deploymentConfig = configManager.get('deployment', {});
    this.config = { ...this.config, ...deploymentConfig };

    // Load deployment targets
    await this.loadDeploymentTargets();

    // Setup health checks
    if (this.config.enableHealthChecks) {
      this.setupHealthChecks();
    }

    // Load existing plans and executions
    await this.loadDeploymentHistory();

    logger.info('Deployment manager initialized', {
      environment: this.config.environment,
      version: this.config.version,
      strategy: this.config.deploymentStrategy,
      targets: this.targets.size,
    });
  }

  public async createDeploymentPlan(planData: {
    version: string;
    strategy?: DeploymentConfig['deploymentStrategy'];
    targets: string[];
    customSteps?: Partial<DeploymentStep>[];
  }): Promise<DeploymentPlan> {
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const steps = this.generateDeploymentSteps(
      planData.strategy || this.config.deploymentStrategy,
      planData.targets,
      planData.customSteps
    );

    const rollbackSteps = this.generateRollbackSteps(steps);
    const estimatedDuration = this.estimateDeploymentDuration(steps);

    const plan: DeploymentPlan = {
      id: planId,
      version: planData.version,
      strategy: planData.strategy || this.config.deploymentStrategy,
      targets: planData.targets,
      steps,
      estimatedDuration,
      rollbackPlan: rollbackSteps,
      createdAt: Date.now(),
      createdBy: 'system', // Would be actual user in real implementation
    };

    this.plans.set(planId, plan);

    this.emit('deployment_plan_created', plan);
    logger.info('Deployment plan created', {
      planId,
      version: plan.version,
      strategy: plan.strategy,
      targets: plan.targets.length,
      steps: plan.steps.length,
    });

    return plan;
  }

  public async executeDeploymentPlan(planId: string): Promise<DeploymentExecution> {
    const plan = this.plans.get(planId);
    if (!plan) {
      throw new Error(`Deployment plan ${planId} not found`);
    }

    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const execution: DeploymentExecution = {
      id: executionId,
      planId,
      status: 'pending',
      startTime: Date.now(),
      completedSteps: [],
      failedSteps: [],
      logs: [],
      metrics: {
        totalSteps: plan.steps.length,
        completedSteps: 0,
        failedSteps: 0,
        successRate: 0,
        averageStepDuration: 0,
        totalDuration: 0,
        resourceUsage: { cpu: 0, memory: 0, network: 0 },
      },
    };

    this.executions.set(executionId, execution);

    // Start execution
    this.runDeploymentExecution(execution, plan);

    this.emit('deployment_started', execution);
    logger.info('Deployment execution started', {
      executionId,
      planId,
      version: plan.version,
    });

    return execution;
  }

  private async runDeploymentExecution(
    execution: DeploymentExecution,
    plan: DeploymentPlan
  ): Promise<void> {
    execution.status = 'running';

    try {
      for (const step of plan.steps) {
        execution.currentStep = step.id;

        this.addDeploymentLog(execution, 'info', `Starting step: ${step.name}`, step.id);

        const stepStartTime = Date.now();
        const success = await this.executeDeploymentStep(step, execution);
        const stepDuration = Date.now() - stepStartTime;

        if (success) {
          execution.completedSteps.push(step.id);
          execution.metrics.completedSteps++;
          this.addDeploymentLog(
            execution,
            'info',
            `Step completed: ${step.name} (${stepDuration}ms)`,
            step.id
          );
        } else {
          execution.failedSteps.push(step.id);
          execution.metrics.failedSteps++;
          this.addDeploymentLog(execution, 'error', `Step failed: ${step.name}`, step.id);

          if (this.config.enableRollback) {
            await this.rollbackDeployment(execution, plan);
            return;
          } else {
            throw new Error(`Deployment step failed: ${step.name}`);
          }
        }
      }

      // Deployment successful
      execution.status = 'success';
      execution.endTime = Date.now();
      execution.duration = execution.endTime - execution.startTime;
      execution.metrics.totalDuration = execution.duration;
      execution.metrics.successRate =
        execution.metrics.completedSteps / execution.metrics.totalSteps;
      execution.metrics.averageStepDuration = execution.duration / execution.metrics.totalSteps;

      this.addDeploymentLog(execution, 'info', 'Deployment completed successfully');
      this.emit('deployment_completed', execution);
    } catch (error) {
      execution.status = 'failed';
      execution.endTime = Date.now();
      execution.duration = execution.endTime - execution.startTime;

      this.addDeploymentLog(
        execution,
        'error',
        `Deployment failed: ${error instanceof Error ? error.message : String(error)}`
      );
      this.emit('deployment_failed', { execution, error });

      logger.error('Deployment execution failed', error, {
        executionId: execution.id,
        planId: execution.planId,
      });
    }
  }

  private async executeDeploymentStep(
    step: DeploymentStep,
    execution: DeploymentExecution
  ): Promise<boolean> {
    try {
      // Check dependencies
      for (const dependency of step.dependencies) {
        if (!execution.completedSteps.includes(dependency)) {
          throw new Error(`Dependency not met: ${dependency}`);
        }
      }

      // Execute step based on type
      switch (step.type) {
        case 'build':
          return await this.executeBuildStep(step, execution);
        case 'test':
          return await this.executeTestStep(step, execution);
        case 'deploy':
          return await this.executeDeployStep(step, execution);
        case 'verify':
          return await this.executeVerifyStep(step, execution);
        case 'rollback':
          return await this.executeRollbackStep(step, execution);
        default:
          throw new Error(`Unknown step type: ${step.type}`);
      }
    } catch (error) {
      this.addDeploymentLog(
        execution,
        'error',
        `Step execution error: ${error instanceof Error ? error.message : String(error)}`,
        step.id
      );
      return false;
    }
  }

  private async executeBuildStep(
    step: DeploymentStep,
    execution: DeploymentExecution
  ): Promise<boolean> {
    this.addDeploymentLog(execution, 'info', 'Building application...', step.id);

    // Simulate build process
    await new Promise(resolve => setTimeout(resolve, 2000));

    this.addDeploymentLog(execution, 'info', 'Build completed successfully', step.id);
    return true;
  }

  private async executeTestStep(
    step: DeploymentStep,
    execution: DeploymentExecution
  ): Promise<boolean> {
    this.addDeploymentLog(execution, 'info', 'Running tests...', step.id);

    // Simulate test execution
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Simulate test results (90% success rate)
    const success = Math.random() > 0.1;

    if (success) {
      this.addDeploymentLog(execution, 'info', 'All tests passed', step.id);
    } else {
      this.addDeploymentLog(execution, 'error', 'Some tests failed', step.id);
    }

    return success;
  }

  private async executeDeployStep(
    step: DeploymentStep,
    execution: DeploymentExecution
  ): Promise<boolean> {
    this.addDeploymentLog(execution, 'info', 'Deploying to targets...', step.id);

    const plan = this.plans.get(execution.planId);
    if (!plan) return false;

    // Deploy to each target
    for (const targetId of plan.targets) {
      const target = this.targets.get(targetId);
      if (!target) continue;

      this.addDeploymentLog(execution, 'info', `Deploying to ${target.name}...`, step.id);

      // Simulate deployment
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update target status
      target.status = 'deploying';
      target.version = plan.version;
      target.lastDeployed = Date.now();

      this.addDeploymentLog(execution, 'info', `Deployed to ${target.name} successfully`, step.id);
    }

    return true;
  }

  private async executeVerifyStep(
    step: DeploymentStep,
    execution: DeploymentExecution
  ): Promise<boolean> {
    this.addDeploymentLog(execution, 'info', 'Verifying deployment...', step.id);

    // Run health checks
    const healthCheckResults = await this.runHealthChecks();
    const allHealthy = healthCheckResults.every(result => result.healthy);

    if (allHealthy) {
      this.addDeploymentLog(execution, 'info', 'Deployment verification successful', step.id);
    } else {
      this.addDeploymentLog(execution, 'error', 'Deployment verification failed', step.id);
    }

    return allHealthy;
  }

  private async executeRollbackStep(
    step: DeploymentStep,
    execution: DeploymentExecution
  ): Promise<boolean> {
    this.addDeploymentLog(execution, 'info', 'Rolling back deployment...', step.id);

    // Simulate rollback
    await new Promise(resolve => setTimeout(resolve, 2000));

    this.addDeploymentLog(execution, 'info', 'Rollback completed', step.id);
    return true;
  }

  private async rollbackDeployment(
    execution: DeploymentExecution,
    plan: DeploymentPlan
  ): Promise<void> {
    execution.status = 'rolled_back';
    this.addDeploymentLog(execution, 'info', 'Starting automatic rollback...');

    for (const step of plan.rollbackPlan) {
      await this.executeDeploymentStep(step, execution);
    }

    this.addDeploymentLog(execution, 'info', 'Rollback completed');
    this.emit('deployment_rolled_back', execution);
  }

  private generateDeploymentSteps(
    strategy: DeploymentConfig['deploymentStrategy'],
    targets: string[],
    customSteps?: Partial<DeploymentStep>[]
  ): DeploymentStep[] {
    const steps: DeploymentStep[] = [];

    // Build step
    steps.push({
      id: 'build',
      name: 'Build Application',
      type: 'build',
      timeout: 300000, // 5 minutes
      retries: 2,
      dependencies: [],
      conditions: [],
    });

    // Test step
    steps.push({
      id: 'test',
      name: 'Run Tests',
      type: 'test',
      timeout: 600000, // 10 minutes
      retries: 1,
      dependencies: ['build'],
      conditions: [],
    });

    // Deploy steps based on strategy
    switch (strategy) {
      case 'blue-green':
        steps.push(...this.generateBlueGreenSteps(targets));
        break;
      case 'rolling':
        steps.push(...this.generateRollingSteps(targets));
        break;
      case 'canary':
        steps.push(...this.generateCanarySteps(targets));
        break;
      case 'recreate':
        steps.push(...this.generateRecreateSteps(targets));
        break;
    }

    // Verify step
    steps.push({
      id: 'verify',
      name: 'Verify Deployment',
      type: 'verify',
      timeout: 180000, // 3 minutes
      retries: 3,
      dependencies: steps.filter(s => s.type === 'deploy').map(s => s.id),
      conditions: [],
    });

    // Add custom steps
    if (customSteps) {
      customSteps.forEach((customStep, index) => {
        steps.push({
          id: `custom_${index}`,
          name: customStep.name || `Custom Step ${index + 1}`,
          type: customStep.type || 'deploy',
          timeout: customStep.timeout || 300000,
          retries: customStep.retries || 1,
          dependencies: customStep.dependencies || [],
          conditions: customStep.conditions || [],
          command: customStep.command,
        });
      });
    }

    return steps;
  }

  private generateBlueGreenSteps(targets: string[]): DeploymentStep[] {
    return [
      {
        id: 'deploy_green',
        name: 'Deploy to Green Environment',
        type: 'deploy',
        timeout: 600000,
        retries: 2,
        dependencies: ['test'],
        conditions: [],
      },
      {
        id: 'switch_traffic',
        name: 'Switch Traffic to Green',
        type: 'deploy',
        timeout: 60000,
        retries: 1,
        dependencies: ['deploy_green'],
        conditions: [],
      },
    ];
  }

  private generateRollingSteps(targets: string[]): DeploymentStep[] {
    return targets.map((targetId, index) => ({
      id: `deploy_${targetId}`,
      name: `Deploy to ${targetId}`,
      type: 'deploy' as const,
      timeout: 300000,
      retries: 2,
      dependencies: index === 0 ? ['test'] : [`deploy_${targets[index - 1]}`],
      conditions: [],
    }));
  }

  private generateCanarySteps(targets: string[]): DeploymentStep[] {
    const canaryTargets = targets.slice(
      0,
      Math.ceil((targets.length * this.config.canaryPercentage) / 100)
    );
    const remainingTargets = targets.slice(canaryTargets.length);

    const steps: DeploymentStep[] = [];

    // Deploy to canary targets
    steps.push({
      id: 'deploy_canary',
      name: 'Deploy to Canary Targets',
      type: 'deploy',
      timeout: 300000,
      retries: 2,
      dependencies: ['test'],
      conditions: [],
    });

    // Monitor canary
    steps.push({
      id: 'monitor_canary',
      name: 'Monitor Canary Deployment',
      type: 'verify',
      timeout: 600000,
      retries: 1,
      dependencies: ['deploy_canary'],
      conditions: [],
    });

    // Deploy to remaining targets
    if (remainingTargets.length > 0) {
      steps.push({
        id: 'deploy_remaining',
        name: 'Deploy to Remaining Targets',
        type: 'deploy',
        timeout: 600000,
        retries: 2,
        dependencies: ['monitor_canary'],
        conditions: [],
      });
    }

    return steps;
  }

  private generateRecreateSteps(targets: string[]): DeploymentStep[] {
    return [
      {
        id: 'stop_old',
        name: 'Stop Old Version',
        type: 'deploy',
        timeout: 180000,
        retries: 1,
        dependencies: ['test'],
        conditions: [],
      },
      {
        id: 'deploy_new',
        name: 'Deploy New Version',
        type: 'deploy',
        timeout: 600000,
        retries: 2,
        dependencies: ['stop_old'],
        conditions: [],
      },
    ];
  }

  private generateRollbackSteps(deploymentSteps: DeploymentStep[]): DeploymentStep[] {
    return deploymentSteps
      .filter(step => step.type === 'deploy')
      .reverse()
      .map(step => ({
        id: `rollback_${step.id}`,
        name: `Rollback ${step.name}`,
        type: 'rollback' as const,
        timeout: step.timeout,
        retries: step.retries,
        dependencies: [],
        conditions: [],
      }));
  }

  private estimateDeploymentDuration(steps: DeploymentStep[]): number {
    return steps.reduce((total, step) => total + step.timeout, 0);
  }

  private addDeploymentLog(
    execution: DeploymentExecution,
    level: DeploymentLog['level'],
    message: string,
    step?: string,
    target?: string,
    metadata?: Record<string, any>
  ): void {
    const log: DeploymentLog = {
      timestamp: Date.now(),
      level,
      message,
      step,
      target,
      metadata,
    };

    execution.logs.push(log);

    // Limit log size
    if (execution.logs.length > 1000) {
      execution.logs = execution.logs.slice(-1000);
    }

    this.emit('deployment_log', { execution, log });
  }

  private async loadDeploymentTargets(): Promise<void> {
    // In a real implementation, this would load from configuration or service discovery
    const sampleTargets: DeploymentTarget[] = [
      {
        id: 'web-1',
        name: 'Web Server 1',
        type: 'server',
        url: 'https://web-1.example.com',
        region: 'us-east-1',
        status: 'active',
        version: this.config.version,
        lastDeployed: Date.now() - 86400000, // 1 day ago
        healthStatus: 'healthy',
        metrics: { cpu: 45, memory: 60, disk: 30, network: 25, requests: 1000, errors: 5 },
      },
      {
        id: 'web-2',
        name: 'Web Server 2',
        type: 'server',
        url: 'https://web-2.example.com',
        region: 'us-west-1',
        status: 'active',
        version: this.config.version,
        lastDeployed: Date.now() - 86400000,
        healthStatus: 'healthy',
        metrics: { cpu: 50, memory: 55, disk: 35, network: 30, requests: 950, errors: 3 },
      },
    ];

    sampleTargets.forEach(target => {
      this.targets.set(target.id, target);
    });
  }

  private setupHealthChecks(): void {
    // Create default health checks
    this.healthChecks.set('http-check', {
      id: 'http-check',
      name: 'HTTP Health Check',
      type: 'http',
      endpoint: '/health',
      timeout: 5000,
      interval: this.config.healthCheckInterval,
      retries: 3,
      successThreshold: 1,
      failureThreshold: 3,
      enabled: true,
    });

    // Start health check intervals
    this.healthChecks.forEach(healthCheck => {
      if (healthCheck.enabled) {
        this.startHealthCheck(healthCheck);
      }
    });
  }

  private startHealthCheck(healthCheck: HealthCheck): void {
    const interval = setInterval(async () => {
      await this.runSingleHealthCheck(healthCheck);
    }, healthCheck.interval);

    this.healthCheckIntervals.set(healthCheck.id, interval);
  }

  private async runSingleHealthCheck(
    healthCheck: HealthCheck
  ): Promise<{ healthy: boolean; message?: string }> {
    try {
      // Simulate health check
      const healthy = Math.random() > 0.1; // 90% success rate

      if (!healthy) {
        this.emit('health_check_failed', { healthCheck, message: 'Health check failed' });
      }

      return { healthy };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.emit('health_check_error', { healthCheck, error, message });
      return { healthy: false, message };
    }
  }

  private async runHealthChecks(): Promise<Array<{ healthy: boolean; message?: string }>> {
    const results = [];

    for (const healthCheck of this.healthChecks.values()) {
      if (healthCheck.enabled) {
        const result = await this.runSingleHealthCheck(healthCheck);
        results.push(result);
      }
    }

    return results;
  }

  private async loadDeploymentHistory(): Promise<void> {
    // In a real implementation, this would load from persistent storage
    logger.debug('Deployment history loaded');
  }

  // Getters
  public getTargets(): DeploymentTarget[] {
    return Array.from(this.targets.values());
  }

  public getPlans(): DeploymentPlan[] {
    return Array.from(this.plans.values());
  }

  public getExecutions(): DeploymentExecution[] {
    return Array.from(this.executions.values());
  }

  public getExecution(executionId: string): DeploymentExecution | null {
    return this.executions.get(executionId) || null;
  }

  public updateConfig(config: Partial<DeploymentConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('deployment', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): DeploymentConfig {
    return { ...this.config };
  }

  public destroy(): void {
    // Clear health check intervals
    this.healthCheckIntervals.forEach(interval => {
      clearInterval(interval);
    });

    this.removeAllListeners();
  }
}

// Export singleton instance
export const deploymentManager = DeploymentManager.getInstance();
