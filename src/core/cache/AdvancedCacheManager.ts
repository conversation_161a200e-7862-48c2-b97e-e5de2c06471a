/**
 * Продвинутая система кэширования с поддержкой различных стратегий,
 * TTL, LRU, сжатия и персистентности
 */

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl?: number;
  accessCount: number;
  lastAccessed: number;
  size?: number;
  compressed?: boolean;
  tags?: string[];
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum cache size in bytes
  maxEntries?: number; // Maximum number of entries
  compression?: boolean;
  persistent?: boolean;
  strategy?: CacheStrategy;
}

export enum CacheStrategy {
  LRU = 'lru', // Least Recently Used
  LFU = 'lfu', // Least Frequently Used
  FIFO = 'fifo', // First In First Out
  TTL = 'ttl', // Time To Live based
}

export interface ICacheManager {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, options?: CacheOptions): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<void>;
  has(key: string): Promise<boolean>;
  keys(): Promise<string[]>;
  size(): Promise<number>;
  stats(): Promise<CacheStats>;
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalEntries: number;
  totalSize: number;
  evictions: number;
}

export class AdvancedCacheManager implements ICacheManager {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalEntries: 0,
    totalSize: 0,
    evictions: 0,
  };

  private defaultOptions: CacheOptions;
  private cleanupInterval?: NodeJS.Timeout;

  constructor(options: CacheOptions = {}) {
    this.defaultOptions = {
      ttl: 5 * 60 * 1000, // 5 minutes
      maxSize: 100 * 1024 * 1024, // 100MB
      maxEntries: 10000,
      compression: false,
      persistent: false,
      strategy: CacheStrategy.LRU,
      ...options,
    };

    // Запускаем периодическую очистку
    this.startCleanup();
  }

  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Проверяем TTL
    if (this.isExpired(entry)) {
      await this.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Обновляем статистику доступа
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    this.stats.hits++;
    this.updateHitRate();

    // Декомпрессия если необходимо
    let value = entry.value;
    if (entry.compressed && typeof value === 'string') {
      value = await this.decompress(value);
    }

    return value as T;
  }

  async set<T>(key: string, value: T, options?: CacheOptions): Promise<void> {
    const opts = { ...this.defaultOptions, ...options };

    // Сжатие если необходимо
    let processedValue = value;
    let compressed = false;
    if (opts.compression && this.shouldCompress(value)) {
      processedValue = await this.compress(value);
      compressed = true;
    }

    const size = this.calculateSize(processedValue);
    const entry: CacheEntry<T> = {
      key,
      value: processedValue,
      timestamp: Date.now(),
      ttl: opts.ttl,
      accessCount: 0,
      lastAccessed: Date.now(),
      size,
      compressed,
      tags: [],
    };

    // Проверяем ограничения и выполняем эвикцию если необходимо
    await this.ensureCapacity(size);

    // Удаляем старую запись если существует
    if (this.cache.has(key)) {
      await this.delete(key);
    }

    this.cache.set(key, entry);
    this.stats.totalEntries++;
    this.stats.totalSize += size;
  }

  async delete(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    this.cache.delete(key);
    this.stats.totalEntries--;
    this.stats.totalSize -= entry.size || 0;

    return true;
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.stats.totalEntries = 0;
    this.stats.totalSize = 0;
  }

  async has(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    if (this.isExpired(entry)) {
      await this.delete(key);
      return false;
    }

    return true;
  }

  async keys(): Promise<string[]> {
    const validKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      if (!this.isExpired(entry)) {
        validKeys.push(key);
      } else {
        // Удаляем истекшие записи
        await this.delete(key);
      }
    }

    return validKeys;
  }

  async size(): Promise<number> {
    return this.stats.totalEntries;
  }

  async stats(): Promise<CacheStats> {
    return { ...this.stats };
  }

  // Методы для работы с тегами
  async setWithTags<T>(
    key: string,
    value: T,
    tags: string[],
    options?: CacheOptions
  ): Promise<void> {
    await this.set(key, value, options);
    const entry = this.cache.get(key);
    if (entry) {
      entry.tags = tags;
    }
  }

  async invalidateByTag(tag: string): Promise<void> {
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache) {
      if (entry.tags?.includes(tag)) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      await this.delete(key);
    }
  }

  // Batch операции
  async mget<T>(keys: string[]): Promise<Map<string, T | null>> {
    const result = new Map<string, T | null>();

    for (const key of keys) {
      result.set(key, await this.get<T>(key));
    }

    return result;
  }

  async mset<T>(entries: Map<string, T>, options?: CacheOptions): Promise<void> {
    for (const [key, value] of entries) {
      await this.set(key, value, options);
    }
  }

  private async ensureCapacity(newEntrySize: number): Promise<void> {
    const maxSize = this.defaultOptions.maxSize!;
    const maxEntries = this.defaultOptions.maxEntries!;

    // Проверяем ограничение по размеру
    while (this.stats.totalSize + newEntrySize > maxSize && this.cache.size > 0) {
      await this.evictOne();
    }

    // Проверяем ограничение по количеству записей
    while (this.cache.size >= maxEntries) {
      await this.evictOne();
    }
  }

  private async evictOne(): Promise<void> {
    const strategy = this.defaultOptions.strategy!;
    let keyToEvict: string | null = null;

    switch (strategy) {
      case CacheStrategy.LRU:
        keyToEvict = this.findLRUKey();
        break;
      case CacheStrategy.LFU:
        keyToEvict = this.findLFUKey();
        break;
      case CacheStrategy.FIFO:
        keyToEvict = this.findFIFOKey();
        break;
      case CacheStrategy.TTL:
        keyToEvict = this.findExpiredKey();
        break;
    }

    if (keyToEvict) {
      await this.delete(keyToEvict);
      this.stats.evictions++;
    }
  }

  private findLRUKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUKey(): string | null {
    let leastUsedKey: string | null = null;
    let leastCount = Infinity;

    for (const [key, entry] of this.cache) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findFIFOKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findExpiredKey(): string | null {
    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        return key;
      }
    }
    return null;
  }

  private isExpired(entry: CacheEntry): boolean {
    if (!entry.ttl) {
      return false;
    }
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private calculateSize(value: any): number {
    if (typeof value === 'string') {
      return value.length * 2; // UTF-16
    }
    if (typeof value === 'number') {
      return 8;
    }
    if (typeof value === 'boolean') {
      return 4;
    }
    if (value === null || value === undefined) {
      return 0;
    }

    // Приблизительный расчет для объектов
    return JSON.stringify(value).length * 2;
  }

  private shouldCompress(value: any): boolean {
    const size = this.calculateSize(value);
    return size > 1024; // Сжимаем если больше 1KB
  }

  private async compress(value: any): Promise<string> {
    // Простая реализация сжатия (в реальном проекте можно использовать pako или другие библиотеки)
    const str = typeof value === 'string' ? value : JSON.stringify(value);
    return btoa(str); // Base64 encoding как простая замена сжатия
  }

  private async decompress(compressed: string): Promise<any> {
    try {
      const str = atob(compressed);
      return JSON.parse(str);
    } catch {
      return compressed; // Возвращаем как есть если не удалось декомпрессировать
    }
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(async () => {
      await this.cleanup();
    }, 60000); // Очистка каждую минуту
  }

  private async cleanup(): Promise<void> {
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      await this.delete(key);
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Фабрика для создания кэш-менеджеров
export class CacheFactory {
  static createMemoryCache(options?: CacheOptions): AdvancedCacheManager {
    return new AdvancedCacheManager({
      persistent: false,
      ...options,
    });
  }

  static createLRUCache(maxEntries: number = 1000): AdvancedCacheManager {
    return new AdvancedCacheManager({
      strategy: CacheStrategy.LRU,
      maxEntries,
      persistent: false,
    });
  }

  static createTTLCache(ttl: number = 300000): AdvancedCacheManager {
    return new AdvancedCacheManager({
      strategy: CacheStrategy.TTL,
      ttl,
      persistent: false,
    });
  }
}

// Глобальный кэш-менеджер
export const globalCache = CacheFactory.createLRUCache(10000);
