import { BrowserWindow, app } from 'electron';

interface Command {
  id: string;
  name: string;
  description: string;
  shortcut?: string;
  action: (window: BrowserWindow, ...args: any[]) => Promise<void>;
}

export class CommandManager {
  private static instance: CommandManager;
  private commands: Map<string, Command>;
  private mainWindow: BrowserWindow | null = null;

  private constructor() {
    this.commands = new Map();
    this.registerDefaultCommands();
  }

  public static getInstance(): CommandManager {
    if (!CommandManager.instance) {
      CommandManager.instance = new CommandManager();
    }
    return CommandManager.instance;
  }

  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private registerDefaultCommands(): void {
    this.registerCommand({
      id: 'new-tab',
      name: 'New Tab',
      description: 'Open a new tab',
      shortcut: 'CmdOrCtrl+T',
      action: async window => {
        // Implement new tab logic
      },
    });

    this.registerCommand({
      id: 'close-tab',
      name: 'Close Tab',
      description: 'Close current tab',
      shortcut: 'CmdOrCtrl+W',
      action: async window => {
        // Implement close tab logic
      },
    });

    this.registerCommand({
      id: 'reload',
      name: 'Reload',
      description: 'Reload current page',
      shortcut: 'CmdOrCtrl+R',
      action: async window => {
        window?.webContents.reload();
      },
    });

    this.registerCommand({
      id: 'go-back',
      name: 'Go Back',
      description: 'Go to previous page',
      shortcut: 'Alt+Left',
      action: async window => {
        window?.webContents.goBack();
      },
    });

    this.registerCommand({
      id: 'go-forward',
      name: 'Go Forward',
      description: 'Go to next page',
      shortcut: 'Alt+Right',
      action: async window => {
        window?.webContents.goForward();
      },
    });
  }

  public registerCommand(command: Command): void {
    this.commands.set(command.id, command);
    if (command.shortcut) {
      this.registerShortcut(command);
    }
  }

  private registerShortcut(command: Command): void {
    if (!command.shortcut) return;

    const parts = command.shortcut.split('+');
    const key = parts.pop()?.toLowerCase();
    const modifiers = parts.map(m => m.toLowerCase());

    if (!key) return;

    app.on('ready', () => {
      // Register global shortcut
      // Implementation depends on your needs
    });
  }

  public async executeCommand(commandId: string, ...args: any[]): Promise<void> {
    const command = this.commands.get(commandId);
    if (!command) {
      throw new Error(`Command not found: ${commandId}`);
    }

    if (!this.mainWindow) {
      throw new Error('Main window not set');
    }

    await command.action(this.mainWindow, ...args);
  }

  public getCommands(): Command[] {
    return Array.from(this.commands.values());
  }

  public getCommand(commandId: string): Command | undefined {
    return this.commands.get(commandId);
  }

  public searchCommands(query: string): Command[] {
    const lowerQuery = query.toLowerCase();
    return this.getCommands().filter(
      command =>
        command.name.toLowerCase().includes(lowerQuery) ||
        command.description.toLowerCase().includes(lowerQuery)
    );
  }
}
