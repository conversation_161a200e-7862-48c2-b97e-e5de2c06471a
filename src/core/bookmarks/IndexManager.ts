import { Bookmark } from '../BookmarkManager';

export class IndexManager {
  private searchIndex: Map<string, Set<string>> = new Map();
  private tagIndex: Map<string, Set<string>> = new Map();
  private urlIndex: Map<string, string> = new Map();
  private titleIndex: Map<string, Set<string>> = new Map();
  private descriptionIndex: Map<string, Set<string>> = new Map();
  private notesIndex: Map<string, Set<string>> = new Map();
  private customIconIndex: Map<string, Set<string>> = new Map();
  private colorIndex: Map<string, Set<string>> = new Map();
  private folderIndex: Map<string, Set<string>> = new Map();

  public updateIndexes(bookmarks: Map<string, Bookmark>): void {
    this.clearIndexes();

    for (const [id, bookmark] of bookmarks) {
      // Update search index
      const searchTerms = this.getSearchTerms(bookmark);
      for (const term of searchTerms) {
        if (!this.searchIndex.has(term)) {
          this.searchIndex.set(term, new Set());
        }
        this.searchIndex.get(term)?.add(id);
      }

      // Update tag index
      for (const tag of bookmark.tags) {
        if (!this.tagIndex.has(tag)) {
          this.tagIndex.set(tag, new Set());
        }
        this.tagIndex.get(tag)?.add(id);
      }

      // Update URL index
      this.urlIndex.set(bookmark.url, id);

      // Update title index
      const titleTerms = bookmark.title.toLowerCase().split(/\s+/);
      for (const term of titleTerms) {
        if (!this.titleIndex.has(term)) {
          this.titleIndex.set(term, new Set());
        }
        this.titleIndex.get(term)?.add(id);
      }

      // Update description index
      if (bookmark.description) {
        const descriptionTerms = bookmark.description.toLowerCase().split(/\s+/);
        for (const term of descriptionTerms) {
          if (!this.descriptionIndex.has(term)) {
            this.descriptionIndex.set(term, new Set());
          }
          this.descriptionIndex.get(term)?.add(id);
        }
      }

      // Update notes index
      if (bookmark.notes) {
        const notesTerms = bookmark.notes.toLowerCase().split(/\s+/);
        for (const term of notesTerms) {
          if (!this.notesIndex.has(term)) {
            this.notesIndex.set(term, new Set());
          }
          this.notesIndex.get(term)?.add(id);
        }
      }

      // Update custom icon index
      if (bookmark.customIcon) {
        if (!this.customIconIndex.has(bookmark.customIcon)) {
          this.customIconIndex.set(bookmark.customIcon, new Set());
        }
        this.customIconIndex.get(bookmark.customIcon)?.add(id);
      }

      // Update color index
      if (bookmark.color) {
        if (!this.colorIndex.has(bookmark.color)) {
          this.colorIndex.set(bookmark.color, new Set());
        }
        this.colorIndex.get(bookmark.color)?.add(id);
      }

      // Update folder index
      if (bookmark.parentId) {
        if (!this.folderIndex.has(bookmark.parentId)) {
          this.folderIndex.set(bookmark.parentId, new Set());
        }
        this.folderIndex.get(bookmark.parentId)?.add(id);
      }
    }
  }

  private clearIndexes(): void {
    this.searchIndex.clear();
    this.tagIndex.clear();
    this.urlIndex.clear();
    this.titleIndex.clear();
    this.descriptionIndex.clear();
    this.notesIndex.clear();
    this.customIconIndex.clear();
    this.colorIndex.clear();
    this.folderIndex.clear();
  }

  private getSearchTerms(bookmark: Bookmark): string[] {
    const terms = new Set<string>();

    // Add title terms
    bookmark.title
      .toLowerCase()
      .split(/\s+/)
      .forEach(term => terms.add(term));

    // Add URL terms
    bookmark.url
      .toLowerCase()
      .split(/[/?#&.=]/)
      .forEach(term => {
        if (term) terms.add(term);
      });

    // Add description terms
    if (bookmark.description) {
      bookmark.description
        .toLowerCase()
        .split(/\s+/)
        .forEach(term => terms.add(term));
    }

    // Add notes terms
    if (bookmark.notes) {
      bookmark.notes
        .toLowerCase()
        .split(/\s+/)
        .forEach(term => terms.add(term));
    }

    // Add tags
    bookmark.tags.forEach(tag => terms.add(tag.toLowerCase()));

    return Array.from(terms);
  }

  public searchBookmarks(
    query: string,
    bookmarks: Map<string, Bookmark>,
    maxResults: number
  ): Bookmark[] {
    const terms = query.toLowerCase().split(/\s+/);
    const results = new Set<string>();

    for (const term of terms) {
      // Search in title index
      this.titleIndex.get(term)?.forEach(id => results.add(id));

      // Search in description index
      this.descriptionIndex.get(term)?.forEach(id => results.add(id));

      // Search in notes index
      this.notesIndex.get(term)?.forEach(id => results.add(id));

      // Search in tag index
      this.tagIndex.get(term)?.forEach(id => results.add(id));

      // Search in URL index
      for (const [url, id] of this.urlIndex.entries()) {
        if (url.includes(term)) {
          results.add(id);
        }
      }
    }

    const bookmarksArray = Array.from(results)
      .map(id => bookmarks.get(id))
      .filter((bookmark): bookmark is Bookmark => bookmark !== undefined)
      .slice(0, maxResults);

    return bookmarksArray;
  }
}
