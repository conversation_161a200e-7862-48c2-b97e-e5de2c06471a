import { AppState, initialAppState } from './AppState';
import { StateManager } from './StateManager';

// Создание синглтона для управления состоянием приложения
export const appStateManager = new StateManager<AppState>(initialAppState, {
  persistence: true,
  storageKey: 'nova_browser_state',
  maxHistory: 100,
  debounceTime: 300,
});

// Экспорт типов и интерфейсов
export type { AppState } from './AppState';
export type { StateConfig, StateChange } from './StateManager';

// Экспорт базового класса для возможного расширения
export { StateManager } from './StateManager';
