import { EventEmitter } from 'events';

export interface StateConfig {
  persistence: boolean;
  storageKey: string;
  maxHistory: number;
  debounceTime: number;
}

export interface StateChange<T> {
  previousState: T;
  newState: T;
  timestamp: number;
  source: string;
}

export class StateManager<T extends object> {
  private state: T;
  private config: StateConfig;
  private eventEmitter: EventEmitter;
  private history: StateChange<T>[];
  private pendingChanges: Map<string, Partial<T>>;
  private debounceTimers: Map<string, NodeJS.Timeout>;

  constructor(initialState: T, config: Partial<StateConfig> = {}) {
    this.state = initialState;
    this.config = {
      persistence: true,
      storageKey: 'nova_state',
      maxHistory: 100,
      debounceTime: 300,
      ...config,
    };
    this.eventEmitter = new EventEmitter();
    this.history = [];
    this.pendingChanges = new Map();
    this.debounceTimers = new Map();

    if (this.config.persistence) {
      this.loadState();
    }
  }

  // Загрузка состояния из хранилища
  private loadState(): void {
    try {
      const savedState = localStorage.getItem(this.config.storageKey);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        this.state = { ...this.state, ...parsedState };
        this.eventEmitter.emit('stateLoaded', this.state);
      }
    } catch (error) {
      console.error('Failed to load state:', error);
    }
  }

  // Сохранение состояния в хранилище
  private saveState(): void {
    if (!this.config.persistence) return;

    try {
      localStorage.setItem(this.config.storageKey, JSON.stringify(this.state));
      this.eventEmitter.emit('stateSaved', this.state);
    } catch (error) {
      console.error('Failed to save state:', error);
    }
  }

  // Получение текущего состояния
  getState(): T {
    return { ...this.state };
  }

  // Обновление состояния
  setState(partialState: Partial<T>, source: string = 'unknown'): void {
    const previousState = { ...this.state };
    this.state = { ...this.state, ...partialState };

    const change: StateChange<T> = {
      previousState,
      newState: this.state,
      timestamp: Date.now(),
      source,
    };

    this.history.push(change);
    if (this.history.length > this.config.maxHistory) {
      this.history.shift();
    }

    this.saveState();
    this.eventEmitter.emit('stateChanged', change);
  }

  // Отложенное обновление состояния
  debouncedSetState(key: string, partialState: Partial<T>, source: string = 'unknown'): void {
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key));
    }

    this.pendingChanges.set(key, partialState);

    const timer = setTimeout(() => {
      const changes = this.pendingChanges.get(key);
      if (changes) {
        this.setState(changes, source);
        this.pendingChanges.delete(key);
      }
    }, this.config.debounceTime);

    this.debounceTimers.set(key, timer);
  }

  // Отмена отложенного обновления
  cancelDebouncedUpdate(key: string): void {
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key));
      this.debounceTimers.delete(key);
      this.pendingChanges.delete(key);
    }
  }

  // Получение истории изменений
  getHistory(): StateChange<T>[] {
    return [...this.history];
  }

  // Отмена последнего изменения
  undo(): void {
    if (this.history.length > 0) {
      const lastChange = this.history.pop();
      if (lastChange) {
        this.state = lastChange.previousState;
        this.saveState();
        this.eventEmitter.emit('stateUndone', lastChange);
      }
    }
  }

  // Подписка на изменения состояния
  subscribe(callback: (change: StateChange<T>) => void): () => void {
    this.eventEmitter.on('stateChanged', callback);
    return () => {
      this.eventEmitter.off('stateChanged', callback);
    };
  }

  // Подписка на загрузку состояния
  onStateLoaded(callback: (state: T) => void): () => void {
    this.eventEmitter.on('stateLoaded', callback);
    return () => {
      this.eventEmitter.off('stateLoaded', callback);
    };
  }

  // Подписка на сохранение состояния
  onStateSaved(callback: (state: T) => void): () => void {
    this.eventEmitter.on('stateSaved', callback);
    return () => {
      this.eventEmitter.off('stateSaved', callback);
    };
  }

  // Подписка на отмену изменения
  onStateUndone(callback: (change: StateChange<T>) => void): () => void {
    this.eventEmitter.on('stateUndone', callback);
    return () => {
      this.eventEmitter.off('stateUndone', callback);
    };
  }

  // Очистка ресурсов
  dispose(): void {
    this.eventEmitter.removeAllListeners();
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    this.pendingChanges.clear();
  }
}
