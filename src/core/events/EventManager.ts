import { EventEmitter } from 'events';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

import { app } from 'electron';

interface EventHandler {
  id: string;
  callback: (...args: any[]) => void;
  once: boolean;
}

interface EventSubscription {
  event: string;
  handler: EventHandler;
}

interface EventLog {
  timestamp: number;
  event: string;
  data: any;
}

interface EventManagerSettings {
  maxListeners: number;
  logEvents: boolean;
  logPath: string;
  persistEvents: boolean;
  eventTTL: number;
}

export class EventManager {
  private emitter: EventEmitter;
  private handlers: Map<string, EventHandler[]>;
  private subscriptions: Map<string, EventSubscription[]>;
  private eventLog: EventLog[];
  private settings: EventManagerSettings;

  constructor() {
    this.emitter = new EventEmitter();
    this.handlers = new Map();
    this.subscriptions = new Map();
    this.eventLog = [];
    this.settings = {
      maxListeners: 10,
      logEvents: true,
      logPath: join(app.getPath('userData'), 'events'),
      persistEvents: true,
      eventTTL: 24 * 60 * 60 * 1000, // 24 hours
    };

    this.initialize();
  }

  private initialize(): void {
    // Create event log directory
    if (!existsSync(this.settings.logPath)) {
      mkdirSync(this.settings.logPath, { recursive: true });
    }

    // Set max listeners
    this.emitter.setMaxListeners(this.settings.maxListeners);

    // Load persisted events
    if (this.settings.persistEvents) {
      this.loadPersistedEvents();
    }

    // Start event cleanup interval
    setInterval(() => this.cleanupEvents(), this.settings.eventTTL);
  }

  public on(event: string, callback: (...args: any[]) => void): string {
    const handler: EventHandler = {
      id: Math.random().toString(36).substr(2, 9),
      callback,
      once: false,
    };

    if (!this.handlers.has(event)) {
      this.handlers.set(event, []);
    }

    this.handlers.get(event)!.push(handler);
    this.emitter.on(event, callback);

    return handler.id;
  }

  public once(event: string, callback: (...args: any[]) => void): string {
    const handler: EventHandler = {
      id: Math.random().toString(36).substr(2, 9),
      callback,
      once: true,
    };

    if (!this.handlers.has(event)) {
      this.handlers.set(event, []);
    }

    this.handlers.get(event)!.push(handler);
    this.emitter.once(event, callback);

    return handler.id;
  }

  public off(event: string, handlerId: string): void {
    const handlers = this.handlers.get(event);
    if (!handlers) return;

    const handler = handlers.find(h => h.id === handlerId);
    if (!handler) return;

    this.emitter.removeListener(event, handler.callback);
    this.handlers.set(
      event,
      handlers.filter(h => h.id !== handlerId)
    );
  }

  public emit(event: string, ...args: any[]): void {
    this.emitter.emit(event, ...args);

    if (this.settings.logEvents) {
      this.logEvent(event, args);
    }
  }

  public subscribe(event: string, handler: EventHandler): void {
    if (!this.subscriptions.has(event)) {
      this.subscriptions.set(event, []);
    }

    this.subscriptions.get(event)!.push({ event, handler });
  }

  public unsubscribe(event: string, handlerId: string): void {
    const subscriptions = this.subscriptions.get(event);
    if (!subscriptions) return;

    this.subscriptions.set(
      event,
      subscriptions.filter(s => s.handler.id !== handlerId)
    );
  }

  public getEventHandlers(event: string): EventHandler[] {
    return this.handlers.get(event) || [];
  }

  public getEventSubscriptions(event: string): EventSubscription[] {
    return this.subscriptions.get(event) || [];
  }

  public getEventLog(): EventLog[] {
    return this.eventLog;
  }

  public clearEventLog(): void {
    this.eventLog = [];
  }

  public updateSettings(settings: Partial<EventManagerSettings>): void {
    this.settings = { ...this.settings, ...settings };
    this.emitter.setMaxListeners(this.settings.maxListeners);
  }

  private logEvent(event: string, data: any[]): void {
    const log: EventLog = {
      timestamp: Date.now(),
      event,
      data,
    };

    this.eventLog.push(log);

    if (this.settings.persistEvents) {
      this.persistEvent(log);
    }
  }

  private persistEvent(log: EventLog): void {
    const logFile = join(this.settings.logPath, `${log.timestamp}.json`);
    writeFileSync(logFile, JSON.stringify(log, null, 2));
  }

  private loadPersistedEvents(): void {
    // Implementation for loading persisted events
  }

  private cleanupEvents(): void {
    const now = Date.now();
    this.eventLog = this.eventLog.filter(log => now - log.timestamp < this.settings.eventTTL);
  }

  public getEventStats(): { [key: string]: number } {
    const stats: { [key: string]: number } = {};
    this.eventLog.forEach(log => {
      stats[log.event] = (stats[log.event] || 0) + 1;
    });
    return stats;
  }

  public getEventTimeline(): EventLog[] {
    return [...this.eventLog].sort((a, b) => a.timestamp - b.timestamp);
  }

  public getEventDistribution(): { [key: string]: number } {
    const distribution: { [key: string]: number } = {};
    const total = this.eventLog.length;

    this.eventLog.forEach(log => {
      distribution[log.event] = (distribution[log.event] || 0) + 1;
    });

    Object.keys(distribution).forEach(event => {
      distribution[event] = (distribution[event] / total) * 100;
    });

    return distribution;
  }

  public getEventFrequency(): { [key: string]: number } {
    const frequency: { [key: string]: number } = {};
    const now = Date.now();

    this.eventLog.forEach(log => {
      const timeDiff = now - log.timestamp;
      const minutes = Math.floor(timeDiff / (60 * 1000));
      frequency[log.event] = (frequency[log.event] || 0) + 1 / minutes;
    });

    return frequency;
  }

  public getEventPatterns(): { [key: string]: string[] } {
    const patterns: { [key: string]: string[] } = {};
    const timeline = this.getEventTimeline();

    for (let i = 0; i < timeline.length - 1; i++) {
      const current = timeline[i];
      const next = timeline[i + 1];

      if (!patterns[current.event]) {
        patterns[current.event] = [];
      }

      if (!patterns[current.event].includes(next.event)) {
        patterns[current.event].push(next.event);
      }
    }

    return patterns;
  }

  public getEventCorrelations(): { [key: string]: { [key: string]: number } } {
    const correlations: { [key: string]: { [key: string]: number } } = {};
    const timeline = this.getEventTimeline();

    for (let i = 0; i < timeline.length; i++) {
      const current = timeline[i];
      const window = timeline.slice(i + 1, i + 6); // Look at next 5 events

      if (!correlations[current.event]) {
        correlations[current.event] = {};
      }

      window.forEach(event => {
        correlations[current.event][event.event] =
          (correlations[current.event][event.event] || 0) + 1;
      });
    }

    return correlations;
  }

  public getEventMetrics(): {
    total: number;
    unique: number;
    averageFrequency: number;
    mostFrequent: string;
    leastFrequent: string;
  } {
    const stats = this.getEventStats();
    const events = Object.keys(stats);
    const total = this.eventLog.length;
    const unique = events.length;
    const averageFrequency =
      total / (this.eventLog[this.eventLog.length - 1].timestamp - this.eventLog[0].timestamp);
    const mostFrequent = events.reduce((a, b) => (stats[a] > stats[b] ? a : b));
    const leastFrequent = events.reduce((a, b) => (stats[a] < stats[b] ? a : b));

    return {
      total,
      unique,
      averageFrequency,
      mostFrequent,
      leastFrequent,
    };
  }
}
