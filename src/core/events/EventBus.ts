/**
 * Event Bus - Система событий для event-driven архитектуры
 * Поддерживает типизированные события, middleware, и асинхронную обработку
 */

export type EventHandler<T = any> = (event: T) => void | Promise<void>;
export type EventMiddleware<T = any> = (
  event: T,
  next: () => void | Promise<void>
) => void | Promise<void>;

export interface IEvent {
  type: string;
  timestamp: number;
  id: string;
  source?: string;
  metadata?: Record<string, any>;
}

export interface EventSubscription {
  id: string;
  unsubscribe: () => void;
}

export interface EventBusOptions {
  enableLogging?: boolean;
  enableMetrics?: boolean;
  maxListeners?: number;
  errorHandler?: (error: Error, event: IEvent) => void;
}

export class EventBus {
  private listeners = new Map<string, Set<EventHandler>>();
  private middlewares: EventMiddleware[] = [];
  private subscriptions = new Map<string, EventSubscription>();
  private options: EventBusOptions;
  private metrics = {
    eventsEmitted: 0,
    eventsHandled: 0,
    errors: 0,
  };

  constructor(options: EventBusOptions = {}) {
    this.options = {
      enableLogging: false,
      enableMetrics: true,
      maxListeners: 100,
      ...options,
    };
  }

  /**
   * Подписка на событие
   */
  on<T extends IEvent>(eventType: string, handler: EventHandler<T>): EventSubscription {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }

    const listeners = this.listeners.get(eventType)!;

    if (listeners.size >= (this.options.maxListeners || 100)) {
      throw new Error(
        `Maximum listeners (${this.options.maxListeners}) exceeded for event: ${eventType}`
      );
    }

    listeners.add(handler as EventHandler);

    const subscriptionId = this.generateId();
    const subscription: EventSubscription = {
      id: subscriptionId,
      unsubscribe: () => this.off(eventType, handler),
    };

    this.subscriptions.set(subscriptionId, subscription);
    return subscription;
  }

  /**
   * Одноразовая подписка на событие
   */
  once<T extends IEvent>(eventType: string, handler: EventHandler<T>): EventSubscription {
    const onceHandler = (event: T) => {
      handler(event);
      this.off(eventType, onceHandler);
    };

    return this.on(eventType, onceHandler);
  }

  /**
   * Отписка от события
   */
  off<T extends IEvent>(eventType: string, handler: EventHandler<T>): void {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.delete(handler as EventHandler);
      if (listeners.size === 0) {
        this.listeners.delete(eventType);
      }
    }
  }

  /**
   * Отписка от всех событий определенного типа
   */
  offAll(eventType: string): void {
    this.listeners.delete(eventType);
  }

  /**
   * Эмиссия события
   */
  async emit<T extends IEvent>(event: T): Promise<void> {
    if (this.options.enableMetrics) {
      this.metrics.eventsEmitted++;
    }

    if (this.options.enableLogging) {
      console.log(`[EventBus] Emitting event: ${event.type}`, event);
    }

    const listeners = this.listeners.get(event.type);
    if (!listeners || listeners.size === 0) {
      return;
    }

    // Применяем middleware
    await this.applyMiddlewares(event);

    // Обрабатываем событие всеми слушателями
    const promises = Array.from(listeners).map(async handler => {
      try {
        await handler(event);
        if (this.options.enableMetrics) {
          this.metrics.eventsHandled++;
        }
      } catch (error) {
        if (this.options.enableMetrics) {
          this.metrics.errors++;
        }

        if (this.options.errorHandler) {
          this.options.errorHandler(error as Error, event);
        } else {
          console.error(`[EventBus] Error handling event ${event.type}:`, error);
        }
      }
    });

    await Promise.all(promises);
  }

  /**
   * Синхронная эмиссия события
   */
  emitSync<T extends IEvent>(event: T): void {
    if (this.options.enableMetrics) {
      this.metrics.eventsEmitted++;
    }

    if (this.options.enableLogging) {
      console.log(`[EventBus] Emitting sync event: ${event.type}`, event);
    }

    const listeners = this.listeners.get(event.type);
    if (!listeners || listeners.size === 0) {
      return;
    }

    // Обрабатываем событие всеми слушателями синхронно
    for (const handler of listeners) {
      try {
        const result = handler(event);
        // Если handler возвращает Promise, игнорируем его в синхронном режиме
        if (result instanceof Promise) {
          result.catch(error => {
            if (this.options.errorHandler) {
              this.options.errorHandler(error, event);
            } else {
              console.error(`[EventBus] Async error in sync handler for ${event.type}:`, error);
            }
          });
        }

        if (this.options.enableMetrics) {
          this.metrics.eventsHandled++;
        }
      } catch (error) {
        if (this.options.enableMetrics) {
          this.metrics.errors++;
        }

        if (this.options.errorHandler) {
          this.options.errorHandler(error as Error, event);
        } else {
          console.error(`[EventBus] Error handling sync event ${event.type}:`, error);
        }
      }
    }
  }

  /**
   * Добавление middleware
   */
  use(middleware: EventMiddleware): void {
    this.middlewares.push(middleware);
  }

  /**
   * Получение метрик
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Сброс метрик
   */
  resetMetrics(): void {
    this.metrics = {
      eventsEmitted: 0,
      eventsHandled: 0,
      errors: 0,
    };
  }

  /**
   * Получение количества слушателей для события
   */
  listenerCount(eventType: string): number {
    const listeners = this.listeners.get(eventType);
    return listeners ? listeners.size : 0;
  }

  /**
   * Получение всех типов событий с активными слушателями
   */
  eventNames(): string[] {
    return Array.from(this.listeners.keys());
  }

  /**
   * Очистка всех слушателей
   */
  removeAllListeners(): void {
    this.listeners.clear();
    this.subscriptions.clear();
  }

  private async applyMiddlewares<T extends IEvent>(event: T): Promise<void> {
    let index = 0;

    const next = async (): Promise<void> => {
      if (index < this.middlewares.length) {
        const middleware = this.middlewares[index++];
        await middleware(event, next);
      }
    };

    await next();
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Базовые типы событий
export interface SystemEvent extends IEvent {
  type: 'system.startup' | 'system.shutdown' | 'system.error';
}

export interface UserEvent extends IEvent {
  type: 'user.login' | 'user.logout' | 'user.action';
  userId?: string;
}

export interface NavigationEvent extends IEvent {
  type: 'navigation.changed' | 'navigation.loading' | 'navigation.loaded';
  url?: string;
  tabId?: string;
}

export interface SecurityEvent extends IEvent {
  type: 'security.threat' | 'security.blocked' | 'security.warning';
  threatType?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

export interface PerformanceEvent extends IEvent {
  type: 'performance.metric' | 'performance.warning' | 'performance.threshold';
  metric?: string;
  value?: number;
}

// Фабрика для создания событий
export class EventFactory {
  static createEvent<T extends IEvent>(
    type: string,
    data: Omit<T, 'type' | 'timestamp' | 'id'> = {} as any
  ): T {
    return {
      type,
      timestamp: Date.now(),
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...data,
    } as T;
  }

  static createSystemEvent(
    type: SystemEvent['type'],
    data: Omit<SystemEvent, 'type' | 'timestamp' | 'id'> = {}
  ): SystemEvent {
    return this.createEvent<SystemEvent>(type, data);
  }

  static createUserEvent(
    type: UserEvent['type'],
    data: Omit<UserEvent, 'type' | 'timestamp' | 'id'> = {}
  ): UserEvent {
    return this.createEvent<UserEvent>(type, data);
  }

  static createNavigationEvent(
    type: NavigationEvent['type'],
    data: Omit<NavigationEvent, 'type' | 'timestamp' | 'id'> = {}
  ): NavigationEvent {
    return this.createEvent<NavigationEvent>(type, data);
  }

  static createSecurityEvent(
    type: SecurityEvent['type'],
    data: Omit<SecurityEvent, 'type' | 'timestamp' | 'id'> = {}
  ): SecurityEvent {
    return this.createEvent<SecurityEvent>(type, data);
  }

  static createPerformanceEvent(
    type: PerformanceEvent['type'],
    data: Omit<PerformanceEvent, 'type' | 'timestamp' | 'id'> = {}
  ): PerformanceEvent {
    return this.createEvent<PerformanceEvent>(type, data);
  }
}

// Глобальный экземпляр Event Bus
export const eventBus = new EventBus({
  enableLogging: process.env.NODE_ENV === 'development',
  enableMetrics: true,
  maxListeners: 1000,
});

// Middleware для логирования
export const loggingMiddleware: EventMiddleware = async (event, next) => {
  console.log(`[EventBus] Processing event: ${event.type}`, event);
  const start = performance.now();
  await next();
  const duration = performance.now() - start;
  console.log(`[EventBus] Event ${event.type} processed in ${duration.toFixed(2)}ms`);
};

// Middleware для метрик
export const metricsMiddleware: EventMiddleware = async (event, next) => {
  const start = performance.now();
  await next();
  const duration = performance.now() - start;

  // Здесь можно отправлять метрики в систему мониторинга
  if (duration > 100) {
    console.warn(`[EventBus] Slow event processing: ${event.type} took ${duration.toFixed(2)}ms`);
  }
};
