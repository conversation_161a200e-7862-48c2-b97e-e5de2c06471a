import { AnalyticsConfig, AnalyticsCore, AnalyticsEvent } from '../AnalyticsCore';

// Mock implementation of AnalyticsCore for testing
class MockAnalyticsCore extends AnalyticsCore {
  public sendEventsMock = jest.fn();
  public getCommonPropertiesMock = jest.fn(() => ({ mockProperty: 'mockValue' }));

  protected async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    this.sendEventsMock(events);
    return Promise.resolve();
  }

  protected getCommonProperties(): Record<string, any> {
    return this.getCommonPropertiesMock();
  }

  // Expose protected methods for testing
  public getEvents(): AnalyticsEvent[] {
    return this.events;
  }
}

describe('AnalyticsCore', () => {
  let analytics: MockAnalyticsCore;
  const config: Partial<AnalyticsConfig> = {
    endpoint: 'http://test.com/analytics',
    batchSize: 3,
    flushInterval: 1000,
    debug: false,
  };

  beforeEach(() => {
    analytics = new MockAnalyticsCore(config);
    analytics.initialize();
    jest.useFakeTimers();
  });

  afterEach(() => {
    analytics.cleanup();
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('should initialize with the correct config', () => {
    const currentConfig = (analytics as any).config;
    expect(currentConfig).toEqual(expect.objectContaining(config));
  });

  it('should track an event and add it to the queue', () => {
    analytics.track('test_event', { foo: 'bar' });
    const events = analytics.getEvents();
    expect(events).toHaveLength(1);
    expect(events[0]).toEqual(
      expect.objectContaining({
        type: 'test_event',
        properties: expect.objectContaining({
          foo: 'bar',
          mockProperty: 'mockValue',
        }),
      })
    );
  });

  it('should not track events if disabled', () => {
    analytics.updateConfig({ enabled: false });
    analytics.track('disabled_event');
    expect(analytics.getEvents()).toHaveLength(0);
  });

  it('should flush events when batch size is reached', () => {
    analytics.track('event1');
    analytics.track('event2');
    expect(analytics.sendEventsMock).not.toHaveBeenCalled();
    analytics.track('event3');
    expect(analytics.sendEventsMock).toHaveBeenCalledTimes(1);
    expect(analytics.sendEventsMock).toHaveBeenCalledWith(expect.any(Array));
    expect(analytics.getEvents()).toHaveLength(0);
  });

  it('should flush events automatically after the flush interval', () => {
    analytics.track('event1');
    expect(analytics.sendEventsMock).not.toHaveBeenCalled();

    jest.advanceTimersByTime(config.flushInterval! + 100);

    expect(analytics.sendEventsMock).toHaveBeenCalledTimes(1);
    expect(analytics.getEvents()).toHaveLength(0);
  });

  it('should handle flush errors and re-queue events', async () => {
    const error = new Error('Flush failed');
    analytics.sendEventsMock.mockRejectedValueOnce(error);

    analytics.track('event1');
    analytics.track('event2');
    analytics.track('event3'); // This will trigger a flush

    // Wait for the async flush to complete
    await new Promise(process.nextTick);

    expect(analytics.sendEventsMock).toHaveBeenCalledTimes(1);
    expect(analytics.getEvents()).toHaveLength(3); // Events should be re-queued
  });

  it('should identify a user and include user ID in subsequent events', () => {
    const userId = 'test-user-123';
    analytics.identify(userId, { plan: 'premium' });

    // The 'identify' call itself is an event
    expect(analytics.getEvents()[0]).toEqual(
      expect.objectContaining({
        type: 'identify',
        userId,
      })
    );

    analytics.track('another_event');
    expect(analytics.getEvents()[1]).toEqual(
      expect.objectContaining({
        type: 'another_event',
        userId,
      })
    );
  });

  it('should reset the session and clear user data', () => {
    const oldSessionId = (analytics as any).sessionId;
    analytics.identify('user-to-reset');
    analytics.track('event_before_reset');

    analytics.reset();

    const newSessionId = (analytics as any).sessionId;
    expect(newSessionId).not.toBe(oldSessionId);
    expect((analytics as any).userId).toBeUndefined();
    expect(analytics.getEvents()).toHaveLength(0);
  });

  it('should update config and restart flush timer', () => {
    const newInterval = 5000;
    analytics.updateConfig({ flushInterval: newInterval });

    // Clear previous timer calls
    jest.clearAllTimers();

    analytics.track('event');
    jest.advanceTimersByTime(newInterval - 1);
    expect(analytics.sendEventsMock).not.toHaveBeenCalled();

    jest.advanceTimersByTime(1);
    expect(analytics.sendEventsMock).toHaveBeenCalledTimes(1);
  });
});
