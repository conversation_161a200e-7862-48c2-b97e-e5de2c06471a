import * as fs from 'fs/promises';
import * as https from 'https';
import { Writable } from 'stream';

import { app } from 'electron';

import { AnalyticsConfig } from '../AnalyticsCore';
import { MainAnalyticsManager } from '../MainAnalyticsManager';

// Mocking Electron and Node.js modules
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn(name => `/mock/path/to/${name}`),
    getVersion: jest.fn(() => '1.0.0'),
    getLocale: jest.fn(() => 'en-US'),
    on: jest.fn(),
  },
}));

jest.mock('fs/promises');
jest.mock('https');

describe('MainAnalyticsManager', () => {
  let analytics: MainAnalyticsManager;
  const config: Partial<AnalyticsConfig> = {
    endpoint: 'https://test.com/analytics',
    debug: false,
  };

  beforeEach(async () => {
    // Reset mocks before each test
    (fs.readFile as jest.Mock).mockRejectedValue(new Error('File not found'));
    (fs.writeFile as jest.Mock).mockResolvedValue(undefined);

    analytics = MainAnalyticsManager.getInstance(config);
    await analytics.initialize();
  });

  afterEach(() => {
    analytics.cleanup();
    jest.clearAllMocks();
    (MainAnalyticsManager as any).instance = null;
  });

  it('should be a singleton', () => {
    const instance1 = MainAnalyticsManager.getInstance();
    const instance2 = MainAnalyticsManager.getInstance();
    expect(instance1).toBe(instance2);
  });

  it('should generate and save a new device ID if one does not exist', async () => {
    // Initialize creates a new manager, which should trigger this logic
    expect(fs.readFile).toHaveBeenCalledWith('/mock/path/to/userData/device.id', 'utf-8');
    expect(fs.writeFile).toHaveBeenCalledWith(
      '/mock/path/to/userData/device.id',
      expect.any(String),
      'utf-8'
    );
  });

  it('should load an existing device ID', async () => {
    (fs.readFile as jest.Mock).mockImplementation(path => {
      if (path.endsWith('device.id')) {
        return Promise.resolve('existing-device-id');
      }
      return Promise.reject(new Error('File not found'));
    });

    // Re-initialize to test loading logic
    (MainAnalyticsManager as any).instance = null;
    const newAnalytics = MainAnalyticsManager.getInstance(config);
    await newAnalytics.initialize();

    expect(newAnalytics.getDeviceId()).toBe('existing-device-id');
    expect(fs.writeFile).not.toHaveBeenCalledWith(
      expect.stringContaining('device.id'),
      expect.any(String),
      'utf-8'
    );
  });

  it('should load and apply a saved config', async () => {
    const savedConfig = { endpoint: 'https://saved.com', batchSize: 10 };
    (fs.readFile as jest.Mock).mockImplementation(path => {
      if (path.endsWith('analytics.config.json')) {
        return Promise.resolve(JSON.stringify(savedConfig));
      }
      return Promise.reject(new Error('File not found'));
    });

    (MainAnalyticsManager as any).instance = null;
    const newAnalytics = MainAnalyticsManager.getInstance(config);
    await newAnalytics.initialize();

    const currentConfig = (newAnalytics as any).config;
    expect(currentConfig.endpoint).toBe('https://saved.com');
    expect(currentConfig.batchSize).toBe(10);
  });

  it('should send events using Node.js https module', async () => {
    const mockRequest = new Writable({
      write(chunk, encoding, callback) {
        callback();
      },
    }) as any;
    mockRequest.end = jest.fn().mockReturnThis();
    mockRequest.on = jest.fn().mockReturnThis();

    (https.request as jest.Mock).mockImplementation((options, callback) => {
      // Immediately invoke the callback with a mock response
      const mockResponse = new Writable() as any;
      mockResponse.statusCode = 200;
      callback(mockResponse);

      // Simulate the 'end' event on the response to resolve the promise
      setTimeout(() => mockResponse.emit('end'), 0);

      return mockRequest;
    });

    analytics.track('main_process_event');
    await analytics.flush();

    expect(https.request).toHaveBeenCalledTimes(1);
    const requestOptions = (https.request as jest.Mock).mock.calls[0][0];
    expect(requestOptions.hostname).toBe('test.com');
    expect(requestOptions.method).toBe('POST');
  });

  it('should track app lifecycle events', () => {
    const trackSpy = jest.spyOn(analytics, 'track');

    // Find the 'before-quit' listener and execute it
    const beforeQuitCallback = (app.on as jest.Mock).mock.calls.find(
      call => call[0] === 'before-quit'
    )[1];

    beforeQuitCallback();

    expect(trackSpy).toHaveBeenCalledWith('app_quit', {});
  });
});
