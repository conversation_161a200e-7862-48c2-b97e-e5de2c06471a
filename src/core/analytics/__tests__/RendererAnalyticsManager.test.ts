import { AnalyticsConfig } from '../AnalyticsCore';
import { RendererAnalyticsManager } from '../RendererAnalyticsManager';

// Mocking global browser objects
const mockFetch = jest.fn(() =>
  Promise.resolve(new Response(null, { status: 200, statusText: 'OK' }))
);
global.fetch = mockFetch as jest.Mock;

Object.defineProperty(window, 'location', {
  value: { href: 'http://test.com', origin: 'http://test.com' },
  writable: true,
});
Object.defineProperty(document, 'title', {
  value: 'Test Page',
  writable: true,
});
Object.defineProperty(document, 'referrer', {
  value: 'http://referrer.com',
  writable: true,
});
Object.defineProperty(navigator, 'userAgent', {
  value: 'TestAgent/1.0',
  writable: true,
});
Object.defineProperty(navigator, 'language', {
  value: 'en-US',
  writable: true,
});

describe('RendererAnalyticsManager', () => {
  let analytics: RendererAnalyticsManager;
  const config: Partial<AnalyticsConfig> = {
    endpoint: 'http://test.com/analytics',
    debug: false,
  };

  beforeEach(() => {
    // We need to get a new instance for each test to reset its state
    analytics = RendererAnalyticsManager.getInstance(config);
    analytics.initialize();
  });

  afterEach(() => {
    analytics.cleanup();
    mockFetch.mockClear();
    // This is a bit of a hack to reset the singleton instance
    (RendererAnalyticsManager as any).instance = null;
  });

  it('should be a singleton', () => {
    const instance1 = RendererAnalyticsManager.getInstance();
    const instance2 = RendererAnalyticsManager.getInstance();
    expect(instance1).toBe(instance2);
  });

  it('should gather correct device and page info', () => {
    const commonProps = (analytics as any).getCommonProperties();
    expect(commonProps).toMatchObject({
      userAgent: 'TestAgent/1.0',
      language: 'en-US',
      url: 'http://test.com/', // Note: window.location.href adds a trailing slash
      title: 'Test Page',
      referrer: 'http://referrer.com',
    });
  });

  it('should track a page view automatically on load', () => {
    const trackSpy = jest.spyOn(analytics, 'track');

    // Simulate the load event
    window.dispatchEvent(new Event('load'));

    expect(trackSpy).toHaveBeenCalledWith('page_view', expect.any(Object));
  });

  it('should send events using fetch', async () => {
    analytics.track('test_event');
    await analytics.flush();

    expect(mockFetch).toHaveBeenCalledTimes(1);

    // Type-safe access to mock call arguments, using the recommended 'unknown' cast
    const call = mockFetch.mock.calls[0] as unknown as [string, RequestInit];
    expect(call).toBeDefined();

    const [url, options] = call;
    expect(url).toBe(config.endpoint);
    expect(options).toBeDefined();
    expect(options.method).toBe('POST');
    expect(options.headers).toEqual({ 'Content-Type': 'application/json' });
    expect(options.body).toBeDefined();

    const body = JSON.parse(options.body as string);
    expect(body.events).toHaveLength(1);
    expect(body.events[0].type).toBe('test_event');
  });

  it('should track clicks on elements with data-analytics-track attribute', () => {
    const trackSpy = jest.spyOn(analytics, 'track');

    // Setup a DOM element
    document.body.innerHTML = `
      <div data-analytics-track>
        <button id="test-button">Click Me</button>
      </div>
    `;

    const button = document.getElementById('test-button');
    button?.click();

    expect(trackSpy).toHaveBeenCalledWith(
      'click',
      expect.objectContaining({
        elementId: 'test-button',
        elementTag: 'BUTTON',
      })
    );
  });

  it('should not track clicks on elements without the attribute', () => {
    const trackSpy = jest.spyOn(analytics, 'track');

    document.body.innerHTML = `<button id="other-button">No Track</button>`;

    const button = document.getElementById('other-button');
    button?.click();

    // track is called for other reasons (e.g. identify), so we check it wasn't called with 'click'
    const clickCall = trackSpy.mock.calls.find(call => call[0] === 'click');
    expect(clickCall).toBeUndefined();
  });

  it('should track form submissions', () => {
    const trackSpy = jest.spyOn(analytics, 'track');

    document.body.innerHTML = `
        <form id="test-form" action="/submit">
            <input type="text" />
            <button type="submit">Submit</button>
        </form>
    `;

    const form = document.getElementById('test-form') as HTMLFormElement;
    form.dispatchEvent(new Event('submit'));

    expect(trackSpy).toHaveBeenCalledWith(
      'form_submit',
      expect.objectContaining({
        formId: 'test-form',
        formAction: expect.stringContaining('/submit'),
      })
    );
  });
});
