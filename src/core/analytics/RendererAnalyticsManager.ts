import { AnalyticsConfig, AnalyticsCore, AnalyticsEvent } from './AnalyticsCore';

/**
 * Device information collected from the browser environment.
 */
export interface DeviceInfo {
  userAgent: string;
  platform: string;
  language: string;
  screenWidth: number;
  screenHeight: number;
  colorDepth: number;
  timezone: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
}

/**
 * Page information collected from the browser environment.
 */
export interface PageInfo {
  url: string;
  title: string;
  referrer: string;
  viewportWidth: number;
  viewportHeight: number;
}

/**
 * Analytics manager for the renderer process.
 * Extends AnalyticsCore with browser-specific functionality.
 */
export class RendererAnalyticsManager extends AnalyticsCore {
  private static instance: RendererAnalyticsManager;
  private deviceInfo: DeviceInfo;
  private pageInfo: PageInfo;

  private constructor(config: Partial<AnalyticsConfig> = {}) {
    super(config);
    this.deviceInfo = this.gatherDeviceInfo();
    this.pageInfo = this.gatherPageInfo();
  }

  /**
   * Gets the singleton instance of the RendererAnalyticsManager.
   */
  public static getInstance(config?: Partial<AnalyticsConfig>): RendererAnalyticsManager {
    if (!RendererAnalyticsManager.instance) {
      RendererAnalyticsManager.instance = new RendererAnalyticsManager(config);
    }
    return RendererAnalyticsManager.instance;
  }

  /**
   * Initializes the manager and sets up automatic event listeners.
   */
  public initialize(): void {
    super.initialize();
    if (this.config.enabled) {
      this.setupEventListeners();
      this.log('RendererAnalyticsManager initialized.');
    }
  }

  /**
   * Tracks a page view event.
   */
  public trackPageView(): void {
    this.pageInfo = this.gatherPageInfo(); // Update page info on each view
    this.track('page_view', {
      url: this.pageInfo.url,
      title: this.pageInfo.title,
      referrer: this.pageInfo.referrer,
    });
  }

  /**
   * Implements the event sending logic using the browser's fetch API.
   */
  protected async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        events,
        sentAt: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send analytics events: ${response.statusText}`);
    }
    this.log(`${events.length} events sent to endpoint.`);
  }

  /**
   * Gathers common properties from the browser environment.
   */
  protected getCommonProperties(): Record<string, any> {
    return {
      ...this.deviceInfo,
      ...this.pageInfo,
      doNotTrack: this.getDoNotTrack(),
    };
  }

  /**
   * Sets up automatic event listeners for common user interactions.
   */
  private setupEventListeners(): void {
    // Page View Tracking on load and on history changes
    window.addEventListener('load', () => this.trackPageView());
    window.addEventListener('popstate', () => this.trackPageView());

    // Click Tracking
    document.addEventListener(
      'click',
      event => {
        const target = event.target as HTMLElement;
        if (target?.closest('[data-analytics-track]')) {
          this.trackClick(target);
        }
      },
      true
    ); // Use capture phase to get all clicks

    // Form Submission Tracking
    document.addEventListener(
      'submit',
      event => {
        const form = event.target as HTMLFormElement;
        if (form) {
          this.trackFormSubmission(form);
        }
      },
      true
    );
  }

  private trackClick(element: HTMLElement): void {
    const properties = {
      elementId: element.id,
      elementClass: element.className,
      elementTag: element.tagName,
      elementText: element.textContent?.trim().substring(0, 200), // Limit text length
    };
    this.track('click', properties);
  }

  private trackFormSubmission(form: HTMLFormElement): void {
    const properties = {
      formId: form.id,
      formAction: form.action,
      formMethod: form.method,
    };
    this.track('form_submit', properties);
  }

  private gatherDeviceInfo(): DeviceInfo {
    const userAgent = navigator.userAgent;
    let deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop';
    if (/Mobile|Android|iPhone|iPad|iPod/i.test(userAgent)) {
      deviceType = /iPad/i.test(userAgent) ? 'tablet' : 'mobile';
    }

    return {
      userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      colorDepth: window.screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      deviceType,
    };
  }

  private gatherPageInfo(): PageInfo {
    return {
      url: window.location.href,
      title: document.title,
      referrer: document.referrer,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
    };
  }

  private getDoNotTrack(): boolean {
    // navigator.doNotTrack is deprecated but still worth checking
    const dnt = navigator.doNotTrack || (window as any).doNotTrack;
    return dnt === '1' || dnt === 'yes';
  }
}
