import { EventEmitter } from 'events';

import { v4 as uuidv4 } from 'uuid';

/**
 * Unified analytics event structure.
 */
export interface AnalyticsEvent {
  id: string;
  timestamp: number;
  type: string;
  properties: Record<string, any>;
  sessionId: string;
  userId?: string;
}

/**
 * Unified and simplified configuration structure.
 */
export interface AnalyticsConfig {
  enabled: boolean;
  endpoint: string;
  flushInterval: number;
  batchSize: number;
  debug: boolean;
}

/**
 * Core analytics class with environment-agnostic logic.
 * Handles event queuing, batching, and flushing.
 */
export abstract class AnalyticsCore extends EventEmitter {
  protected config: AnalyticsConfig;
  protected events: AnalyticsEvent[] = [];
  protected sessionId: string;
  protected userId?: string;
  private flushTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<AnalyticsConfig>) {
    super();
    this.config = {
      enabled: true,
      endpoint: '/api/analytics',
      flushInterval: 30000, // 30 seconds
      batchSize: 50,
      debug: false,
      ...config,
    };
    this.sessionId = this.generateSessionId();
  }

  /**
   * Initializes the analytics core, starts the flush interval.
   */
  public initialize(): void {
    if (!this.config.enabled) {
      this.log('Analytics is disabled.');
      return;
    }
    this.startFlushTimer();
    this.log('AnalyticsCore initialized.');
  }

  /**
   * Tracks a new event.
   * @param type - The type of the event (e.g., 'page_view', 'click').
   * @param properties - Additional data associated with the event.
   */
  public track(type: string, properties: Record<string, any> = {}): void {
    if (!this.config.enabled) return;

    const event: AnalyticsEvent = {
      id: uuidv4(),
      type,
      properties: {
        ...this.getCommonProperties(),
        ...properties,
      },
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId,
    };

    this.events.push(event);
    this.emit('event', event);
    this.log(`Event tracked: ${type}`, event);

    if (this.events.length >= this.config.batchSize) {
      this.flush();
    }
  }

  /**
   * Identifies a user.
   * @param userId - A unique identifier for the user.
   * @param traits - User properties.
   */
  public identify(userId: string, traits: Record<string, any> = {}): void {
    this.userId = userId;
    this.track('identify', traits);
    this.emit('identify', { userId, traits });
  }

  /**
   * Resets the user identification and session.
   */
  public reset(): void {
    this.userId = undefined;
    this.sessionId = this.generateSessionId();
    this.events = [];
    this.log('Analytics session reset.');
  }

  /**
   * Flushes the event queue, sending all pending events to the endpoint.
   */
  public async flush(): Promise<void> {
    if (this.events.length === 0) {
      return;
    }

    const eventsToFlush = [...this.events];
    this.events = [];

    this.log(`Flushing ${eventsToFlush.length} events...`);

    try {
      await this.sendEvents(eventsToFlush);
      this.emit('flush', { success: true, count: eventsToFlush.length });
      this.log(`${eventsToFlush.length} events flushed successfully.`);
    } catch (error) {
      this.emit('flush', { success: false, error, count: eventsToFlush.length });
      this.log('Failed to flush events. Re-queuing.', error);
      // Re-queue failed events
      this.events.unshift(...eventsToFlush);
    }
  }

  /**
   * Updates the configuration.
   * @param newConfig - The new configuration properties to merge.
   */
  public updateConfig(newConfig: Partial<AnalyticsConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated.', this.config);
    // Restart flush timer with new interval
    this.stopFlushTimer();
    this.startFlushTimer();
  }

  /**
   * Cleans up resources, like stopping timers.
   */
  public cleanup(): void {
    this.stopFlushTimer();
    this.flush();
    this.emit('cleanup');
    this.log('AnalyticsCore cleaned up.');
  }

  /**
   * Abstract method to be implemented by subclasses to send events.
   * @param events - The array of events to send.
   */
  protected abstract sendEvents(events: AnalyticsEvent[]): Promise<void>;

  /**
   * Abstract method for subclasses to provide common, environment-specific properties.
   */
  protected abstract getCommonProperties(): Record<string, any>;

  private startFlushTimer(): void {
    if (this.flushTimer) {
      this.stopFlushTimer();
    }
    this.flushTimer = setInterval(() => this.flush(), this.config.flushInterval);
    this.log(`Flush timer started with interval ${this.config.flushInterval}ms.`);
  }

  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
      this.log('Flush timer stopped.');
    }
  }

  private generateSessionId(): string {
    return uuidv4();
  }

  protected log(message: string, ...data: any[]): void {
    if (this.config.debug) {
      console.log(`[AnalyticsCore] ${message}`, ...data);
    }
  }
}
