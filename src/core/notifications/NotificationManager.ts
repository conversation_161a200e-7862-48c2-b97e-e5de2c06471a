import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import { join } from 'path';
import * as path from 'path';

import { Notification, app } from 'electron';

import { logger } from '../../logging/Logger';
import { errorManager } from '../ErrorManager';

// Enhanced notification interfaces combining both managers
export interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'low' | 'normal' | 'high';
  timeout?: number;
  actions?: NotificationAction[];
  category?: string;
  priority?: number;
  tag?: string;
  onClick?: () => void;
  onClose?: () => void;
}

export interface NotificationAction {
  type: 'button' | 'text';
  text: string;
  label: string;
  action?: () => void;
}

export interface NotificationWithHandlers extends Notification {
  onClick?: () => void;
  onClose?: () => void;
  tag?: string;
}

export type NotificationPosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
export type NotificationTheme = 'light' | 'dark' | 'auto';
export type NotificationAnimation = 'slide' | 'fade' | 'bounce' | 'none';
export type NotificationSound = 'default' | 'success' | 'error' | 'warning' | 'info' | 'none';
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'custom';

export interface NotificationBehavior {
  autoClose: boolean;
  autoCloseDelay: number;
  clickToClose: boolean;
  pauseOnHover: boolean;
  showProgress: boolean;
  stackable: boolean;
}

export interface NotificationLayout {
  width: number;
  height: number;
  margin: number;
  padding: number;
  borderRadius: number;
}

export interface NotificationAccessibility {
  screenReader: boolean;
  highContrast: boolean;
  reducedMotion: boolean;
  keyboardNavigation: boolean;
}

export interface NotificationConfig {
  maxNotifications: number;
  maxGroups: number;
  autoClose: boolean;
  autoCloseDelay: number;
  position: NotificationPosition;
  sound: NotificationSound | boolean;
  desktop: boolean;
  theme: NotificationTheme;
  animation: NotificationAnimation;
  behavior: NotificationBehavior;
  layout: NotificationLayout;
  accessibility: NotificationAccessibility;
}

// Legacy interface for backward compatibility
interface LegacyNotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'low' | 'normal' | 'high';
  timeout?: number;
  actions?: NotificationAction[];
  category?: string;
  priority?: number;
}

export interface NotificationSettings {
  enabled: boolean;
  maxNotifications: number;
  sound: boolean;
  desktop: boolean;
  categories: {
    [key: string]: {
      enabled: boolean;
      sound: boolean;
      priority: NotificationPriority;
    };
  };
}

export interface NotificationGroup {
  id: string;
  name: string;
  notifications: string[];
  collapsed: boolean;
  priority: NotificationPriority;
}

interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  duration: number;
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxNotifications: number;
  categories: {
    [key: string]: {
      enabled: boolean;
      sound: boolean;
      priority: number;
    };
  };
}

export class NotificationManager {
  private static instance: NotificationManager;
  private config: NotificationConfig;
  private eventEmitter: EventEmitter;
  private notifications: Notification[];
  private groups: NotificationGroup[];
  private settings: NotificationSettings;
  private activeNotifications: Map<string, Notification>;
  private notificationHistory: NotificationOptions[];
  private maxHistory: number;

  private constructor() {
    this.eventEmitter = new EventEmitter();
    this.notifications = [];
    this.groups = [];
    this.settings = {
      enabled: true,
      sound: true,
      desktop: true,
      duration: 5000,
      position: 'top-right',
      maxNotifications: 5,
      categories: {
        default: {
          enabled: true,
          sound: true,
          priority: 0,
        },
      },
    };
    this.activeNotifications = new Map();
    this.notificationHistory = [];
    this.maxHistory = 100;

    this.config = {
      maxNotifications: 100,
      maxGroups: 10,
      autoClose: true,
      autoCloseDelay: 5000,
      position: 'top-right',
      sound: true,
      desktop: true,
      theme: {
        background: '#ffffff',
        text: '#000000',
        border: '#e0e0e0',
        shadow: '0 2px 4px rgba(0,0,0,0.1)',
        borderRadius: 8,
        icon: {
          color: '#2196f3',
          background: '#ffffff',
          size: 24,
          borderRadius: 4,
        },
      },
      animation: {
        initial: { opacity: 0, y: -20 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -20 },
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 30,
        },
      },
      behavior: 'stack',
      layout: 'stack',
      accessibility: 'default',
    };

    this.loadNotifications();
    this.loadGroups();
  }

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadSettings();
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'notification-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      // If settings don't exist, use defaults
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'notification-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  // Загрузка уведомлений из хранилища
  private async loadNotifications(): Promise<void> {
    try {
      const persisted = localStorage.getItem('novabrowser_notifications');
      if (persisted) {
        this.notifications = JSON.parse(persisted);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  }

  // Загрузка групп из хранилища
  private async loadGroups(): Promise<void> {
    try {
      const persisted = localStorage.getItem('novabrowser_notification_groups');
      if (persisted) {
        this.groups = JSON.parse(persisted);
      }
    } catch (error) {
      console.error('Error loading notification groups:', error);
    }
  }

  // Сохранение уведомлений в хранилище
  private async persistNotifications(): Promise<void> {
    try {
      localStorage.setItem('novabrowser_notifications', JSON.stringify(this.notifications));
    } catch (error) {
      console.error('Error persisting notifications:', error);
    }
  }

  // Сохранение групп в хранилище
  private async persistGroups(): Promise<void> {
    try {
      localStorage.setItem('novabrowser_notification_groups', JSON.stringify(this.groups));
    } catch (error) {
      console.error('Error persisting notification groups:', error);
    }
  }

  // Создание нового уведомления
  create(notification: Omit<Notification, 'id' | 'read'>): string {
    const id = Math.random().toString(36).substring(2);
    const newNotification: Notification = {
      ...notification,
      id,
      read: false,
      theme: notification.theme || this.config.theme,
      animation: notification.animation || this.config.animation,
      behavior: notification.behavior || this.config.behavior,
      layout: notification.layout || this.config.layout,
      accessibility: notification.accessibility || this.config.accessibility,
    };

    this.notifications.unshift(newNotification);

    // Ограничение количества уведомлений
    if (this.notifications.length > this.config.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.config.maxNotifications);
    }

    this.eventEmitter.emit('notificationCreated', newNotification);
    this.persistNotifications();

    // Автоматическое закрытие
    if (this.config.autoClose) {
      setTimeout(() => {
        this.dismiss(id);
      }, this.config.autoCloseDelay);
    }

    // Воспроизведение звука
    if (this.config.sound) {
      this.playNotificationSound(this.config.sound);
    }

    // Десктопное уведомление
    if (this.config.desktop && 'Notification' in window) {
      this.showDesktopNotification(newNotification);
    }

    return id;
  }

  // Создание новой группы уведомлений
  createGroup(group: Omit<NotificationGroup, 'id'>): string {
    const id = Math.random().toString(36).substring(2);
    const newGroup: NotificationGroup = {
      ...group,
      id,
      theme: group.theme || this.config.theme,
      animation: group.animation || this.config.animation,
      behavior: group.behavior || this.config.behavior,
      layout: group.layout || this.config.layout,
      accessibility: group.accessibility || this.config.accessibility,
    };

    this.groups.unshift(newGroup);

    // Ограничение количества групп
    if (this.groups.length > this.config.maxGroups) {
      this.groups = this.groups.slice(0, this.config.maxGroups);
    }

    this.eventEmitter.emit('groupCreated', newGroup);
    this.persistGroups();

    return id;
  }

  // Отметка уведомления как прочитанного
  markAsRead(id: string): void {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      notification.read = true;
      this.eventEmitter.emit('notificationRead', notification);
      this.persistNotifications();
    }
  }

  // Отметка всех уведомлений как прочитанных
  markAllAsRead(): void {
    this.notifications.forEach(notification => {
      notification.read = true;
    });
    this.eventEmitter.emit('allNotificationsRead');
    this.persistNotifications();
  }

  // Удаление уведомления
  dismiss(id: string): void {
    const index = this.notifications.findIndex(n => n.id === id);
    if (index !== -1) {
      const notification = this.notifications[index];
      this.notifications.splice(index, 1);
      this.eventEmitter.emit('notificationDismissed', notification);
      this.persistNotifications();
    }
  }

  // Удаление группы уведомлений
  dismissGroup(id: string): void {
    const index = this.groups.findIndex(g => g.id === id);
    if (index !== -1) {
      const group = this.groups[index];
      this.groups.splice(index, 1);
      this.eventEmitter.emit('groupDismissed', group);
      this.persistGroups();
    }
  }

  // Очистка всех уведомлений
  clear(): void {
    this.notifications = [];
    this.eventEmitter.emit('notificationsCleared');
    this.persistNotifications();
  }

  // Очистка всех групп
  clearGroups(): void {
    this.groups = [];
    this.eventEmitter.emit('groupsCleared');
    this.persistGroups();
  }

  // Получение всех уведомлений
  getAll(): Notification[] {
    return [...this.notifications];
  }

  // Получение всех групп
  getAllGroups(): NotificationGroup[] {
    return [...this.groups];
  }

  // Получение непрочитанных уведомлений
  getUnread(): Notification[] {
    return this.notifications.filter(n => !n.read);
  }

  // Воспроизведение звука уведомления
  private playNotificationSound(sound: NotificationSound | boolean): void {
    if (typeof sound === 'boolean') {
      if (sound) {
        const audio = new Audio('/sounds/notification.mp3');
        audio.play().catch(error => {
          console.error('Error playing notification sound:', error);
        });
      }
    } else if (sound.actionSound) {
      const audio = new Audio(sound.actionSound);
      audio.play().catch(error => {
        console.error('Error playing notification sound:', error);
      });
    }
  }

  // Показ десктопного уведомления
  private async showDesktopNotification(notification: Notification): Promise<void> {
    if (!('Notification' in window)) return;

    try {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        new window.Notification(notification.title, {
          body: notification.message,
          icon:
            typeof notification.icon === 'string' ? notification.icon : '/icons/notification.png',
          badge: typeof notification.icon === 'string' ? notification.icon : undefined,
          tag: notification.id,
        });
      }
    } catch (error) {
      console.error('Error showing desktop notification:', error);
    }
  }

  // Подписка на события уведомлений
  onNotificationEvent(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  // Обновление конфигурации
  updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.eventEmitter.emit('configUpdated', this.config);
  }

  public async show(options: NotificationOptions): Promise<void> {
    if (!this.settings.enabled) return;

    const category = options.category || 'default';
    const categorySettings = this.settings.categories[category];

    if (!categorySettings?.enabled) return;

    // Check if we've reached the maximum number of notifications
    if (this.activeNotifications.size >= this.settings.maxNotifications) {
      this.removeOldestNotification();
    }

    const notification = new Notification({
      title: options.title,
      body: options.body,
      icon: options.icon,
      silent: !this.settings.sound || !categorySettings.sound,
      urgency: options.urgency || 'normal',
      timeoutType: 'default',
      actions: options.actions?.map(action => ({
        type: action.type,
        text: action.text,
      })),
    });

    const id = Math.random().toString(36).substr(2, 9);
    this.activeNotifications.set(id, notification);

    // Add to history
    this.notificationHistory.unshift(options);
    if (this.notificationHistory.length > this.maxHistory) {
      this.notificationHistory.pop();
    }

    // Set up event listeners
    notification.on('click', () => {
      this.handleNotificationClick(id, options);
    });

    notification.on('close', () => {
      this.activeNotifications.delete(id);
    });

    // Show the notification
    notification.show();

    // Auto-close after timeout
    if (options.timeout) {
      setTimeout(() => {
        notification.close();
      }, options.timeout);
    }
  }

  private removeOldestNotification(): void {
    const oldestId = this.activeNotifications.keys().next().value;
    if (oldestId) {
      const notification = this.activeNotifications.get(oldestId);
      if (notification) {
        notification.close();
        this.activeNotifications.delete(oldestId);
      }
    }
  }

  private handleNotificationClick(id: string, options: NotificationOptions): void {
    const notification = this.activeNotifications.get(id);
    if (notification) {
      // Handle click action
      if (options.actions) {
        const defaultAction = options.actions.find(a => a.type === 'button');
        if (defaultAction) {
          defaultAction.action();
        }
      }
    }
  }

  public getHistory(): NotificationOptions[] {
    return [...this.notificationHistory];
  }

  public clearHistory(): void {
    this.notificationHistory = [];
  }

  public getSettings(): NotificationSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<NotificationSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
  }

  public async enableCategory(category: string): Promise<void> {
    if (!this.settings.categories[category]) {
      this.settings.categories[category] = {
        enabled: true,
        sound: true,
        priority: 0,
      };
    } else {
      this.settings.categories[category].enabled = true;
    }
    await this.saveSettings();
  }

  public async disableCategory(category: string): Promise<void> {
    if (this.settings.categories[category]) {
      this.settings.categories[category].enabled = false;
      await this.saveSettings();
    }
  }

  // Enhanced methods from second NotificationManager
  public async showNotification(options: NotificationOptions): Promise<void> {
    if (!this.settings.enabled) return;

    const category = options.category || 'default';
    const categorySettings = this.settings.categories[category];

    if (!categorySettings?.enabled) return;

    // Check if we've reached the maximum number of notifications
    if (this.activeNotifications.size >= this.settings.maxNotifications) {
      this.removeOldestNotification();
    }

    try {
      const notification = new Notification({
        title: options.title,
        body: options.body,
        icon: options.icon || join(app.getPath('userData'), 'icons', 'notification.png'),
        silent: options.silent || !this.settings.sound || !categorySettings.sound,
        urgency: options.urgency || 'normal',
        actions: options.actions?.map(action => ({
          type: 'button',
          text: action.label,
        })),
        timeoutType: 'default',
      }) as NotificationWithHandlers;

      if (options.tag) {
        notification.onClick = options.onClick;
        notification.onClose = options.onClose;
        notification.tag = options.tag;
        this.activeNotifications.set(options.tag, notification);
      }

      this.emit('notification-shown', options);
      logger.info('Notification shown:', options.title);
    } catch (error) {
      logger.error('Failed to show notification:', error);
      errorManager.handleError(error as Error, {
        component: 'NotificationManager',
        method: 'showNotification',
        options,
      });
    }
  }

  private removeOldestNotification(): void {
    const oldest = Array.from(this.activeNotifications.entries())[0];
    if (oldest) {
      const [tag, notification] = oldest;
      notification.close();
      this.activeNotifications.delete(tag);
    }
  }

  public closeNotification(tag: string): void {
    const notification = this.activeNotifications.get(tag);
    if (notification) {
      notification.close();
      this.activeNotifications.delete(tag);
    }
  }

  public closeAllNotifications(): void {
    this.activeNotifications.forEach(notification => notification.close());
    this.activeNotifications.clear();
  }

  public getActiveNotifications(): NotificationWithHandlers[] {
    return Array.from(this.activeNotifications.values());
  }

  // Legacy method for backward compatibility
  public async show(options: LegacyNotificationOptions): Promise<void> {
    return this.showNotification(options as NotificationOptions);
  }

  public cleanup(): void {
    this.activeNotifications.forEach(notification => {
      notification.close();
    });
    this.activeNotifications.clear();
    this.notificationGroups.clear();
    this.notificationHistory = [];
  }
}

// Создание синглтона
export const notificationManager = NotificationManager.getInstance();
