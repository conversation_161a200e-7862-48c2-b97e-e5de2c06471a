/**
 * Microservices Communication Bus
 * Handles inter-service communication, message routing, and service discovery
 */

import { EventEmitter } from 'events';
import { logger } from './EnhancedLogger';

export interface ServiceMessage {
  id: string;
  from: string;
  to: string;
  type: 'request' | 'response' | 'event' | 'broadcast';
  action: string;
  payload: any;
  timestamp: Date;
  correlationId?: string;
  replyTo?: string;
  timeout?: number;
  priority?: 'low' | 'normal' | 'high' | 'critical';
  metadata?: Record<string, any>;
}

export interface ServiceEndpoint {
  serviceName: string;
  version: string;
  capabilities: string[];
  healthEndpoint?: string;
  metadata: Record<string, any>;
  lastSeen: Date;
}

export interface MessageHandler {
  (message: ServiceMessage): Promise<any> | any;
}

export interface ServiceDiscovery {
  registerService(endpoint: ServiceEndpoint): Promise<void>;
  unregisterService(serviceName: string): Promise<void>;
  discoverServices(capability?: string): Promise<ServiceEndpoint[]>;
  getService(serviceName: string): Promise<ServiceEndpoint | null>;
}

export class MicroservicesBus extends EventEmitter {
  private handlers = new Map<string, Map<string, MessageHandler>>();
  private services = new Map<string, ServiceEndpoint>();
  private pendingRequests = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  private messageQueue: ServiceMessage[] = [];
  private processing = false;

  constructor() {
    super();
    this.startMessageProcessing();
  }

  /**
   * Register a service with the bus
   */
  async registerService(endpoint: ServiceEndpoint): Promise<void> {
    this.services.set(endpoint.serviceName, {
      ...endpoint,
      lastSeen: new Date(),
    });

    logger.info(`Service registered: ${endpoint.serviceName}`, {
      version: endpoint.version,
      capabilities: endpoint.capabilities,
    });

    this.emit('service-registered', endpoint);
  }

  /**
   * Unregister a service from the bus
   */
  async unregisterService(serviceName: string): Promise<void> {
    this.services.delete(serviceName);
    this.handlers.delete(serviceName);

    logger.info(`Service unregistered: ${serviceName}`);
    this.emit('service-unregistered', { serviceName });
  }

  /**
   * Discover services by capability
   */
  async discoverServices(capability?: string): Promise<ServiceEndpoint[]> {
    const services = Array.from(this.services.values());
    
    if (!capability) {
      return services;
    }

    return services.filter(service => 
      service.capabilities.includes(capability)
    );
  }

  /**
   * Get a specific service
   */
  async getService(serviceName: string): Promise<ServiceEndpoint | null> {
    return this.services.get(serviceName) || null;
  }

  /**
   * Register a message handler for a service
   */
  registerHandler(serviceName: string, action: string, handler: MessageHandler): void {
    if (!this.handlers.has(serviceName)) {
      this.handlers.set(serviceName, new Map());
    }

    this.handlers.get(serviceName)!.set(action, handler);
    logger.debug(`Handler registered: ${serviceName}.${action}`);
  }

  /**
   * Unregister a message handler
   */
  unregisterHandler(serviceName: string, action: string): void {
    const serviceHandlers = this.handlers.get(serviceName);
    if (serviceHandlers) {
      serviceHandlers.delete(action);
      if (serviceHandlers.size === 0) {
        this.handlers.delete(serviceName);
      }
    }
  }

  /**
   * Send a request to a service and wait for response
   */
  async request<T = any>(
    to: string,
    action: string,
    payload: any,
    options: {
      timeout?: number;
      priority?: 'low' | 'normal' | 'high' | 'critical';
      metadata?: Record<string, any>;
    } = {}
  ): Promise<T> {
    const message: ServiceMessage = {
      id: this.generateMessageId(),
      from: 'system',
      to,
      type: 'request',
      action,
      payload,
      timestamp: new Date(),
      timeout: options.timeout || 30000,
      priority: options.priority || 'normal',
      metadata: options.metadata,
    };

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(message.id);
        reject(new Error(`Request timeout: ${to}.${action}`));
      }, message.timeout);

      this.pendingRequests.set(message.id, {
        resolve,
        reject,
        timeout,
      });

      this.sendMessage(message);
    });
  }

  /**
   * Send a response to a request
   */
  async respond(originalMessage: ServiceMessage, payload: any): Promise<void> {
    const response: ServiceMessage = {
      id: this.generateMessageId(),
      from: originalMessage.to,
      to: originalMessage.from,
      type: 'response',
      action: originalMessage.action,
      payload,
      timestamp: new Date(),
      correlationId: originalMessage.id,
    };

    this.sendMessage(response);
  }

  /**
   * Send an event to a service
   */
  async sendEvent(to: string, action: string, payload: any): Promise<void> {
    const message: ServiceMessage = {
      id: this.generateMessageId(),
      from: 'system',
      to,
      type: 'event',
      action,
      payload,
      timestamp: new Date(),
    };

    this.sendMessage(message);
  }

  /**
   * Broadcast a message to all services
   */
  async broadcast(action: string, payload: any): Promise<void> {
    const message: ServiceMessage = {
      id: this.generateMessageId(),
      from: 'system',
      to: '*',
      type: 'broadcast',
      action,
      payload,
      timestamp: new Date(),
    };

    this.sendMessage(message);
  }

  /**
   * Send a message through the bus
   */
  private sendMessage(message: ServiceMessage): void {
    this.messageQueue.push(message);
    this.emit('message-queued', message);
  }

  /**
   * Process messages in the queue
   */
  private async startMessageProcessing(): Promise<void> {
    if (this.processing) return;
    this.processing = true;

    while (true) {
      if (this.messageQueue.length === 0) {
        await new Promise(resolve => setTimeout(resolve, 10));
        continue;
      }

      // Sort by priority
      this.messageQueue.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
        const aPriority = priorityOrder[a.priority || 'normal'];
        const bPriority = priorityOrder[b.priority || 'normal'];
        return bPriority - aPriority;
      });

      const message = this.messageQueue.shift()!;
      await this.processMessage(message);
    }
  }

  /**
   * Process a single message
   */
  private async processMessage(message: ServiceMessage): Promise<void> {
    try {
      this.emit('message-processing', message);

      if (message.type === 'response') {
        await this.handleResponse(message);
      } else if (message.type === 'broadcast') {
        await this.handleBroadcast(message);
      } else {
        await this.handleMessage(message);
      }

      this.emit('message-processed', message);
    } catch (error) {
      logger.error('Error processing message', {
        messageId: message.id,
        action: message.action,
        error: error instanceof Error ? error.message : String(error),
      });

      this.emit('message-error', { message, error });

      // Send error response for requests
      if (message.type === 'request') {
        await this.respond(message, {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  /**
   * Handle response messages
   */
  private async handleResponse(message: ServiceMessage): Promise<void> {
    if (!message.correlationId) return;

    const pending = this.pendingRequests.get(message.correlationId);
    if (pending) {
      clearTimeout(pending.timeout);
      this.pendingRequests.delete(message.correlationId);

      if (message.payload?.error) {
        pending.reject(new Error(message.payload.error));
      } else {
        pending.resolve(message.payload);
      }
    }
  }

  /**
   * Handle broadcast messages
   */
  private async handleBroadcast(message: ServiceMessage): Promise<void> {
    for (const [serviceName, serviceHandlers] of this.handlers) {
      const handler = serviceHandlers.get(message.action);
      if (handler) {
        try {
          await handler({ ...message, to: serviceName });
        } catch (error) {
          logger.error(`Broadcast handler error: ${serviceName}.${message.action}`, error);
        }
      }
    }
  }

  /**
   * Handle regular messages
   */
  private async handleMessage(message: ServiceMessage): Promise<void> {
    const serviceHandlers = this.handlers.get(message.to);
    if (!serviceHandlers) {
      throw new Error(`Service not found: ${message.to}`);
    }

    const handler = serviceHandlers.get(message.action);
    if (!handler) {
      throw new Error(`Handler not found: ${message.to}.${message.action}`);
    }

    const result = await handler(message);

    // Send response for requests
    if (message.type === 'request') {
      await this.respond(message, result);
    }
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get bus statistics
   */
  getStats() {
    return {
      registeredServices: this.services.size,
      totalHandlers: Array.from(this.handlers.values()).reduce(
        (sum, handlers) => sum + handlers.size,
        0
      ),
      queuedMessages: this.messageQueue.length,
      pendingRequests: this.pendingRequests.size,
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.processing = false;
    this.pendingRequests.forEach(({ timeout }) => clearTimeout(timeout));
    this.pendingRequests.clear();
    this.messageQueue.length = 0;
    this.removeAllListeners();
  }
}

// Global microservices bus instance
export const microservicesBus = new MicroservicesBus();

export default microservicesBus;
