import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import { URL } from 'url';

import { app, session } from 'electron';

export interface NetworkConfig {
  enableProxy: boolean;
  proxyServer: string;
  proxyPort: number;
  proxyUsername?: string;
  proxyPassword?: string;
  enableSSL: boolean;
  enableHTTP2: boolean;
  enableQUIC: boolean;
  enableWebRTC: boolean;
  enableWebSocket: boolean;
  enableWebTransport: boolean;
  enableWebCodecs: boolean;
  enableWebML: boolean;
  enableWebNN: boolean;
  enableWebMIDI: boolean;
  enableWebUSB: boolean;
  enableWebBluetooth: boolean;
  enableWebSerial: boolean;
  enableWebHID: boolean;
  enableWebNFC: boolean;
  enableWebXR: boolean;
  maxConcurrentConnections: number;
  maxConcurrentRequests: number;
  maxConcurrentWebSockets: number;
  maxConcurrentWebRTCConnections: number;
  maxConcurrentWebTransportConnections: number;
  maxConcurrentWebCodecsDecoders: number;
  maxConcurrentWebMLModels: number;
  maxConcurrentWebNNModels: number;
  connectionTimeout: number;
  requestTimeout: number;
  webSocketTimeout: number;
  webRTCTimeout: number;
  webTransportTimeout: number;
  webCodecsTimeout: number;
  webMLTimeout: number;
  webNNTimeout: number;
  enableCache: boolean;
  cacheSize: number;
  enableCompression: boolean;
  enableKeepAlive: boolean;
  keepAliveTimeout: number;
  enableDNS: boolean;
  dnsServers: string[];
  enableDNSSEC: boolean;
  enableDNSCrypt: boolean;
  enableDNSOverHTTPS: boolean;
  enableDNSOverTLS: boolean;
  enableDNSOverQUIC: boolean;
  enableDNSOverWebTransport: boolean;
  enableDNSOverWebSocket: boolean;
  enableDNSOverWebRTC: boolean;
  enableDNSOverWebCodecs: boolean;
  enableDNSOverWebML: boolean;
  enableDNSOverWebNN: boolean;
}

export interface NetworkStats {
  totalRequests: number;
  totalBytesReceived: number;
  totalBytesSent: number;
  activeConnections: number;
  failedRequests: number;
  averageResponseTime: number;
  bandwidthUsage: number;
  dnsQueries: number;
  dnsCacheHits: number;
  dnsCacheMisses: number;
  sslConnections: number;
  http2Connections: number;
  quicConnections: number;
  webRTCConnections: number;
  webSocketConnections: number;
  webTransportConnections: number;
  webCodecsDecoders: number;
  webMLModels: number;
  webNNModels: number;
}

export class NetworkManager extends EventEmitter {
  private static instance: NetworkManager;
  private config: NetworkConfig;
  private stats: NetworkStats;
  private isInitialized: boolean;
  private defaultSession: Electron.Session;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.stats = this.getDefaultStats();
    this.isInitialized = false;
    this.defaultSession = session.defaultSession;
    this.initialize();
  }

  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  private getDefaultConfig(): NetworkConfig {
    return {
      enableProxy: false,
      proxyServer: '',
      proxyPort: 0,
      enableSSL: true,
      enableHTTP2: true,
      enableQUIC: true,
      enableWebRTC: true,
      enableWebSocket: true,
      enableWebTransport: true,
      enableWebCodecs: true,
      enableWebML: true,
      enableWebNN: true,
      enableWebMIDI: true,
      enableWebUSB: true,
      enableWebBluetooth: true,
      enableWebSerial: true,
      enableWebHID: true,
      enableWebNFC: true,
      enableWebXR: true,
      maxConcurrentConnections: 6,
      maxConcurrentRequests: 6,
      maxConcurrentWebSockets: 6,
      maxConcurrentWebRTCConnections: 6,
      maxConcurrentWebTransportConnections: 6,
      maxConcurrentWebCodecsDecoders: 6,
      maxConcurrentWebMLModels: 6,
      maxConcurrentWebNNModels: 6,
      connectionTimeout: 30000,
      requestTimeout: 30000,
      webSocketTimeout: 30000,
      webRTCTimeout: 30000,
      webTransportTimeout: 30000,
      webCodecsTimeout: 30000,
      webMLTimeout: 30000,
      webNNTimeout: 30000,
      enableCache: true,
      cacheSize: 1024 * 1024 * 100, // 100MB
      enableCompression: true,
      enableKeepAlive: true,
      keepAliveTimeout: 30000,
      enableDNS: true,
      dnsServers: ['8.8.8.8', '8.8.4.4'],
      enableDNSSEC: true,
      enableDNSCrypt: true,
      enableDNSOverHTTPS: true,
      enableDNSOverTLS: true,
      enableDNSOverQUIC: true,
      enableDNSOverWebTransport: true,
      enableDNSOverWebSocket: true,
      enableDNSOverWebRTC: true,
      enableDNSOverWebCodecs: true,
      enableDNSOverWebML: true,
      enableDNSOverWebNN: true,
    };
  }

  private getDefaultStats(): NetworkStats {
    return {
      totalRequests: 0,
      totalBytesReceived: 0,
      totalBytesSent: 0,
      activeConnections: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      bandwidthUsage: 0,
      dnsQueries: 0,
      dnsCacheHits: 0,
      dnsCacheMisses: 0,
      sslConnections: 0,
      http2Connections: 0,
      quicConnections: 0,
      webRTCConnections: 0,
      webSocketConnections: 0,
      webTransportConnections: 0,
      webCodecsDecoders: 0,
      webMLModels: 0,
      webNNModels: 0,
    };
  }

  private async initialize(): Promise<void> {
    try {
      await this.loadConfig();
      this.setupNetworkListeners();
      this.configureSession();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize network manager:', error);
      this.emit('initialization-error', error);
    }
  }

  private async loadConfig(): Promise<void> {
    try {
      const configPath = path.join(app.getPath('userData'), 'network-config.json');
      const data = await fs.readFile(configPath, 'utf-8');
      this.config = { ...this.getDefaultConfig(), ...JSON.parse(data) };
    } catch (error) {
      console.error('Failed to load network config:', error);
    }
  }

  private setupNetworkListeners(): void {
    this.defaultSession.webRequest.onBeforeRequest((details, callback) => {
      this.stats.totalRequests++;
      this.stats.activeConnections++;
      this.emit('request-started', details);
      callback({});
    });

    this.defaultSession.webRequest.onCompleted(details => {
      this.stats.activeConnections--;
      this.stats.totalBytesReceived += details.responseHeaders?.['content-length']
        ? parseInt(details.responseHeaders['content-length'], 10)
        : 0;
      this.emit('request-completed', details);
    });

    this.defaultSession.webRequest.onErrorOccurred(details => {
      this.stats.activeConnections--;
      this.stats.failedRequests++;
      this.emit('request-error', details);
    });

    this.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      this.updateProtocolStats(details);
      callback({});
    });
  }

  private updateProtocolStats(details: Electron.OnHeadersReceivedListenerDetails): void {
    const protocol = details.url.split(':')[0].toLowerCase();
    switch (protocol) {
      case 'https':
        this.stats.sslConnections++;
        break;
      case 'http/2':
        this.stats.http2Connections++;
        break;
      case 'quic':
        this.stats.quicConnections++;
        break;
      case 'ws':
      case 'wss':
        this.stats.webSocketConnections++;
        break;
    }
  }

  private configureSession(): void {
    if (this.config.enableProxy) {
      const proxyRules = `http://${this.config.proxyServer}:${this.config.proxyPort}`;
      this.defaultSession.setProxy({
        proxyRules,
        proxyBypassRules: 'localhost,127.0.0.1',
      });
    }

    this.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      const url = webContents.getURL();
      const parsedUrl = new URL(url);

      switch (permission) {
        case 'media':
          callback(this.config.enableWebRTC);
          break;
        case 'geolocation':
          callback(true);
          break;
        case 'notifications':
          callback(true);
          break;
        case 'midi':
        case 'midiSysex':
          callback(this.config.enableWebMIDI);
          break;
        case 'display-capture':
          callback(true);
          break;
        case 'fullscreen':
          callback(true);
          break;
        case 'clipboard-read':
        case 'clipboard-sanitized-write':
          callback(true);
          break;
        case 'mediaKeySystem':
          callback(true);
          break;
        case 'idle-detection':
          callback(true);
          break;
        default:
          callback(false);
      }
    });
  }

  public async setConfig(newConfig: Partial<NetworkConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    this.configureSession();
    this.emit('config-updated', this.config);
  }

  private async saveConfig(): Promise<void> {
    try {
      const configPath = path.join(app.getPath('userData'), 'network-config.json');
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('Failed to save network config:', error);
    }
  }

  public getConfig(): NetworkConfig {
    return { ...this.config };
  }

  public getStats(): NetworkStats {
    return { ...this.stats };
  }

  public resetStats(): void {
    this.stats = this.getDefaultStats();
    this.emit('stats-reset');
  }

  public getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  public async clearCache(): Promise<void> {
    try {
      await this.defaultSession.clearCache();
      this.emit('cache-cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      throw error;
    }
  }

  public async clearStorageData(): Promise<void> {
    try {
      await this.defaultSession.clearStorageData({
        storages: [
          'cookies',
          'filesystem',
          'indexdb',
          'localstorage',
          'shadercache',
          'websql',
          'serviceworkers',
          'cachestorage',
        ],
      });
      this.emit('storage-cleared');
    } catch (error) {
      console.error('Failed to clear storage data:', error);
      throw error;
    }
  }

  public async getCookies(domain?: string): Promise<Electron.Cookie[]> {
    try {
      return await this.defaultSession.cookies.get({ domain });
    } catch (error) {
      console.error('Failed to get cookies:', error);
      throw error;
    }
  }

  public async setCookie(cookie: Electron.Cookie): Promise<void> {
    try {
      const cookieDetails: Electron.CookiesSetDetails = {
        url: `https://${cookie.domain}${cookie.path}`,
        name: cookie.name,
        value: cookie.value,
        domain: cookie.domain,
        path: cookie.path,
        secure: cookie.secure,
        httpOnly: cookie.httpOnly,
        expirationDate: cookie.expirationDate,
      };
      await this.defaultSession.cookies.set(cookieDetails);
      this.emit('cookie-set', cookie);
    } catch (error) {
      console.error('Failed to set cookie:', error);
      throw error;
    }
  }

  public async removeCookie(url: string, name: string): Promise<void> {
    try {
      await this.defaultSession.cookies.remove(url, name);
      this.emit('cookie-removed', { url, name });
    } catch (error) {
      console.error('Failed to remove cookie:', error);
      throw error;
    }
  }

  public async flushStorageData(): Promise<void> {
    try {
      await this.defaultSession.flushStorageData();
      this.emit('storage-flushed');
    } catch (error) {
      console.error('Failed to flush storage data:', error);
      throw error;
    }
  }

  public async getPreloads(): Promise<string[]> {
    try {
      return await this.defaultSession.getPreloads();
    } catch (error) {
      console.error('Failed to get preloads:', error);
      throw error;
    }
  }

  public async setPreloads(preloads: string[]): Promise<void> {
    try {
      await this.defaultSession.setPreloads(preloads);
      this.emit('preloads-updated', preloads);
    } catch (error) {
      console.error('Failed to set preloads:', error);
      throw error;
    }
  }

  public async getUserAgent(): Promise<string> {
    return this.defaultSession.getUserAgent();
  }

  public async setUserAgent(userAgent: string): Promise<void> {
    try {
      await this.defaultSession.setUserAgent(userAgent);
      this.emit('user-agent-updated', userAgent);
    } catch (error) {
      console.error('Failed to set user agent:', error);
      throw error;
    }
  }
}
