import { URL } from 'url';

import { BrowserWindow } from 'electron';

interface PreloadConfig {
  maxPreloadPages: number;
  preloadTimeout: number;
  preloadStrategy: 'aggressive' | 'conservative';
}

export class PreloadManager {
  private static instance: PreloadManager;
  private preloadedPages: Map<string, BrowserWindow>;
  private config: PreloadConfig;

  private constructor() {
    this.preloadedPages = new Map();
    this.config = {
      maxPreloadPages: 3,
      preloadTimeout: 30000,
      preloadStrategy: 'conservative',
    };
  }

  public static getInstance(): PreloadManager {
    if (!PreloadManager.instance) {
      PreloadManager.instance = new PreloadManager();
    }
    return PreloadManager.instance;
  }

  public async preloadPage(url: string): Promise<void> {
    if (this.preloadedPages.size >= this.config.maxPreloadPages) {
      this.cleanupOldestPreload();
    }

    const preloadWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
      },
    });

    try {
      await preloadWindow.loadURL(url);
      this.preloadedPages.set(url, preloadWindow);

      // Set timeout to cleanup if not used
      setTimeout(() => {
        this.cleanupPreload(url);
      }, this.config.preloadTimeout);
    } catch (error) {
      preloadWindow.destroy();
      throw error;
    }
  }

  public getPreloadedPage(url: string): BrowserWindow | undefined {
    const window = this.preloadedPages.get(url);
    if (window) {
      this.preloadedPages.delete(url);
    }
    return window;
  }

  private cleanupOldestPreload(): void {
    const oldestUrl = this.preloadedPages.keys().next().value;
    if (oldestUrl) {
      this.cleanupPreload(oldestUrl);
    }
  }

  private cleanupPreload(url: string): void {
    const window = this.preloadedPages.get(url);
    if (window) {
      window.destroy();
      this.preloadedPages.delete(url);
    }
  }

  public setConfig(config: Partial<PreloadConfig>): void {
    this.config = { ...this.config, ...config };
  }
}
