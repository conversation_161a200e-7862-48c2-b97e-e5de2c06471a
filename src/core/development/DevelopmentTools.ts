import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { Worker } from 'worker_threads';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import { promisify } from 'util';

interface ToolConfig {
  name: string;
  type: 'linter' | 'formatter' | 'typeChecker' | 'testRunner' | 'debugger' | 'profiler' | 'coverage';
  config: any;
}

interface ToolResult {
  success: boolean;
  output: any;
  error?: string;
  duration: number;
}

export class DevelopmentTools extends EventEmitter {
  private static instance: DevelopmentTools;
  private tools: Map<string, ToolConfig>;
  private isInitialized: boolean = false;

  private constructor() {
    super();
    this.tools = new Map();
  }

  public static getInstance(): DevelopmentTools {
    if (!DevelopmentTools.instance) {
      DevelopmentTools.instance = new DevelopmentTools();
    }
    return DevelopmentTools.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.setupTools();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize DevelopmentTools:', error);
      throw error;
    }
  }

  private async setupTools(): Promise<void> {
    // Setup ESLint
    await this.setupLinter({
      name: 'ESLint',
      type: 'linter',
      config: {
        rules: {
          'no-console': 'warn',
          'no-unused-vars': 'error',
          'semi': ['error', 'always']
        },
        parser: '@typescript-eslint/parser',
        plugins: ['@typescript-eslint'],
        extends: [
          'eslint:recommended',
          'plugin:@typescript-eslint/recommended'
        ]
      }
    });

    // Setup Prettier
    await this.setupFormatter({
      name: 'Prettier',
      type: 'formatter',
      config: {
        printWidth: 80,
        tabWidth: 2,
        useTabs: false,
        semi: true,
        singleQuote: true,
        trailingComma: 'es5',
        bracketSpacing: true,
        arrowParens: 'avoid'
      }
    });

    // Setup TypeScript
    await this.setupTypeChecker({
      name: 'TypeScript',
      type: 'typeChecker',
      config: {
        strict: true,
        noImplicitAny: true,
        strictNullChecks: true,
        strictFunctionTypes: true,
        strictBindCallApply: true,
        strictPropertyInitialization: true,
        noImplicitThis: true,
        alwaysStrict: true
      }
    });

    // Setup Jest
    await this.setupTestRunner({
      name: 'Jest',
      type: 'testRunner',
      config: {
        preset: 'ts-jest',
        testEnvironment: 'node',
        roots: ['<rootDir>/src'],
        transform: {
          '^.+\\.tsx?$': 'ts-jest'
        },
        testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.tsx?$',
        moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
        collectCoverage: true,
        coverageDirectory: 'coverage',
        coverageReporters: ['text', 'lcov'],
        verbose: true
      }
    });

    // Setup Node.js Debugger
    await this.setupDebugger({
      name: 'Node.js Debugger',
      type: 'debugger',
      config: {
        port: 9229,
        host: 'localhost',
        protocol: 'inspector',
        timeout: 30000,
        breakOnStart: false,
        sourceMaps: true,
        smartStep: true,
        skipFiles: ['<node_internals>/**'],
        console: 'integratedTerminal'
      }
    });

    // Setup Node.js Profiler
    await this.setupProfiler({
      name: 'Node.js Profiler',
      type: 'profiler',
      config: {
        samplingInterval: 1000,
        maxSamples: 10000,
        includeNodeModules: false,
        includeSystemModules: false,
        includeAsync: true,
        includeV8Internals: false,
        includeNodeInternals: false
      }
    });

    // Setup Istanbul
    await this.setupCoverage({
      name: 'Istanbul',
      type: 'coverage',
      config: {
        reporter: ['text', 'lcov', 'html'],
        exclude: [
          '**/node_modules/**',
          '**/coverage/**',
          '**/dist/**',
          '**/build/**',
          '**/test/**',
          '**/*.test.ts',
          '**/*.spec.ts'
        ],
        all: true,
        statements: 80,
        branches: 80,
        functions: 80,
        lines: 80
      }
    });
  }

  private async setupLinter(config: ToolConfig): Promise<void> {
    this.tools.set('linter', config);
    this.emit('tool-setup', config);
  }

  private async setupFormatter(config: ToolConfig): Promise<void> {
    this.tools.set('formatter', config);
    this.emit('tool-setup', config);
  }

  private async setupTypeChecker(config: ToolConfig): Promise<void> {
    this.tools.set('typeChecker', config);
    this.emit('tool-setup', config);
  }

  private async setupTestRunner(config: ToolConfig): Promise<void> {
    this.tools.set('testRunner', config);
    this.emit('tool-setup', config);
  }

  private async setupDebugger(config: ToolConfig): Promise<void> {
    this.tools.set('debugger', config);
    this.emit('tool-setup', config);
  }

  private async setupProfiler(config: ToolConfig): Promise<void> {
    this.tools.set('profiler', config);
    this.emit('tool-setup', config);
  }

  private async setupCoverage(config: ToolConfig): Promise<void> {
    this.tools.set('coverage', config);
    this.emit('tool-setup', config);
  }

  public async runLinter(files: string[]): Promise<ToolResult> {
    const config = this.tools.get('linter');
    if (!config) {
      throw new Error('Linter not configured');
    }

    const startTime = Date.now();
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const command = `npx eslint ${files.join(' ')} --format json`;
      const { stdout } = await execAsync(command);
      const output = JSON.parse(stdout);

      return {
        success: output.length === 0,
        output,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public async runFormatter(files: string[]): Promise<ToolResult> {
    const config = this.tools.get('formatter');
    if (!config) {
      throw new Error('Formatter not configured');
    }

    const startTime = Date.now();
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const command = `npx prettier --write ${files.join(' ')}`;
      const { stdout } = await execAsync(command);

      return {
        success: true,
        output: stdout,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public async runTypeChecker(files: string[]): Promise<ToolResult> {
    const config = this.tools.get('typeChecker');
    if (!config) {
      throw new Error('Type checker not configured');
    }

    const startTime = Date.now();
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const command = `npx tsc --noEmit ${files.join(' ')}`;
      const { stdout } = await execAsync(command);

      return {
        success: true,
        output: stdout,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public async runTests(files: string[]): Promise<ToolResult> {
    const config = this.tools.get('testRunner');
    if (!config) {
      throw new Error('Test runner not configured');
    }

    const startTime = Date.now();
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const command = `npx jest ${files.join(' ')} --json`;
      const { stdout } = await execAsync(command);
      const output = JSON.parse(stdout);

      return {
        success: output.success,
        output,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public async startDebugger(port: number = 9229): Promise<ToolResult> {
    const config = this.tools.get('debugger');
    if (!config) {
      throw new Error('Debugger not configured');
    }

    const startTime = Date.now();
    try {
      const { spawn } = require('child_process');
      const debuggerProcess = spawn('node', ['--inspect', `--inspect-port=${port}`]);

      return {
        success: true,
        output: {
          pid: debuggerProcess.pid,
          port
        },
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public async runProfiler(duration: number = 10000): Promise<ToolResult> {
    const config = this.tools.get('profiler');
    if (!config) {
      throw new Error('Profiler not configured');
    }

    const startTime = Date.now();
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const command = `npx 0x --duration ${duration}`;
      const { stdout } = await execAsync(command);

      return {
        success: true,
        output: stdout,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public async runCoverage(files: string[]): Promise<ToolResult> {
    const config = this.tools.get('coverage');
    if (!config) {
      throw new Error('Coverage tool not configured');
    }

    const startTime = Date.now();
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const command = `npx nyc --reporter=text --reporter=lcov --reporter=html ${files.join(' ')}`;
      const { stdout } = await execAsync(command);

      return {
        success: true,
        output: stdout,
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        output: null,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  public getTool(type: string): ToolConfig | undefined {
    return this.tools.get(type);
  }

  public getAllTools(): ToolConfig[] {
    return Array.from(this.tools.values());
  }
} 