import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { createHash } from 'crypto';
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import { createGzip, createGunzip } from 'zlib';
import { pipeline } from 'stream/promises';
import { createReadStream, createWriteStream } from 'fs';
import { Client as S3Client } from '@aws-sdk/client-s3';
import { Client as GCSClient } from '@google-cloud/storage';
import { Client as AzureClient } from '@azure/storage-blob';
import { Client as DropboxClient } from 'dropbox';
import { Client as OneDriveClient } from '@microsoft/microsoft-graph-client';
import { Client as BoxClient } from 'box-node-sdk';
import { Client as GDriveClient } from 'googleapis';
import { Client as MegaClient } from 'mega';
import { Client as PCloudClient } from 'pcloud-sdk-js';
import { Client as NextCloudClient } from 'nextcloud-node-client';
import { Client as OwnCloudClient } from 'owncloud-node-client';
import { Client as SeafileClient } from 'seafile-api';
import { Client as WebDAVClient } from 'webdav-client';
import { Client as FTPClient } from 'basic-ftp';
import { Client as SFTPClient } from 'ssh2-sftp-client';
import { Client as SMBClient } from 'smb2';
import { Client as NFSClient } from 'nfs';
import { Client as AFPClient } from 'afp';
import { Worker } from 'worker_threads';
import { EventEmitter as WorkerEventEmitter } from 'events';
import { createInterface } from 'readline';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import { promisify } from 'util';
import { createServer } from 'net';
import { createServer as createHttpServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { createServer as createWebSocketServer } from 'ws';
import { createServer as createGrpcServer } from '@grpc/grpc-js';
import { createServer as createTcpServer } from 'net';
import { createServer as createUdpServer } from 'dgram';
import { createServer as createIpcServer } from 'net';
import { createServer as createUnixServer } from 'net';
import { createServer as createTlsServer } from 'tls';
import { createServer as createSecureServer } from 'tls';
import { createServer as createHttp2Server } from 'http2';
import { createServer as createHttps2Server } from 'http2';
import { createServer as createWebSocket2Server } from 'ws';
import { createServer as createGrpc2Server } from '@grpc/grpc-js';
import { createServer as createTcp2Server } from 'net';
import { createServer as createUdp2Server } from 'dgram';
import { createServer as createIpc2Server } from 'net';
import { createServer as createUnix2Server } from 'net';
import { createServer as createTls2Server } from 'tls';
import { createServer as createSecure2Server } from 'tls';
import { createServer as createHttp3Server } from 'http3';
import { createServer as createHttps3Server } from 'https3';
import { createServer as createWebSocket3Server } from 'ws';
import { createServer as createGrpc3Server } from '@grpc/grpc-js';
import { createServer as createTcp3Server } from 'net';
import { createServer as createUdp3Server } from 'dgram';
import { createServer as createIpc3Server } from 'net';
import { createServer as createUnix3Server } from 'net';
import { createServer as createTls3Server } from 'tls';
import { createServer as createSecure3Server } from 'tls';
import { createServer as createHttp4Server } from 'http4';
import { createServer as createHttps4Server } from 'https4';
import { createServer as createWebSocket4Server } from 'ws';
import { createServer as createGrpc4Server } from '@grpc/grpc-js';
import { createServer as createTcp4Server } from 'net';
import { createServer as createUdp4Server } from 'dgram';
import { createServer as createIpc4Server } from 'net';
import { createServer as createUnix4Server } from 'net';
import { createServer as createTls4Server } from 'tls';
import { createServer as createSecure4Server } from 'tls';
import { createServer as createHttp5Server } from 'http5';
import { createServer as createHttps5Server } from 'https5';
import { createServer as createWebSocket5Server } from 'ws';
import { createServer as createGrpc5Server } from '@grpc/grpc-js';
import { createServer as createTcp5Server } from 'net';
import { createServer as createUdp5Server } from 'dgram';
import { createServer as createIpc5Server } from 'net';
import { createServer as createUnix5Server } from 'net';
import { createServer as createTls5Server } from 'tls';
import { createServer as createSecure5Server } from 'tls';
import { createServer as createHttp6Server } from 'http6';
import { createServer as createHttps6Server } from 'https6';
import { createServer as createWebSocket6Server } from 'ws';
import { createServer as createGrpc6Server } from '@grpc/grpc-js';
import { createServer as createTcp6Server } from 'net';
import { createServer as createUdp6Server } from 'dgram';
import { createServer as createIpc6Server } from 'net';
import { createServer as createUnix6Server } from 'net';
import { createServer as createTls6Server } from 'tls';
import { createServer as createSecure6Server } from 'tls';
import { createServer as createHttp7Server } from 'http7';
import { createServer as createHttps7Server } from 'https7';
import { createServer as createWebSocket7Server } from 'ws';
import { createServer as createGrpc7Server } from '@grpc/grpc-js';
import { createServer as createTcp7Server } from 'net';
import { createServer as createUdp7Server } from 'dgram';
import { createServer as createIpc7Server } from 'net';
import { createServer as createUnix7Server } from 'net';
import { createServer as createTls7Server } from 'tls';
import { createServer as createSecure7Server } from 'tls';
import { createServer as createHttp8Server } from 'http8';
import { createServer as createHttps8Server } from 'https8';
import { createServer as createWebSocket8Server } from 'ws';
import { createServer as createGrpc8Server } from '@grpc/grpc-js';
import { createServer as createTcp8Server } from 'net';
import { createServer as createUdp8Server } from 'dgram';
import { createServer as createIpc8Server } from 'net';
import { createServer as createUnix8Server } from 'net';
import { createServer as createTls8Server } from 'tls';
import { createServer as createSecure8Server } from 'tls';
import { createServer as createHttp9Server } from 'http9';
import { createServer as createHttps9Server } from 'https9';
import { createServer as createWebSocket9Server } from 'ws';
import { createServer as createGrpc9Server } from '@grpc/grpc-js';
import { createServer as createTcp9Server } from 'net';
import { createServer as createUdp9Server } from 'dgram';
import { createServer as createIpc9Server } from 'net';
import { createServer as createUnix9Server } from 'net';
import { createServer as createTls9Server } from 'tls';
import { createServer as createSecure9Server } from 'tls';
import { createServer as createHttp10Server } from 'http10';
import { createServer as createHttps10Server } from 'https10';
import { createServer as createWebSocket10Server } from 'ws';
import { createServer as createGrpc10Server } from '@grpc/grpc-js';
import { createServer as createTcp10Server } from 'net';
import { createServer as createUdp10Server } from 'dgram';
import { createServer as createIpc10Server } from 'net';
import { createServer as createUnix10Server } from 'net';
import { createServer as createTls10Server } from 'tls';
import { createServer as createSecure10Server } from 'tls';

interface DevelopmentSettings {
  enabled: boolean;
  tools: {
    linter: boolean;
    formatter: boolean;
    typeChecker: boolean;
    testRunner: boolean;
    debugger: boolean;
    profiler: boolean;
    coverage: boolean;
  };
  monitoring: {
    enabled: boolean;
    metrics: boolean;
    logging: boolean;
    tracing: boolean;
  };
  storage: {
    enabled: boolean;
    path: string;
  };
}

interface DevelopmentTool {
  id: string;
  name: string;
  type: 'linter' | 'formatter' | 'typeChecker' | 'testRunner' | 'debugger' | 'profiler' | 'coverage';
  config: any;
  status: 'enabled' | 'disabled' | 'running' | 'error';
  metadata: {
    created: number;
    modified: number;
    lastRun: number;
    version: string;
  };
}

interface DevelopmentResult {
  id: string;
  toolId: string;
  status: 'success' | 'failure';
  output: any;
  error?: string;
  metrics: {
    duration: number;
    startTime: number;
    endTime: number;
  };
  metadata: {
    timestamp: number;
    version: string;
    environment: string;
  };
}

interface MonitoringMetrics {
  cpu: {
    usage: number;
    temperature: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    connections: number;
  };
  process: {
    uptime: number;
    threads: number;
    handles: number;
  };
}

interface MonitoringLog {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: number;
  metadata: {
    tool?: string;
    operation?: string;
    duration?: number;
    error?: string;
  };
}

interface MonitoringTrace {
  id: string;
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  metadata: {
    tool?: string;
    operation?: string;
    status?: string;
    error?: string;
  };
  children: MonitoringTrace[];
}

export class DevelopmentManager extends EventEmitter {
  private static instance: DevelopmentManager;
  private settings: DevelopmentSettings;
  private tools: Map<string, DevelopmentTool>;
  private results: Map<string, DevelopmentResult>;
  private isInitialized: boolean = false;

  private constructor() {
    super();
    this.settings = {
      enabled: true,
      tools: {
        linter: true,
        formatter: true,
        typeChecker: true,
        testRunner: true,
        debugger: true,
        profiler: true,
        coverage: true
      },
      monitoring: {
        enabled: true,
        metrics: true,
        logging: true,
        tracing: true
      },
      storage: {
        enabled: true,
        path: 'development'
      }
    };
    this.tools = new Map();
    this.results = new Map();
  }

  public static getInstance(): DevelopmentManager {
    if (!DevelopmentManager.instance) {
      DevelopmentManager.instance = new DevelopmentManager();
    }
    return DevelopmentManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadTools();
      await this.loadResults();
      await this.setupTools();
      await this.setupMonitoring();
      await this.setupStorage();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize DevelopmentManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'development-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'development-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async loadTools(): Promise<void> {
    try {
      const toolsPath = path.join(app.getPath('userData'), 'development-tools.json');
      const data = await fs.readFile(toolsPath, 'utf-8');
      const tools = JSON.parse(data);
      
      for (const tool of tools) {
        this.tools.set(tool.id, tool);
      }
    } catch (error) {
      await this.saveTools();
    }
  }

  private async saveTools(): Promise<void> {
    const toolsPath = path.join(app.getPath('userData'), 'development-tools.json');
    await fs.writeFile(
      toolsPath,
      JSON.stringify(Array.from(this.tools.values()), null, 2)
    );
  }

  private async loadResults(): Promise<void> {
    try {
      const resultsPath = path.join(app.getPath('userData'), 'development-results.json');
      const data = await fs.readFile(resultsPath, 'utf-8');
      const results = JSON.parse(data);
      
      for (const result of results) {
        this.results.set(result.id, result);
      }
    } catch (error) {
      await this.saveResults();
    }
  }

  private async saveResults(): Promise<void> {
    const resultsPath = path.join(app.getPath('userData'), 'development-results.json');
    await fs.writeFile(
      resultsPath,
      JSON.stringify(Array.from(this.results.values()), null, 2)
    );
  }

  private async setupTools(): Promise<void> {
    // Setup development tools based on settings
    if (this.settings.tools.linter) {
      await this.setupLinter();
    }

    if (this.settings.tools.formatter) {
      await this.setupFormatter();
    }

    if (this.settings.tools.typeChecker) {
      await this.setupTypeChecker();
    }

    if (this.settings.tools.testRunner) {
      await this.setupTestRunner();
    }

    if (this.settings.tools.debugger) {
      await this.setupDebugger();
    }

    if (this.settings.tools.profiler) {
      await this.setupProfiler();
    }

    if (this.settings.tools.coverage) {
      await this.setupCoverage();
    }
  }

  private async setupLinter(): Promise<void> {
    const linter = await this.createTool({
      name: 'ESLint',
      type: 'linter',
      config: {
        rules: {
          'no-console': 'warn',
          'no-unused-vars': 'error',
          'semi': ['error', 'always']
        },
        parser: '@typescript-eslint/parser',
        plugins: ['@typescript-eslint'],
        extends: [
          'eslint:recommended',
          'plugin:@typescript-eslint/recommended'
        ]
      }
    });

    this.emit('tool-created', linter);
  }

  private async setupFormatter(): Promise<void> {
    const formatter = await this.createTool({
      name: 'Prettier',
      type: 'formatter',
      config: {
        printWidth: 80,
        tabWidth: 2,
        useTabs: false,
        semi: true,
        singleQuote: true,
        trailingComma: 'es5',
        bracketSpacing: true,
        arrowParens: 'avoid'
      }
    });

    this.emit('tool-created', formatter);
  }

  private async setupTypeChecker(): Promise<void> {
    const typeChecker = await this.createTool({
      name: 'TypeScript',
      type: 'typeChecker',
      config: {
        strict: true,
        noImplicitAny: true,
        strictNullChecks: true,
        strictFunctionTypes: true,
        strictBindCallApply: true,
        strictPropertyInitialization: true,
        noImplicitThis: true,
        alwaysStrict: true
      }
    });

    this.emit('tool-created', typeChecker);
  }

  private async setupTestRunner(): Promise<void> {
    const testRunner = await this.createTool({
      name: 'Jest',
      type: 'testRunner',
      config: {
        preset: 'ts-jest',
        testEnvironment: 'node',
        roots: ['<rootDir>/src'],
        transform: {
          '^.+\\.tsx?$': 'ts-jest'
        },
        testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.tsx?$',
        moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
        collectCoverage: true,
        coverageDirectory: 'coverage',
        coverageReporters: ['text', 'lcov'],
        verbose: true
      }
    });

    this.emit('tool-created', testRunner);
  }

  private async setupDebugger(): Promise<void> {
    const debuggerTool = await this.createTool({
      name: 'Node.js Debugger',
      type: 'debugger',
      config: {
        port: 9229,
        host: 'localhost',
        protocol: 'inspector',
        timeout: 30000,
        breakOnStart: false,
        sourceMaps: true,
        smartStep: true,
        skipFiles: ['<node_internals>/**'],
        console: 'integratedTerminal'
      }
    });

    this.emit('tool-created', debuggerTool);
  }

  private async setupProfiler(): Promise<void> {
    const profiler = await this.createTool({
      name: 'Node.js Profiler',
      type: 'profiler',
      config: {
        samplingInterval: 1000,
        maxSamples: 10000,
        includeNodeModules: false,
        includeSystemModules: false,
        includeAsync: true,
        includeV8Internals: false,
        includeNodeInternals: false
      }
    });

    this.emit('tool-created', profiler);
  }

  private async setupCoverage(): Promise<void> {
    const coverage = await this.createTool({
      name: 'Istanbul',
      type: 'coverage',
      config: {
        reporter: ['text', 'lcov', 'html'],
        exclude: [
          '**/node_modules/**',
          '**/coverage/**',
          '**/dist/**',
          '**/build/**',
          '**/test/**',
          '**/*.test.ts',
          '**/*.spec.ts'
        ],
        all: true,
        statements: 80,
        branches: 80,
        functions: 80,
        lines: 80
      }
    });

    this.emit('tool-created', coverage);
  }

  private async setupMonitoring(): Promise<void> {
    if (!this.settings.monitoring.enabled) return;

    if (this.settings.monitoring.metrics) {
      await this.setupMetricsCollection();
    }

    if (this.settings.monitoring.logging) {
      await this.setupLogging();
    }

    if (this.settings.monitoring.tracing) {
      await this.setupTracing();
    }
  }

  private async setupMetricsCollection(): Promise<void> {
    const metricsInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics();
        this.emit('metrics-collected', metrics);
      } catch (error) {
        console.error('Failed to collect metrics:', error);
      }
    }, 60000); // Collect metrics every minute

    process.on('exit', () => {
      clearInterval(metricsInterval);
    });
  }

  private async collectMetrics(): Promise<MonitoringMetrics> {
    const os = require('os');
    const process = require('process');

    return {
      cpu: {
        usage: os.loadavg()[0],
        temperature: await this.getCpuTemperature()
      },
      memory: {
        total: os.totalmem(),
        used: os.totalmem() - os.freemem(),
        free: os.freemem()
      },
      disk: await this.getDiskMetrics(),
      network: await this.getNetworkMetrics(),
      process: {
        uptime: process.uptime(),
        threads: process.getActiveResourcesInfo().length,
        handles: process.getActiveHandles().length
      }
    };
  }

  private async getCpuTemperature(): Promise<number> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('sensors');
      const match = stdout.match(/Core 0:\s+\+(\d+\.\d+)°C/);
      return match ? parseFloat(match[1]) : 0;
    } catch (error) {
      return 0;
    }
  }

  private async getDiskMetrics(): Promise<{ total: number; used: number; free: number }> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('df -k /');
      const lines = stdout.split('\n');
      const values = lines[1].split(/\s+/);

      return {
        total: parseInt(values[1]) * 1024,
        used: parseInt(values[2]) * 1024,
        free: parseInt(values[3]) * 1024
      };
    } catch (error) {
      return { total: 0, used: 0, free: 0 };
    }
  }

  private async getNetworkMetrics(): Promise<{ bytesIn: number; bytesOut: number; connections: number }> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('netstat -i');
      const lines = stdout.split('\n');
      const values = lines[2].split(/\s+/);

      return {
        bytesIn: parseInt(values[3]),
        bytesOut: parseInt(values[7]),
        connections: parseInt(values[8])
      };
    } catch (error) {
      return { bytesIn: 0, bytesOut: 0, connections: 0 };
    }
  }

  private async setupLogging(): Promise<void> {
    const logPath = path.join(app.getPath('userData'), 'development-logs.json');
    let logs: MonitoringLog[] = [];

    try {
      const data = await fs.readFile(logPath, 'utf-8');
      logs = JSON.parse(data);
    } catch (error) {
      await fs.writeFile(logPath, JSON.stringify(logs, null, 2));
    }

    const logInterval = setInterval(async () => {
      try {
        await fs.writeFile(logPath, JSON.stringify(logs, null, 2));
      } catch (error) {
        console.error('Failed to save logs:', error);
      }
    }, 60000); // Save logs every minute

    process.on('exit', () => {
      clearInterval(logInterval);
    });

    this.on('log', (log: MonitoringLog) => {
      logs.push(log);
      if (logs.length > 1000) {
        logs = logs.slice(-1000); // Keep only last 1000 logs
      }
    });
  }

  private async setupTracing(): Promise<void> {
    const tracePath = path.join(app.getPath('userData'), 'development-traces.json');
    let traces: MonitoringTrace[] = [];

    try {
      const data = await fs.readFile(tracePath, 'utf-8');
      traces = JSON.parse(data);
    } catch (error) {
      await fs.writeFile(tracePath, JSON.stringify(traces, null, 2));
    }

    const traceInterval = setInterval(async () => {
      try {
        await fs.writeFile(tracePath, JSON.stringify(traces, null, 2));
      } catch (error) {
        console.error('Failed to save traces:', error);
      }
    }, 60000); // Save traces every minute

    process.on('exit', () => {
      clearInterval(traceInterval);
    });

    this.on('trace', (trace: MonitoringTrace) => {
      traces.push(trace);
      if (traces.length > 100) {
        traces = traces.slice(-100); // Keep only last 100 traces
      }
    });
  }

  private async setupStorage(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const storagePath = path.join(app.getPath('userData'), this.settings.storage.path);
    await fs.mkdir(storagePath, { recursive: true });
  }

  public async createTool(tool: Omit<DevelopmentTool, 'id' | 'status' | 'metadata'>): Promise<DevelopmentTool> {
    const newTool: DevelopmentTool = {
      ...tool,
      id: Math.random().toString(36).substr(2, 9),
      status: 'enabled',
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        lastRun: 0,
        version: '1.0.0'
      }
    };

    this.tools.set(newTool.id, newTool);
    await this.saveTools();
    this.emit('tool-created', newTool);

    return newTool;
  }

  public async updateTool(id: string, updates: Partial<DevelopmentTool>): Promise<DevelopmentTool> {
    const tool = this.tools.get(id);
    if (!tool) {
      throw new Error(`Tool not found: ${id}`);
    }

    const updatedTool = {
      ...tool,
      ...updates,
      metadata: {
        ...tool.metadata,
        modified: Date.now()
      }
    };

    this.tools.set(id, updatedTool);
    await this.saveTools();
    this.emit('tool-updated', updatedTool);

    return updatedTool;
  }

  public async deleteTool(id: string): Promise<void> {
    const tool = this.tools.get(id);
    if (!tool) {
      throw new Error(`Tool not found: ${id}`);
    }

    this.tools.delete(id);
    await this.saveTools();
    this.emit('tool-deleted', tool);
  }

  public async runTool(id: string, input: any): Promise<DevelopmentResult> {
    const tool = this.tools.get(id);
    if (!tool) {
      throw new Error(`Tool not found: ${id}`);
    }

    if (tool.status === 'running') {
      throw new Error(`Tool is already running: ${id}`);
    }

    // Update tool status
    tool.status = 'running';
    tool.metadata.lastRun = Date.now();
    await this.saveTools();

    try {
      // Execute tool
      const startTime = Date.now();
      const output = await this.executeTool(tool, input);
      const endTime = Date.now();

      const result: DevelopmentResult = {
        id: Math.random().toString(36).substr(2, 9),
        toolId: tool.id,
        status: 'success',
        output,
        metrics: {
          duration: endTime - startTime,
          startTime,
          endTime
        },
        metadata: {
          timestamp: Date.now(),
          version: tool.metadata.version,
          environment: process.env.NODE_ENV || 'development'
        }
      };

      // Update tool status
      tool.status = 'enabled';

      // Save results
      this.results.set(result.id, result);
      await this.saveResults();
      await this.saveTools();

      this.emit('tool-completed', { tool, result });

      return result;
    } catch (error) {
      const result: DevelopmentResult = {
        id: Math.random().toString(36).substr(2, 9),
        toolId: tool.id,
        status: 'failure',
        error: error.message,
        metrics: {
          duration: Date.now() - tool.metadata.lastRun,
          startTime: tool.metadata.lastRun,
          endTime: Date.now()
        },
        metadata: {
          timestamp: Date.now(),
          version: tool.metadata.version,
          environment: process.env.NODE_ENV || 'development'
        }
      };

      // Update tool status
      tool.status = 'error';

      // Save results
      this.results.set(result.id, result);
      await this.saveResults();
      await this.saveTools();

      this.emit('tool-failed', { tool, result });

      throw error;
    }
  }

  private async executeTool(tool: DevelopmentTool, input: any): Promise<any> {
    // Implement tool execution logic
    return {};
  }

  public getTool(id: string): DevelopmentTool | undefined {
    return this.tools.get(id);
  }

  public getAllTools(): DevelopmentTool[] {
    return Array.from(this.tools.values());
  }

  public getResult(id: string): DevelopmentResult | undefined {
    return this.results.get(id);
  }

  public getAllResults(): DevelopmentResult[] {
    return Array.from(this.results.values());
  }

  public getSettings(): DevelopmentSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<DevelopmentSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }
} 