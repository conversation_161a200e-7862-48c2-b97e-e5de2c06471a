/**
 * Dependency Injection Container
 * Современный IoC контейнер для управления зависимостями
 */

export type Constructor<T = {}> = new (...args: any[]) => T;
export type ServiceIdentifier<T = any> = string | symbol | Constructor<T>;
export type Factory<T = any> = (...args: any[]) => T;

export enum ServiceLifetime {
  Transient = 'transient',
  Singleton = 'singleton',
  Scoped = 'scoped',
}

export interface ServiceDescriptor<T = any> {
  identifier: ServiceIdentifier<T>;
  implementation?: Constructor<T>;
  factory?: Factory<T>;
  instance?: T;
  lifetime: ServiceLifetime;
  dependencies?: ServiceIdentifier[];
}

export interface IContainer {
  register<T>(
    identifier: ServiceIdentifier<T>,
    implementation: Constructor<T>,
    lifetime?: ServiceLifetime
  ): IContainer;

  registerFactory<T>(
    identifier: ServiceIdentifier<T>,
    factory: Factory<T>,
    lifetime?: ServiceLifetime
  ): IContainer;

  registerInstance<T>(identifier: ServiceIdentifier<T>, instance: T): IContainer;

  resolve<T>(identifier: ServiceIdentifier<T>): T;
  resolveAll<T>(identifier: ServiceIdentifier<T>): T[];
  isRegistered<T>(identifier: ServiceIdentifier<T>): boolean;
  createScope(): IContainer;
}

export class Container implements IContainer {
  private services = new Map<ServiceIdentifier, ServiceDescriptor>();
  private instances = new Map<ServiceIdentifier, any>();
  private parent?: Container;
  private isScoped = false;

  constructor(parent?: Container) {
    this.parent = parent;
    this.isScoped = !!parent;
  }

  register<T>(
    identifier: ServiceIdentifier<T>,
    implementation: Constructor<T>,
    lifetime: ServiceLifetime = ServiceLifetime.Transient
  ): IContainer {
    const descriptor: ServiceDescriptor<T> = {
      identifier,
      implementation,
      lifetime,
      dependencies: this.extractDependencies(implementation),
    };

    this.services.set(identifier, descriptor);
    return this;
  }

  registerFactory<T>(
    identifier: ServiceIdentifier<T>,
    factory: Factory<T>,
    lifetime: ServiceLifetime = ServiceLifetime.Transient
  ): IContainer {
    const descriptor: ServiceDescriptor<T> = {
      identifier,
      factory,
      lifetime,
    };

    this.services.set(identifier, descriptor);
    return this;
  }

  registerInstance<T>(identifier: ServiceIdentifier<T>, instance: T): IContainer {
    const descriptor: ServiceDescriptor<T> = {
      identifier,
      instance,
      lifetime: ServiceLifetime.Singleton,
    };

    this.services.set(identifier, descriptor);
    this.instances.set(identifier, instance);
    return this;
  }

  resolve<T>(identifier: ServiceIdentifier<T>): T {
    const descriptor = this.getDescriptor(identifier);

    if (!descriptor) {
      throw new Error(`Service ${String(identifier)} is not registered`);
    }

    return this.createInstance(descriptor);
  }

  resolveAll<T>(identifier: ServiceIdentifier<T>): T[] {
    const instances: T[] = [];

    // Collect from parent containers
    if (this.parent) {
      instances.push(...this.parent.resolveAll<T>(identifier));
    }

    // Collect from current container
    for (const [key, descriptor] of this.services) {
      if (key === identifier) {
        instances.push(this.createInstance(descriptor));
      }
    }

    return instances;
  }

  isRegistered<T>(identifier: ServiceIdentifier<T>): boolean {
    return this.services.has(identifier) || (this.parent?.isRegistered(identifier) ?? false);
  }

  createScope(): IContainer {
    return new Container(this);
  }

  private getDescriptor<T>(identifier: ServiceIdentifier<T>): ServiceDescriptor<T> | undefined {
    const descriptor = this.services.get(identifier);
    if (descriptor) {
      return descriptor;
    }

    return this.parent?.getDescriptor(identifier);
  }

  private createInstance<T>(descriptor: ServiceDescriptor<T>): T {
    // Check for existing singleton instance
    if (descriptor.lifetime === ServiceLifetime.Singleton) {
      const existingInstance = this.instances.get(descriptor.identifier);
      if (existingInstance) {
        return existingInstance;
      }
    }

    // Check for scoped instance
    if (descriptor.lifetime === ServiceLifetime.Scoped && this.isScoped) {
      const existingInstance = this.instances.get(descriptor.identifier);
      if (existingInstance) {
        return existingInstance;
      }
    }

    let instance: T;

    if (descriptor.instance) {
      instance = descriptor.instance;
    } else if (descriptor.factory) {
      instance = descriptor.factory();
    } else if (descriptor.implementation) {
      const dependencies = this.resolveDependencies(descriptor.dependencies || []);
      instance = new descriptor.implementation(...dependencies);
    } else {
      throw new Error(`Cannot create instance for ${String(descriptor.identifier)}`);
    }

    // Store instance for singleton and scoped services
    if (
      descriptor.lifetime === ServiceLifetime.Singleton ||
      (descriptor.lifetime === ServiceLifetime.Scoped && this.isScoped)
    ) {
      this.instances.set(descriptor.identifier, instance);
    }

    return instance;
  }

  private resolveDependencies(dependencies: ServiceIdentifier[]): any[] {
    return dependencies.map(dep => this.resolve(dep));
  }

  private extractDependencies<T>(constructor: Constructor<T>): ServiceIdentifier[] {
    // В реальном проекте здесь можно использовать reflect-metadata
    // для автоматического извлечения зависимостей из декораторов
    return [];
  }
}

// Глобальный контейнер
export const container = new Container();

// Декораторы для удобства использования
export function Injectable<T>(identifier?: ServiceIdentifier<T>) {
  return function (target: Constructor<T>) {
    const id = identifier || target;
    container.register(id, target);
    return target;
  };
}

export function Inject<T>(identifier: ServiceIdentifier<T>) {
  return function (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
    // Здесь можно реализовать логику для автоматического внедрения зависимостей
    // используя reflect-metadata
  };
}

// Утилиты для работы с контейнером
export class ServiceLocator {
  static resolve<T>(identifier: ServiceIdentifier<T>): T {
    return container.resolve(identifier);
  }

  static resolveAll<T>(identifier: ServiceIdentifier<T>): T[] {
    return container.resolveAll(identifier);
  }

  static isRegistered<T>(identifier: ServiceIdentifier<T>): boolean {
    return container.isRegistered(identifier);
  }
}

// Типы для сервисов
export const TYPES = {
  // Core services
  Logger: Symbol.for('Logger'),
  ConfigManager: Symbol.for('ConfigManager'),
  EventBus: Symbol.for('EventBus'),

  // Security services
  SecurityManager: Symbol.for('SecurityManager'),
  AuthenticationService: Symbol.for('AuthenticationService'),
  AuthorizationService: Symbol.for('AuthorizationService'),

  // Performance services
  PerformanceMonitor: Symbol.for('PerformanceMonitor'),
  CacheManager: Symbol.for('CacheManager'),

  // Storage services
  StorageManager: Symbol.for('StorageManager'),
  DatabaseService: Symbol.for('DatabaseService'),

  // Network services
  NetworkManager: Symbol.for('NetworkManager'),
  HttpClient: Symbol.for('HttpClient'),

  // UI services
  ThemeManager: Symbol.for('ThemeManager'),
  NotificationService: Symbol.for('NotificationService'),

  // Analytics services
  AnalyticsService: Symbol.for('AnalyticsService'),
  MetricsCollector: Symbol.for('MetricsCollector'),

  // Extension services
  ExtensionManager: Symbol.for('ExtensionManager'),
  PluginLoader: Symbol.for('PluginLoader'),
} as const;

export type ServiceTypes = (typeof TYPES)[keyof typeof TYPES];
