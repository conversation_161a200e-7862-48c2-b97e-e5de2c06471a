/**
 * Service Worker Manager для кэширования, офлайн-режима и push-уведомлений
 */

export interface CacheStrategy {
  name: string;
  pattern: RegExp;
  strategy:
    | 'cache-first'
    | 'network-first'
    | 'cache-only'
    | 'network-only'
    | 'stale-while-revalidate';
  cacheName: string;
  maxEntries?: number;
  maxAgeSeconds?: number;
}

export interface ServiceWorkerConfig {
  cacheStrategies: CacheStrategy[];
  enablePushNotifications: boolean;
  enableBackgroundSync: boolean;
  enablePeriodicSync: boolean;
  offlinePageUrl: string;
  skipWaiting: boolean;
  clientsClaim: boolean;
}

export interface SyncTask {
  id: string;
  type: string;
  data: any;
  retryCount: number;
  maxRetries: number;
  createdAt: number;
}

export class ServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null;
  private config: ServiceWorkerConfig;
  private syncTasks: Map<string, SyncTask> = new Map();
  private isOnline = navigator.onLine;

  constructor(config: ServiceWorkerConfig) {
    this.config = config;
    this.setupEventListeners();
  }

  /**
   * Регистрирует Service Worker
   */
  async register(scriptUrl: string): Promise<ServiceWorkerRegistration> {
    if (!('serviceWorker' in navigator)) {
      throw new Error('Service Worker not supported');
    }

    try {
      this.registration = await navigator.serviceWorker.register(scriptUrl, {
        scope: '/',
        updateViaCache: 'none',
      });

      console.log('Service Worker registered:', this.registration);

      // Настраиваем обработчики событий
      this.setupServiceWorkerEventHandlers();

      // Отправляем конфигурацию в Service Worker
      await this.sendConfigToServiceWorker();

      return this.registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      throw error;
    }
  }

  /**
   * Обновляет Service Worker
   */
  async update(): Promise<void> {
    if (!this.registration) {
      throw new Error('Service Worker not registered');
    }

    try {
      await this.registration.update();
      console.log('Service Worker updated');
    } catch (error) {
      console.error('Service Worker update failed:', error);
      throw error;
    }
  }

  /**
   * Отменяет регистрацию Service Worker
   */
  async unregister(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      const result = await this.registration.unregister();
      this.registration = null;
      console.log('Service Worker unregistered');
      return result;
    } catch (error) {
      console.error('Service Worker unregistration failed:', error);
      throw error;
    }
  }

  /**
   * Проверяет статус Service Worker
   */
  getStatus(): {
    isRegistered: boolean;
    isActive: boolean;
    isWaiting: boolean;
    isInstalling: boolean;
  } {
    if (!this.registration) {
      return {
        isRegistered: false,
        isActive: false,
        isWaiting: false,
        isInstalling: false,
      };
    }

    return {
      isRegistered: true,
      isActive: !!this.registration.active,
      isWaiting: !!this.registration.waiting,
      isInstalling: !!this.registration.installing,
    };
  }

  /**
   * Отправляет сообщение в Service Worker
   */
  async sendMessage(message: any): Promise<any> {
    if (!this.registration?.active) {
      throw new Error('Service Worker not active');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();

      messageChannel.port1.onmessage = event => {
        if (event.data.error) {
          reject(new Error(event.data.error));
        } else {
          resolve(event.data);
        }
      };

      this.registration!.active!.postMessage(message, [messageChannel.port2]);
    });
  }

  /**
   * Добавляет URL в кэш
   */
  async addToCache(urls: string[], cacheName: string = 'runtime'): Promise<void> {
    try {
      await this.sendMessage({
        type: 'ADD_TO_CACHE',
        urls,
        cacheName,
      });
    } catch (error) {
      console.error('Failed to add URLs to cache:', error);
      throw error;
    }
  }

  /**
   * Удаляет URL из кэша
   */
  async removeFromCache(urls: string[], cacheName: string = 'runtime'): Promise<void> {
    try {
      await this.sendMessage({
        type: 'REMOVE_FROM_CACHE',
        urls,
        cacheName,
      });
    } catch (error) {
      console.error('Failed to remove URLs from cache:', error);
      throw error;
    }
  }

  /**
   * Очищает кэш
   */
  async clearCache(cacheName?: string): Promise<void> {
    try {
      await this.sendMessage({
        type: 'CLEAR_CACHE',
        cacheName,
      });
    } catch (error) {
      console.error('Failed to clear cache:', error);
      throw error;
    }
  }

  /**
   * Получает информацию о кэше
   */
  async getCacheInfo(): Promise<{ name: string; size: number; entries: number }[]> {
    try {
      const response = await this.sendMessage({
        type: 'GET_CACHE_INFO',
      });
      return response.caches || [];
    } catch (error) {
      console.error('Failed to get cache info:', error);
      throw error;
    }
  }

  /**
   * Добавляет задачу для фоновой синхронизации
   */
  async addSyncTask(type: string, data: any, maxRetries: number = 3): Promise<string> {
    const taskId = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const task: SyncTask = {
      id: taskId,
      type,
      data,
      retryCount: 0,
      maxRetries,
      createdAt: Date.now(),
    };

    this.syncTasks.set(taskId, task);

    if (this.isOnline) {
      // Если онлайн, выполняем сразу
      await this.executeSyncTask(task);
    } else {
      // Если офлайн, регистрируем для фоновой синхронизации
      await this.registerBackgroundSync(taskId);
    }

    return taskId;
  }

  /**
   * Подписывается на push-уведомления
   */
  async subscribeToPushNotifications(): Promise<PushSubscription | null> {
    if (!this.registration) {
      throw new Error('Service Worker not registered');
    }

    if (!('PushManager' in window)) {
      throw new Error('Push notifications not supported');
    }

    try {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        throw new Error('Push notification permission denied');
      }

      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(process.env.VAPID_PUBLIC_KEY || ''),
      });

      console.log('Push subscription created:', subscription);
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      throw error;
    }
  }

  /**
   * Отписывается от push-уведомлений
   */
  async unsubscribeFromPushNotifications(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      const subscription = await this.registration.pushManager.getSubscription();
      if (subscription) {
        return await subscription.unsubscribe();
      }
      return true;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      throw error;
    }
  }

  /**
   * Проверяет, поддерживается ли офлайн-режим
   */
  isOfflineSupported(): boolean {
    return 'serviceWorker' in navigator && 'caches' in window;
  }

  /**
   * Проверяет, доступно ли приложение офлайн
   */
  async isOfflineReady(): Promise<boolean> {
    if (!this.isOfflineSupported()) {
      return false;
    }

    try {
      const cacheNames = await caches.keys();
      return cacheNames.length > 0;
    } catch (error) {
      return false;
    }
  }

  private setupEventListeners(): void {
    // Отслеживаем изменения сетевого статуса
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processPendingSyncTasks();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Отслеживаем обновления Service Worker
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('Service Worker controller changed');
      window.location.reload();
    });
  }

  private setupServiceWorkerEventHandlers(): void {
    if (!this.registration) return;

    // Обработка обновлений
    this.registration.addEventListener('updatefound', () => {
      const newWorker = this.registration!.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // Новая версия доступна
            this.dispatchEvent(new CustomEvent('sw-update-available'));
          }
        });
      }
    });

    // Обработка сообщений от Service Worker
    navigator.serviceWorker.addEventListener('message', event => {
      this.handleServiceWorkerMessage(event.data);
    });
  }

  private async sendConfigToServiceWorker(): Promise<void> {
    try {
      await this.sendMessage({
        type: 'CONFIG',
        config: this.config,
      });
    } catch (error) {
      console.error('Failed to send config to Service Worker:', error);
    }
  }

  private async registerBackgroundSync(taskId: string): Promise<void> {
    if (!this.registration || !('sync' in this.registration)) {
      console.warn('Background Sync not supported');
      return;
    }

    try {
      await this.registration.sync.register(taskId);
    } catch (error) {
      console.error('Failed to register background sync:', error);
    }
  }

  private async executeSyncTask(task: SyncTask): Promise<void> {
    try {
      await this.sendMessage({
        type: 'EXECUTE_SYNC_TASK',
        task,
      });

      this.syncTasks.delete(task.id);
    } catch (error) {
      task.retryCount++;

      if (task.retryCount >= task.maxRetries) {
        console.error(`Sync task ${task.id} failed after ${task.maxRetries} retries`);
        this.syncTasks.delete(task.id);
      } else {
        console.warn(
          `Sync task ${task.id} failed, retrying (${task.retryCount}/${task.maxRetries})`
        );
      }
    }
  }

  private async processPendingSyncTasks(): Promise<void> {
    const pendingTasks = Array.from(this.syncTasks.values());

    for (const task of pendingTasks) {
      await this.executeSyncTask(task);
    }
  }

  private handleServiceWorkerMessage(message: any): void {
    switch (message.type) {
      case 'CACHE_UPDATED':
        this.dispatchEvent(new CustomEvent('sw-cache-updated', { detail: message.data }));
        break;
      case 'SYNC_COMPLETED':
        this.dispatchEvent(new CustomEvent('sw-sync-completed', { detail: message.data }));
        break;
      case 'ERROR':
        this.dispatchEvent(new CustomEvent('sw-error', { detail: message.error }));
        break;
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private dispatchEvent(event: CustomEvent): void {
    window.dispatchEvent(event);
  }
}

// Фабрика для создания Service Worker Manager
export class ServiceWorkerFactory {
  static create(config?: Partial<ServiceWorkerConfig>): ServiceWorkerManager {
    const defaultConfig: ServiceWorkerConfig = {
      cacheStrategies: [
        {
          name: 'static-assets',
          pattern: /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2)$/,
          strategy: 'cache-first',
          cacheName: 'static-assets',
          maxEntries: 100,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
        },
        {
          name: 'api-calls',
          pattern: /\/api\//,
          strategy: 'network-first',
          cacheName: 'api-cache',
          maxEntries: 50,
          maxAgeSeconds: 5 * 60, // 5 minutes
        },
        {
          name: 'pages',
          pattern: /\.html$/,
          strategy: 'stale-while-revalidate',
          cacheName: 'pages',
          maxEntries: 20,
          maxAgeSeconds: 24 * 60 * 60, // 1 day
        },
      ],
      enablePushNotifications: true,
      enableBackgroundSync: true,
      enablePeriodicSync: false,
      offlinePageUrl: '/offline.html',
      skipWaiting: true,
      clientsClaim: true,
      ...config,
    };

    return new ServiceWorkerManager(defaultConfig);
  }
}

// Глобальный экземпляр
export const serviceWorkerManager = ServiceWorkerFactory.create();
