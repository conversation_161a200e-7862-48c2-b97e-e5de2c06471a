/**
 * Система ленивой загрузки компонентов с поддержкой
 * предзагрузки, fallback компонентов и обработки ошибок
 */

import React, { ComponentType, LazyExoticComponent, ReactNode, Suspense } from 'react';

export interface LazyLoadOptions {
  fallback?: ReactNode;
  errorFallback?: ComponentType<{ error: Error; retry: () => void }>;
  preload?: boolean;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
}

export interface LazyComponentProps {
  loading?: boolean;
  error?: Error | null;
  retry?: () => void;
}

// Кэш для загруженных компонентов
const componentCache = new Map<string, LazyExoticComponent<any>>();
const preloadCache = new Map<string, Promise<any>>();

/**
 * Создает ленивый компонент с расширенными возможностями
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> {
  const {
    fallback = <div>Loading...</div>,
    errorFallback: ErrorFallback,
    timeout = 10000,
    retryCount = 3,
    retryDelay = 1000,
  } = options;

  // Создаем уникальный ключ для кэширования
  const cacheKey = importFn.toString();

  // Проверяем кэш
  if (componentCache.has(cacheKey)) {
    return componentCache.get(cacheKey)!;
  }

  // Функция загрузки с повторными попытками
  const loadWithRetry = async (attempt = 1): Promise<{ default: T }> => {
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Component load timeout')), timeout);
      });

      const result = await Promise.race([importFn(), timeoutPromise]);
      return result;
    } catch (error) {
      if (attempt < retryCount) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        return loadWithRetry(attempt + 1);
      }
      throw error;
    }
  };

  const LazyComponent = React.lazy(loadWithRetry);

  // Оборачиваем в компонент с обработкой ошибок
  const WrappedComponent = React.forwardRef<any, any>((props, ref) => {
    return (
      <ErrorBoundary
        fallback={ErrorFallback}
        onError={error => console.error('Lazy component error:', error)}
      >
        <Suspense fallback={fallback}>
          <LazyComponent {...props} ref={ref} />
        </Suspense>
      </ErrorBoundary>
    );
  });

  WrappedComponent.displayName = `LazyComponent(${LazyComponent.displayName || 'Unknown'})`;

  // Добавляем метод предзагрузки
  (WrappedComponent as any).preload = () => {
    if (!preloadCache.has(cacheKey)) {
      preloadCache.set(cacheKey, loadWithRetry());
    }
    return preloadCache.get(cacheKey);
  };

  // Кэшируем компонент
  componentCache.set(cacheKey, WrappedComponent as LazyExoticComponent<T>);

  return WrappedComponent as LazyExoticComponent<T>;
}

/**
 * Error Boundary для обработки ошибок загрузки
 */
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error) => void;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} retry={this.retry} />;
      }

      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>{this.state.error.message}</p>
          <button onClick={this.retry}>Try again</button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * HOC для ленивой загрузки
 */
export function withLazyLoading<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options?: LazyLoadOptions
) {
  return createLazyComponent(importFn, options);
}

/**
 * Hook для предзагрузки компонентов
 */
export function usePreload() {
  const preload = React.useCallback(
    (importFn: () => Promise<{ default: ComponentType<any> }>, condition: boolean = true) => {
      if (!condition) return;

      const cacheKey = importFn.toString();
      if (!preloadCache.has(cacheKey)) {
        preloadCache.set(cacheKey, importFn());
      }
    },
    []
  );

  return preload;
}

/**
 * Hook для ленивой загрузки с состоянием
 */
export function useLazyLoad<T>(
  importFn: () => Promise<{ default: T }>,
  deps: React.DependencyList = []
) {
  const [state, setState] = React.useState<{
    component: T | null;
    loading: boolean;
    error: Error | null;
  }>({
    component: null,
    loading: false,
    error: null,
  });

  const load = React.useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const module = await importFn();
      setState({
        component: module.default,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState({
        component: null,
        loading: false,
        error: error as Error,
      });
    }
  }, deps);

  React.useEffect(() => {
    load();
  }, [load]);

  return {
    ...state,
    reload: load,
  };
}

/**
 * Компонент для ленивой загрузки изображений
 */
interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  placeholder?: string;
  threshold?: number;
  rootMargin?: string;
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  placeholder,
  threshold = 0.1,
  rootMargin = '50px',
  ...props
}) => {
  const [imageSrc, setImageSrc] = React.useState(placeholder || '');
  const [isLoaded, setIsLoaded] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setImageSrc(src);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [src, threshold, rootMargin]);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  return (
    <img
      {...props}
      ref={imgRef}
      src={imageSrc}
      onLoad={handleLoad}
      style={{
        ...props.style,
        opacity: isLoaded ? 1 : 0.5,
        transition: 'opacity 0.3s ease',
      }}
    />
  );
};

/**
 * Утилиты для работы с динамическими импортами
 */
export class LazyLoadManager {
  private static preloadQueue = new Set<() => Promise<any>>();
  private static isPreloading = false;

  /**
   * Добавляет компонент в очередь предзагрузки
   */
  static addToPreloadQueue(importFn: () => Promise<any>) {
    this.preloadQueue.add(importFn);
  }

  /**
   * Запускает предзагрузку всех компонентов в очереди
   */
  static async preloadAll() {
    if (this.isPreloading) return;

    this.isPreloading = true;

    try {
      await Promise.all(
        Array.from(this.preloadQueue).map(importFn =>
          importFn().catch(error => console.warn('Preload failed:', error))
        )
      );
    } finally {
      this.isPreloading = false;
      this.preloadQueue.clear();
    }
  }

  /**
   * Предзагружает компонент при наведении
   */
  static preloadOnHover(importFn: () => Promise<any>) {
    return {
      onMouseEnter: () => {
        const cacheKey = importFn.toString();
        if (!preloadCache.has(cacheKey)) {
          preloadCache.set(cacheKey, importFn());
        }
      },
    };
  }

  /**
   * Предзагружает компонент при фокусе
   */
  static preloadOnFocus(importFn: () => Promise<any>) {
    return {
      onFocus: () => {
        const cacheKey = importFn.toString();
        if (!preloadCache.has(cacheKey)) {
          preloadCache.set(cacheKey, importFn());
        }
      },
    };
  }

  /**
   * Очищает кэш компонентов
   */
  static clearCache() {
    componentCache.clear();
    preloadCache.clear();
  }

  /**
   * Получает статистику кэша
   */
  static getCacheStats() {
    return {
      componentsInCache: componentCache.size,
      preloadedComponents: preloadCache.size,
    };
  }
}

// Экспорт готовых компонентов для часто используемых случаев
export const DefaultLoadingSpinner = () => (
  <div className="flex items-center justify-center p-4">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
  </div>
);

export const DefaultErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({
  error,
  retry,
}) => (
  <div className="flex flex-col items-center justify-center p-4 text-center">
    <h3 className="text-lg font-semibold text-red-600 mb-2">Failed to load component</h3>
    <p className="text-gray-600 mb-4">{error.message}</p>
    <button
      onClick={retry}
      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
    >
      Try Again
    </button>
  </div>
);
