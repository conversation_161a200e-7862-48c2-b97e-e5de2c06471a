import React from 'react';
import { ModuleFederationPlugin } from '@module-federation/nextjs-mf';
import { createStore } from 'redux';
import { Provider } from 'react-redux';

// Типы для микрофронтендов
export interface MicroFrontend {
  name: string;
  url: string;
  scope: string;
  module: string;
  shared: string[];
}

// Конфигурация микрофронтендов
export const microFrontends: MicroFrontend[] = [
  {
    name: 'search',
    url: 'http://localhost:3001',
    scope: 'search',
    module: './SearchApp',
    shared: ['react', 'react-dom', '@mui/material']
  },
  {
    name: 'bookmarks',
    url: 'http://localhost:3002',
    scope: 'bookmarks',
    module: './BookmarksApp',
    shared: ['react', 'react-dom', '@mui/material']
  },
  {
    name: 'history',
    url: 'http://localhost:3003',
    scope: 'history',
    module: './HistoryApp',
    shared: ['react', 'react-dom', '@mui/material']
  },
  {
    name: 'extensions',
    url: 'http://localhost:3004',
    scope: 'extensions',
    module: './ExtensionsApp',
    shared: ['react', 'react-dom', '@mui/material']
  }
];

// Функция для загрузки микрофронтенда
export async function loadMicroFrontend(name: string) {
  const mf = microFrontends.find(mf => mf.name === name);
  if (!mf) {
    throw new Error(`Micro frontend ${name} not found`);
  }

  // Загрузка скрипта
  await loadScript(mf.url);
  
  // Инициализация модуля
  const module = await (window as any)[mf.scope].get(mf.module);
  return module;
}

// Вспомогательная функция для загрузки скрипта
function loadScript(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = url;
    script.onload = () => resolve();
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

// Провайдер для микрофронтендов
export const MicroFrontendProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Provider store={createStore(() => ({}))}>
      {children}
    </Provider>
  );
};

// Хук для использования микрофронтенда
export function useMicroFrontend(name: string) {
  const [module, setModule] = React.useState<any>(null);
  const [error, setError] = React.useState<Error | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    loadMicroFrontend(name)
      .then(setModule)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [name]);

  return { module, error, loading };
} 