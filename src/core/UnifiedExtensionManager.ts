import * as crypto from 'crypto';
import { EventEmitter } from 'events';
import * as fs from 'fs/promises';
import * as path from 'path';

import { BrowserWindow, app, ipcMain } from 'electron';

import { APP_CONFIG } from '../config/app.config';
import { configManager } from './ConfigurationManager';
import { ErrorManager } from './ErrorManager';
import { logger } from '../logging/Logger';
import { SecurityManager } from '../security/SecurityManager';
import { securityScanner } from '../security/SecurityScanner';

// Unified Extension interface combining all features
export interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  permissions: ExtensionPermission[];
  manifest: ExtensionManifest;
  enabled: boolean;
  installed: boolean;
  verified: boolean;
  sandboxed: boolean;
  status: 'enabled' | 'disabled' | 'error' | 'loading';
  path: string;
  
  // Performance metrics
  loadTime: number;
  memoryUsage: number;
  cpuUsage: number;
  networkRequests: number;
  
  // Metadata
  lastUpdated: number;
  installDate: number;
  updateDate: number;
  updateAvailable: boolean;
  rating: number;
  downloadCount: number;
  size: number;
  hash: string;
}

export interface ExtensionPermission {
  name: string;
  description: string;
  required: boolean;
  granted: boolean;
  type: 'api' | 'storage' | 'network' | 'system' | 'ui';
}

export interface ExtensionManifest {
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  permissions: string[];
  background?: {
    scripts: string[];
    persistent: boolean;
  };
  content_scripts?: Array<{
    matches: string[];
    js: string[];
    css?: string[];
    run_at?: 'document_start' | 'document_end' | 'document_idle';
  }>;
  browser_action?: {
    default_title: string;
    default_popup?: string;
    default_icon?: string;
  };
  icons?: Record<string, string>;
  web_accessible_resources?: string[];
  host_permissions?: string[];
}

interface ExtensionContext {
  extension: Extension;
  window: BrowserWindow;
  sandbox?: any;
}

interface SecurityCheckResult {
  safe: boolean;
  reason?: string;
  warnings: string[];
}

export class UnifiedExtensionManager extends EventEmitter {
  private static instance: UnifiedExtensionManager;
  private extensions: Map<string, Extension> = new Map();
  private extensionContexts: Map<string, ExtensionContext[]> = new Map();
  private isInitialized: boolean = false;
  private securityManager: SecurityManager;
  private performanceMetrics: Map<string, any> = new Map();

  private constructor() {
    super();
    this.securityManager = SecurityManager.getInstance();
    this.initialize();
  }

  public static getInstance(): UnifiedExtensionManager {
    if (!UnifiedExtensionManager.instance) {
      UnifiedExtensionManager.instance = new UnifiedExtensionManager();
    }
    return UnifiedExtensionManager.instance;
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Create extensions directory if it doesn't exist
      const extensionsDir = path.join(app.getPath('userData'), 'extensions');
      await fs.mkdir(extensionsDir, { recursive: true });

      // Load installed extensions
      await this.loadInstalledExtensions();

      // Setup IPC handlers
      this.setupIpcHandlers();

      // Setup performance monitoring
      this.setupPerformanceMonitoring();

      this.isInitialized = true;
      this.emit('initialized');
      
      logger.info('ExtensionManager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ExtensionManager:', error);
      throw error;
    }
  }

  private async loadInstalledExtensions(): Promise<void> {
    const extensionsDir = path.join(app.getPath('userData'), 'extensions');
    
    try {
      const entries = await fs.readdir(extensionsDir, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          try {
            await this.loadExtension(path.join(extensionsDir, entry.name));
          } catch (error) {
            logger.error(`Failed to load extension ${entry.name}:`, error);
          }
        }
      }
    } catch (error) {
      logger.error('Failed to load installed extensions:', error);
    }
  }

  private async loadExtension(extensionPath: string): Promise<void> {
    const manifestPath = path.join(extensionPath, 'manifest.json');
    
    try {
      const manifestData = await fs.readFile(manifestPath, 'utf-8');
      const manifest: ExtensionManifest = JSON.parse(manifestData);
      
      const extension: Extension = {
        id: path.basename(extensionPath),
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: manifest.author,
        homepage: manifest.homepage,
        permissions: this.parsePermissions(manifest.permissions),
        manifest,
        enabled: true,
        installed: true,
        verified: false,
        sandboxed: true,
        status: 'enabled',
        path: extensionPath,
        loadTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        networkRequests: 0,
        lastUpdated: Date.now(),
        installDate: Date.now(),
        updateDate: Date.now(),
        updateAvailable: false,
        rating: 0,
        downloadCount: 0,
        size: await this.calculateExtensionSize(extensionPath),
        hash: await this.calculateHash(extensionPath),
      };

      this.extensions.set(extension.id, extension);
      this.emit('extension-loaded', extension);
      
      logger.debug(`Extension loaded: ${extension.name} v${extension.version}`);
    } catch (error) {
      logger.error(`Failed to load extension from ${extensionPath}:`, error);
      throw error;
    }
  }

  public async installExtension(
    source: string,
    type: 'file' | 'url' | 'store' = 'file'
  ): Promise<Extension> {
    try {
      logger.info(`Installing extension from ${source} (type: ${type})`);

      // Extract or download extension
      const tempPath = await this.extractExtension(source, type);
      
      // Load and validate manifest
      const manifest = await this.extractManifest(tempPath);
      
      // Perform security check
      const securityCheck = await this.performSecurityCheck(manifest, tempPath);
      if (!securityCheck.safe) {
        throw new Error(`Security check failed: ${securityCheck.reason}`);
      }

      // Request permissions
      const permissions = this.parsePermissions(manifest.permissions);
      const permissionGranted = await this.requestPermissions(manifest.name, permissions);
      if (!permissionGranted) {
        throw new Error('Required permissions not granted');
      }

      // Install extension
      const extensionId = this.generateExtensionId(manifest.name);
      const extensionsDir = path.join(app.getPath('userData'), 'extensions');
      const targetPath = path.join(extensionsDir, extensionId);

      await this.copyDirectory(tempPath, targetPath);

      const extension: Extension = {
        id: extensionId,
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: manifest.author,
        homepage: manifest.homepage,
        permissions,
        manifest,
        enabled: true,
        installed: true,
        verified: securityCheck.warnings.length === 0,
        sandboxed: true,
        status: 'enabled',
        path: targetPath,
        loadTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        networkRequests: 0,
        lastUpdated: Date.now(),
        installDate: Date.now(),
        updateDate: Date.now(),
        updateAvailable: false,
        rating: 0,
        downloadCount: 0,
        size: await this.calculateExtensionSize(targetPath),
        hash: await this.calculateHash(targetPath),
      };

      this.extensions.set(extension.id, extension);
      this.emit('extension-installed', extension);
      
      logger.info(`Extension installed successfully: ${extension.name}`);
      return extension;
    } catch (error) {
      logger.error('Extension installation failed:', error);
      throw error;
    }
  }

  public async uninstallExtension(extensionId: string): Promise<void> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    try {
      // Stop extension if running
      await this.stopExtension(extensionId);
      
      // Remove extension files
      await fs.rm(extension.path, { recursive: true, force: true });
      
      // Remove from memory
      this.extensions.delete(extensionId);
      this.extensionContexts.delete(extensionId);
      
      this.emit('extension-uninstalled', extension);
      logger.info(`Extension uninstalled: ${extension.name}`);
    } catch (error) {
      logger.error(`Failed to uninstall extension ${extensionId}:`, error);
      throw error;
    }
  }

  public async enableExtension(extensionId: string): Promise<void> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    extension.enabled = true;
    extension.status = 'enabled';
    this.emit('extension-enabled', extension);
    
    logger.info(`Extension enabled: ${extension.name}`);
  }

  public async disableExtension(extensionId: string): Promise<void> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    await this.stopExtension(extensionId);
    extension.enabled = false;
    extension.status = 'disabled';
    this.emit('extension-disabled', extension);
    
    logger.info(`Extension disabled: ${extension.name}`);
  }

  private async stopExtension(extensionId: string): Promise<void> {
    const contexts = this.extensionContexts.get(extensionId);
    if (contexts) {
      // Clean up extension contexts
      contexts.forEach(context => {
        // Cleanup logic here
      });
      this.extensionContexts.delete(extensionId);
    }
  }

  // Utility methods
  private parsePermissions(permissions: string[]): ExtensionPermission[] {
    return permissions.map(permission => ({
      name: permission,
      description: this.getPermissionDescription(permission),
      required: true,
      granted: false,
      type: this.getPermissionType(permission),
    }));
  }

  private getPermissionDescription(permission: string): string {
    const descriptions: Record<string, string> = {
      'storage': 'Access to local storage',
      'tabs': 'Access to browser tabs',
      'activeTab': 'Access to active tab',
      'bookmarks': 'Access to bookmarks',
      'history': 'Access to browsing history',
      'cookies': 'Access to cookies',
      'webRequest': 'Access to web requests',
      'webNavigation': 'Access to navigation events',
      'notifications': 'Show notifications',
      'contextMenus': 'Add context menu items',
    };
    return descriptions[permission] || `Access to ${permission}`;
  }

  private getPermissionType(permission: string): 'api' | 'storage' | 'network' | 'system' | 'ui' {
    if (['storage'].includes(permission)) return 'storage';
    if (['webRequest', 'webNavigation'].includes(permission)) return 'network';
    if (['notifications', 'contextMenus'].includes(permission)) return 'ui';
    if (['tabs', 'activeTab', 'bookmarks', 'history', 'cookies'].includes(permission)) return 'api';
    return 'system';
  }

  private async requestPermissions(extensionName: string, permissions: ExtensionPermission[]): Promise<boolean> {
    // In a real implementation, show a dialog to the user
    // For now, auto-grant all permissions
    permissions.forEach(permission => {
      permission.granted = true;
    });
    return true;
  }

  private async performSecurityCheck(manifest: ExtensionManifest, extensionPath: string): Promise<SecurityCheckResult> {
    const warnings: string[] = [];

    try {
      // Check for dangerous permissions
      const dangerousPermissions = ['webRequest', 'webRequestBlocking', 'proxy'];
      const hasDangerousPermissions = manifest.permissions.some(p => dangerousPermissions.includes(p));

      if (hasDangerousPermissions) {
        warnings.push('Extension requests dangerous permissions');
      }

      // Scan extension files
      const scanResult = await securityScanner.scanExtension(extensionPath);
      if (scanResult.threats.length > 0) {
        warnings.push(`Security threats detected: ${scanResult.threats.join(', ')}`);
      }

      return {
        safe: warnings.length === 0,
        reason: warnings.length > 0 ? warnings[0] : undefined,
        warnings,
      };
    } catch (error) {
      return {
        safe: false,
        reason: 'Security check failed',
        warnings: ['Failed to perform security check'],
      };
    }
  }

  private async extractManifest(extensionPath: string): Promise<ExtensionManifest> {
    const manifestPath = path.join(extensionPath, 'manifest.json');
    const manifestData = await fs.readFile(manifestPath, 'utf-8');
    return JSON.parse(manifestData);
  }

  private async extractExtension(source: string, type: 'file' | 'url' | 'store'): Promise<string> {
    // Implementation depends on type
    // For now, assume it's a local directory
    return source;
  }

  private generateExtensionId(name: string): string {
    return name.toLowerCase().replace(/[^a-z0-9]/g, '-') + '-' + Date.now();
  }

  private async calculateExtensionSize(extensionPath: string): Promise<number> {
    try {
      const stats = await fs.stat(extensionPath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private async calculateHash(extensionPath: string): Promise<string> {
    try {
      const manifestPath = path.join(extensionPath, 'manifest.json');
      const manifestData = await fs.readFile(manifestPath, 'utf-8');
      return crypto.createHash('sha256').update(manifestData).digest('hex');
    } catch {
      return '';
    }
  }

  private async copyDirectory(src: string, dest: string): Promise<void> {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  private setupIpcHandlers(): void {
    ipcMain.handle('extension:list', () => {
      return Array.from(this.extensions.values());
    });

    ipcMain.handle('extension:install', async (event, source: string, type: string) => {
      return this.installExtension(source, type as any);
    });

    ipcMain.handle('extension:uninstall', async (event, extensionId: string) => {
      return this.uninstallExtension(extensionId);
    });

    ipcMain.handle('extension:enable', async (event, extensionId: string) => {
      return this.enableExtension(extensionId);
    });

    ipcMain.handle('extension:disable', async (event, extensionId: string) => {
      return this.disableExtension(extensionId);
    });
  }

  private setupPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 5000);
  }

  private updatePerformanceMetrics(): void {
    // Update performance metrics for all extensions
    for (const extension of this.extensions.values()) {
      // In a real implementation, collect actual metrics
      this.performanceMetrics.set(extension.id, {
        memoryUsage: Math.random() * 100,
        cpuUsage: Math.random() * 50,
        networkRequests: Math.floor(Math.random() * 10),
      });
    }
  }

  // Public API methods
  public getExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  public getExtension(extensionId: string): Extension | undefined {
    return this.extensions.get(extensionId);
  }

  public getEnabledExtensions(): Extension[] {
    return this.getExtensions().filter(ext => ext.enabled);
  }

  public getPerformanceMetrics(extensionId: string): any {
    return this.performanceMetrics.get(extensionId);
  }
}

// Create singleton instance
export const extensionManager = UnifiedExtensionManager.getInstance();
