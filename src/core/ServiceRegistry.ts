/**
 * Service Registry and Lifecycle Manager
 * Manages service registration, initialization, and lifecycle
 */

import { EventEmitter } from 'events';
import { container } from './DependencyInjection';
import { logger } from './EnhancedLogger';

export interface ServiceMetadata {
  name: string;
  version: string;
  description: string;
  author?: string;
  dependencies: string[];
  optionalDependencies?: string[];
  tags: string[];
  priority: number;
  autoStart: boolean;
  healthCheck?: () => Promise<boolean>;
  config?: Record<string, any>;
}

export interface ServiceStatus {
  name: string;
  state: 'registered' | 'initializing' | 'running' | 'stopping' | 'stopped' | 'error';
  startTime?: Date;
  stopTime?: Date;
  error?: Error;
  health: 'healthy' | 'unhealthy' | 'unknown';
  lastHealthCheck?: Date;
  restartCount: number;
  metadata: ServiceMetadata;
}

export interface ServiceLifecycle {
  initialize?(): Promise<void>;
  start?(): Promise<void>;
  stop?(): Promise<void>;
  destroy?(): Promise<void>;
  healthCheck?(): Promise<boolean>;
}

export class ServiceRegistry extends EventEmitter {
  private services = new Map<string, ServiceStatus>();
  private instances = new Map<string, ServiceLifecycle>();
  private initializationOrder: string[] = [];
  private healthCheckInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.setupHealthChecking();
  }

  /**
   * Register a service with metadata
   */
  register(
    name: string,
    serviceClass: new (...args: any[]) => ServiceLifecycle,
    metadata: ServiceMetadata
  ): void {
    if (this.services.has(name)) {
      throw new Error(`Service ${name} is already registered`);
    }

    // Register with dependency container
    container.singleton(name, (...deps) => new serviceClass(...deps), metadata.dependencies);

    // Store service metadata
    const status: ServiceStatus = {
      name,
      state: 'registered',
      health: 'unknown',
      restartCount: 0,
      metadata,
    };

    this.services.set(name, status);
    this.emit('service-registered', { name, metadata });
  }

  /**
   * Initialize all registered services
   */
  async initializeAll(): Promise<void> {
    const services = this.getServicesInDependencyOrder();
    
    for (const serviceName of services) {
      await this.initializeService(serviceName);
    }
  }

  /**
   * Initialize a specific service
   */
  async initializeService(name: string): Promise<void> {
    const status = this.services.get(name);
    if (!status) {
      throw new Error(`Service ${name} is not registered`);
    }

    if (status.state !== 'registered') {
      return; // Already initialized or in process
    }

    try {
      status.state = 'initializing';
      this.emit('service-initializing', { name });

      // Initialize dependencies first
      for (const dep of status.metadata.dependencies) {
        await this.initializeService(dep);
      }

      // Resolve service instance
      const instance = await container.resolve<ServiceLifecycle>(name);
      this.instances.set(name, instance);

      // Call initialize method if available
      if (instance.initialize) {
        await instance.initialize();
      }

      status.state = 'running';
      status.startTime = new Date();
      this.emit('service-initialized', { name });

      // Auto-start if configured
      if (status.metadata.autoStart) {
        await this.startService(name);
      }
    } catch (error) {
      status.state = 'error';
      status.error = error as Error;
      this.emit('service-error', { name, error });
      throw error;
    }
  }

  /**
   * Start a service
   */
  async startService(name: string): Promise<void> {
    const status = this.services.get(name);
    const instance = this.instances.get(name);

    if (!status || !instance) {
      throw new Error(`Service ${name} is not initialized`);
    }

    if (status.state === 'running') {
      return; // Already running
    }

    try {
      if (instance.start) {
        await instance.start();
      }
      
      status.state = 'running';
      status.startTime = new Date();
      this.emit('service-started', { name });
    } catch (error) {
      status.state = 'error';
      status.error = error as Error;
      this.emit('service-error', { name, error });
      throw error;
    }
  }

  /**
   * Stop a service
   */
  async stopService(name: string): Promise<void> {
    const status = this.services.get(name);
    const instance = this.instances.get(name);

    if (!status || !instance) {
      return; // Service not found or not initialized
    }

    if (status.state !== 'running') {
      return; // Not running
    }

    try {
      status.state = 'stopping';
      this.emit('service-stopping', { name });

      if (instance.stop) {
        await instance.stop();
      }

      status.state = 'stopped';
      status.stopTime = new Date();
      this.emit('service-stopped', { name });
    } catch (error) {
      status.state = 'error';
      status.error = error as Error;
      this.emit('service-error', { name, error });
      throw error;
    }
  }

  /**
   * Stop all services
   */
  async stopAll(): Promise<void> {
    const services = this.getServicesInDependencyOrder().reverse();
    
    for (const serviceName of services) {
      await this.stopService(serviceName);
    }
  }

  /**
   * Restart a service
   */
  async restartService(name: string): Promise<void> {
    const status = this.services.get(name);
    if (!status) {
      throw new Error(`Service ${name} is not registered`);
    }

    await this.stopService(name);
    await this.startService(name);
    
    status.restartCount++;
    this.emit('service-restarted', { name, restartCount: status.restartCount });
  }

  /**
   * Get service status
   */
  getServiceStatus(name: string): ServiceStatus | undefined {
    return this.services.get(name);
  }

  /**
   * Get all service statuses
   */
  getAllServiceStatuses(): ServiceStatus[] {
    return Array.from(this.services.values());
  }

  /**
   * Get services by tag
   */
  getServicesByTag(tag: string): ServiceStatus[] {
    return Array.from(this.services.values()).filter(status =>
      status.metadata.tags.includes(tag)
    );
  }

  /**
   * Perform health check on a service
   */
  async checkServiceHealth(name: string): Promise<boolean> {
    const status = this.services.get(name);
    const instance = this.instances.get(name);

    if (!status || !instance) {
      return false;
    }

    try {
      let healthy = true;

      if (instance.healthCheck) {
        healthy = await instance.healthCheck();
      } else if (status.metadata.healthCheck) {
        healthy = await status.metadata.healthCheck();
      }

      status.health = healthy ? 'healthy' : 'unhealthy';
      status.lastHealthCheck = new Date();

      return healthy;
    } catch (error) {
      status.health = 'unhealthy';
      status.lastHealthCheck = new Date();
      status.error = error as Error;
      return false;
    }
  }

  /**
   * Get services in dependency order
   */
  private getServicesInDependencyOrder(): string[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const result: string[] = [];

    const visit = (name: string) => {
      if (visiting.has(name)) {
        throw new Error(`Circular dependency detected: ${name}`);
      }
      if (visited.has(name)) {
        return;
      }

      visiting.add(name);
      const status = this.services.get(name);
      if (status) {
        for (const dep of status.metadata.dependencies) {
          visit(dep);
        }
      }
      visiting.delete(name);
      visited.add(name);
      result.push(name);
    };

    for (const serviceName of this.services.keys()) {
      visit(serviceName);
    }

    return result;
  }

  /**
   * Setup periodic health checking
   */
  private setupHealthChecking(): void {
    this.healthCheckInterval = setInterval(async () => {
      for (const serviceName of this.services.keys()) {
        const status = this.services.get(serviceName);
        if (status?.state === 'running') {
          await this.checkServiceHealth(serviceName);
        }
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.removeAllListeners();
  }
}

// Global service registry instance
export const serviceRegistry = new ServiceRegistry();

export default serviceRegistry;
