import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import * as path from 'path';

import { app } from 'electron';
import * as i18next from 'i18next';
import * as LanguageDetector from 'i18next-browser-languagedetector';
import * as Backend from 'i18next-fs-backend';
import { initReactI18next } from 'react-i18next';

interface I18nSettings {
  defaultLanguage: string;
  fallbackLanguage: string;
  supportedLanguages: string[];
  autoDetect: boolean;
  loadPath: string;
  saveMissing: boolean;
  debug: boolean;
  interpolation: {
    escapeValue: boolean;
    prefix: string;
    suffix: string;
  };
  detection: {
    order: string[];
    lookupQuerystring: string;
    lookupCookie: string;
    lookupLocalStorage: string;
    caches: string[];
  };
}

interface Language {
  code: string;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  region: string;
  script: string;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: {
      symbol: string;
      position: 'before' | 'after';
    };
  };
}

interface Translation {
  language: string;
  namespace: string;
  resources: Record<string, any>;
}

export class I18nManager extends EventEmitter {
  private static instance: I18nManager;
  private settings: I18nSettings;
  private languages: Map<string, Language>;
  private translations: Map<string, Translation>;
  private isInitialized: boolean = false;
  private i18n: typeof i18next;

  private constructor() {
    super();
    this.settings = {
      defaultLanguage: 'en',
      fallbackLanguage: 'en',
      supportedLanguages: ['en'],
      autoDetect: true,
      loadPath: path.join(app.getPath('userData'), 'locales'),
      saveMissing: true,
      debug: false,
      interpolation: {
        escapeValue: true,
        prefix: '{{',
        suffix: '}}',
      },
      detection: {
        order: ['querystring', 'cookie', 'localStorage', 'navigator'],
        lookupQuerystring: 'lng',
        lookupCookie: 'i18next',
        lookupLocalStorage: 'i18nextLng',
        caches: ['localStorage', 'cookie'],
      },
    };
    this.languages = new Map();
    this.translations = new Map();
    this.i18n = i18next;
  }

  public static getInstance(): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager();
    }
    return I18nManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadLanguages();
      await this.setupI18n();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize I18nManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'i18n-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'i18n-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async loadLanguages(): Promise<void> {
    try {
      const languagesPath = path.join(app.getPath('userData'), 'languages.json');
      const data = await fs.readFile(languagesPath, 'utf-8');
      const languages = JSON.parse(data);

      for (const language of languages) {
        this.languages.set(language.code, language);
      }

      // Create default language if none exists
      if (!this.languages.has('en')) {
        await this.createDefaultLanguage();
      }
    } catch (error) {
      await this.saveLanguages();
    }
  }

  private async saveLanguages(): Promise<void> {
    const languagesPath = path.join(app.getPath('userData'), 'languages.json');
    await fs.writeFile(languagesPath, JSON.stringify(Array.from(this.languages.values()), null, 2));
  }

  private async createDefaultLanguage(): Promise<void> {
    const defaultLanguage: Language = {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      direction: 'ltr',
      region: 'US',
      script: 'Latn',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: 'HH:mm:ss',
      numberFormat: {
        decimal: '.',
        thousands: ',',
        currency: {
          symbol: '$',
          position: 'before',
        },
      },
    };

    this.languages.set('en', defaultLanguage);
    await this.saveLanguages();
  }

  private async setupI18n(): Promise<void> {
    await this.i18n
      .use(Backend)
      .use(LanguageDetector)
      .use(initReactI18next)
      .init({
        lng: this.settings.defaultLanguage,
        fallbackLng: this.settings.fallbackLanguage,
        supportedLngs: this.settings.supportedLanguages,
        load: 'languageOnly',
        ns: ['common', 'errors', 'validation'],
        defaultNS: 'common',
        fallbackNS: 'common',
        backend: {
          loadPath: path.join(this.settings.loadPath, '{{lng}}/{{ns}}.json'),
          addPath: path.join(this.settings.loadPath, '{{lng}}/{{ns}}.missing.json'),
        },
        detection: this.settings.detection,
        interpolation: this.settings.interpolation,
        saveMissing: this.settings.saveMissing,
        debug: this.settings.debug,
      });

    // Load all translations
    for (const language of this.settings.supportedLanguages) {
      await this.loadTranslations(language);
    }
  }

  private async loadTranslations(language: string): Promise<void> {
    try {
      const translationPath = path.join(this.settings.loadPath, language);
      const files = await fs.readdir(translationPath);

      for (const file of files) {
        if (file.endsWith('.json')) {
          const namespace = path.basename(file, '.json');
          const data = await fs.readFile(path.join(translationPath, file), 'utf-8');
          const resources = JSON.parse(data);

          this.translations.set(`${language}:${namespace}`, {
            language,
            namespace,
            resources,
          });
        }
      }
    } catch (error) {
      console.error(`Failed to load translations for ${language}:`, error);
    }
  }

  public async addLanguage(language: Language): Promise<void> {
    this.languages.set(language.code, language);
    this.settings.supportedLanguages.push(language.code);
    await this.saveLanguages();
    await this.saveSettings();
    this.emit('language-added', language);
  }

  public async removeLanguage(code: string): Promise<void> {
    if (code === this.settings.defaultLanguage || code === this.settings.fallbackLanguage) {
      throw new Error(`Cannot remove ${code} language as it is default or fallback`);
    }

    const language = this.languages.get(code);
    if (!language) {
      throw new Error(`Language not found: ${code}`);
    }

    this.languages.delete(code);
    this.settings.supportedLanguages = this.settings.supportedLanguages.filter(l => l !== code);
    await this.saveLanguages();
    await this.saveSettings();
    this.emit('language-removed', language);
  }

  public async setLanguage(code: string): Promise<void> {
    const language = this.languages.get(code);
    if (!language) {
      throw new Error(`Language not found: ${code}`);
    }

    await this.i18n.changeLanguage(code);
    document.documentElement.lang = code;
    document.documentElement.dir = language.direction;
    this.emit('language-changed', language);
  }

  public async addTranslation(
    language: string,
    namespace: string,
    resources: Record<string, any>
  ): Promise<void> {
    const translation: Translation = {
      language,
      namespace,
      resources,
    };

    this.translations.set(`${language}:${namespace}`, translation);
    await this.saveTranslation(language, namespace, resources);
    this.emit('translation-added', translation);
  }

  private async saveTranslation(
    language: string,
    namespace: string,
    resources: Record<string, any>
  ): Promise<void> {
    const translationPath = path.join(this.settings.loadPath, language);
    await fs.mkdir(translationPath, { recursive: true });
    await fs.writeFile(
      path.join(translationPath, `${namespace}.json`),
      JSON.stringify(resources, null, 2)
    );
  }

  public formatDate(date: Date, language: string): string {
    const lang = this.languages.get(language);
    if (!lang) {
      throw new Error(`Language not found: ${language}`);
    }

    // TODO: Implement date formatting based on language settings
    return date.toLocaleDateString(language);
  }

  public formatTime(date: Date, language: string): string {
    const lang = this.languages.get(language);
    if (!lang) {
      throw new Error(`Language not found: ${language}`);
    }

    // TODO: Implement time formatting based on language settings
    return date.toLocaleTimeString(language);
  }

  public formatNumber(
    number: number,
    language: string,
    options: Intl.NumberFormatOptions = {}
  ): string {
    const lang = this.languages.get(language);
    if (!lang) {
      throw new Error(`Language not found: ${language}`);
    }

    return new Intl.NumberFormat(language, options).format(number);
  }

  public formatCurrency(amount: number, language: string, currency: string): string {
    const lang = this.languages.get(language);
    if (!lang) {
      throw new Error(`Language not found: ${language}`);
    }

    const formatter = new Intl.NumberFormat(language, {
      style: 'currency',
      currency,
    });

    return formatter.format(amount);
  }

  public getLanguage(code: string): Language | undefined {
    return this.languages.get(code);
  }

  public getAllLanguages(): Language[] {
    return Array.from(this.languages.values());
  }

  public getSettings(): I18nSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<I18nSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }

  public cleanup(): void {
    // Cleanup any active translations or resources
    this.translations.clear();
  }
}
