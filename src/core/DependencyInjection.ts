/**
 * Dependency Injection Container
 * World-class IoC container for managing dependencies and services
 */

export interface ServiceDefinition<T = any> {
  factory: (...args: any[]) => T | Promise<T>;
  singleton?: boolean;
  dependencies?: string[];
  lifecycle?: 'transient' | 'singleton' | 'scoped';
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface ServiceInstance<T = any> {
  instance: T;
  created: Date;
  dependencies: string[];
  metadata: Record<string, any>;
}

export class DependencyContainer {
  private services = new Map<string, ServiceDefinition>();
  private instances = new Map<string, ServiceInstance>();
  private scoped = new Map<string, Map<string, ServiceInstance>>();
  private resolving = new Set<string>();

  /**
   * Register a service with the container
   */
  register<T>(
    name: string,
    factory: (...args: any[]) => T | Promise<T>,
    options: Partial<ServiceDefinition> = {}
  ): this {
    this.services.set(name, {
      factory,
      singleton: options.singleton ?? true,
      dependencies: options.dependencies ?? [],
      lifecycle: options.lifecycle ?? 'singleton',
      tags: options.tags ?? [],
      metadata: options.metadata ?? {},
    });
    return this;
  }

  /**
   * Register a singleton service
   */
  singleton<T>(
    name: string,
    factory: (...args: any[]) => T | Promise<T>,
    dependencies: string[] = []
  ): this {
    return this.register(name, factory, {
      lifecycle: 'singleton',
      dependencies,
    });
  }

  /**
   * Register a transient service
   */
  transient<T>(
    name: string,
    factory: (...args: any[]) => T | Promise<T>,
    dependencies: string[] = []
  ): this {
    return this.register(name, factory, {
      lifecycle: 'transient',
      dependencies,
    });
  }

  /**
   * Register a scoped service
   */
  scoped<T>(
    name: string,
    factory: (...args: any[]) => T | Promise<T>,
    dependencies: string[] = []
  ): this {
    return this.register(name, factory, {
      lifecycle: 'scoped',
      dependencies,
    });
  }

  /**
   * Resolve a service by name
   */
  async resolve<T>(name: string, scope?: string): Promise<T> {
    if (this.resolving.has(name)) {
      throw new Error(`Circular dependency detected: ${name}`);
    }

    const definition = this.services.get(name);
    if (!definition) {
      throw new Error(`Service not found: ${name}`);
    }

    // Handle different lifecycles
    switch (definition.lifecycle) {
      case 'singleton':
        return this.resolveSingleton<T>(name, definition);
      case 'scoped':
        return this.resolveScoped<T>(name, definition, scope || 'default');
      case 'transient':
      default:
        return this.resolveTransient<T>(name, definition);
    }
  }

  private async resolveSingleton<T>(name: string, definition: ServiceDefinition): Promise<T> {
    if (this.instances.has(name)) {
      return this.instances.get(name)!.instance as T;
    }

    return this.createInstance<T>(name, definition);
  }

  private async resolveScoped<T>(
    name: string,
    definition: ServiceDefinition,
    scope: string
  ): Promise<T> {
    if (!this.scoped.has(scope)) {
      this.scoped.set(scope, new Map());
    }

    const scopedInstances = this.scoped.get(scope)!;
    if (scopedInstances.has(name)) {
      return scopedInstances.get(name)!.instance as T;
    }

    const instance = await this.createInstance<T>(name, definition);
    scopedInstances.set(name, {
      instance,
      created: new Date(),
      dependencies: definition.dependencies || [],
      metadata: definition.metadata || {},
    });

    return instance;
  }

  private async resolveTransient<T>(name: string, definition: ServiceDefinition): Promise<T> {
    return this.createInstance<T>(name, definition);
  }

  private async createInstance<T>(name: string, definition: ServiceDefinition): Promise<T> {
    this.resolving.add(name);

    try {
      // Resolve dependencies
      const dependencies = await Promise.all(
        (definition.dependencies || []).map(dep => this.resolve(dep))
      );

      // Create instance
      const instance = await definition.factory(...dependencies);

      // Store singleton instances
      if (definition.lifecycle === 'singleton') {
        this.instances.set(name, {
          instance,
          created: new Date(),
          dependencies: definition.dependencies || [],
          metadata: definition.metadata || {},
        });
      }

      return instance as T;
    } finally {
      this.resolving.delete(name);
    }
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get services by tag
   */
  getServicesByTag(tag: string): string[] {
    return Array.from(this.services.entries())
      .filter(([, definition]) => definition.tags?.includes(tag))
      .map(([name]) => name);
  }

  /**
   * Clear a scope
   */
  clearScope(scope: string): void {
    this.scoped.delete(scope);
  }

  /**
   * Clear all instances (useful for testing)
   */
  clear(): void {
    this.instances.clear();
    this.scoped.clear();
    this.resolving.clear();
  }

  /**
   * Get container statistics
   */
  getStats() {
    return {
      registeredServices: this.services.size,
      singletonInstances: this.instances.size,
      scopes: this.scoped.size,
      resolving: this.resolving.size,
    };
  }
}

// Global container instance
export const container = new DependencyContainer();

// Decorator for automatic dependency injection
export function Injectable(name: string, dependencies: string[] = []) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    container.singleton(name, (...deps) => new constructor(...deps), dependencies);
    return constructor;
  };
}

// Decorator for injecting dependencies
export function Inject(serviceName: string) {
  return function (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
    // Store metadata for dependency injection
    const existingTokens = Reflect.getMetadata('design:paramtypes', target) || [];
    existingTokens[parameterIndex] = serviceName;
    Reflect.defineMetadata('design:paramtypes', existingTokens, target);
  };
}

export default container;
