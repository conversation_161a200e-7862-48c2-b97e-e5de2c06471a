import { createHash } from 'crypto';
import { EventEmitter } from 'events';
import { createReadStream, createWriteStream, promises as fs } from 'fs';
import * as path from 'path';
import { pipeline } from 'stream/promises';
import { createGunzip, createGzip } from 'zlib';

import archiver from 'archiver';
import { app } from 'electron';

interface DocumentationSettings {
  enabled: boolean;
  format: 'markdown' | 'html' | 'pdf';
  versioning: boolean;
  search: {
    enabled: boolean;
    engine: 'elastic' | 'lucene' | 'simple';
  };
  generation: {
    enabled: boolean;
    schedule: string;
    output: string;
  };
  publishing: {
    enabled: boolean;
    platform: 'github' | 'gitlab' | 'bitbucket';
    branch: string;
  };
  backup: {
    enabled: boolean;
    schedule: string;
    retention: number;
  };
}

interface DocumentationPage {
  id: string;
  title: string;
  content: string;
  format: 'markdown' | 'html' | 'pdf';
  version: string;
  status: 'draft' | 'review' | 'published';
  metadata: {
    created: number;
    modified: number;
    published: number;
    author: string;
    contributors: string[];
    tags: string[];
    categories: string[];
  };
}

interface DocumentationVersion {
  id: string;
  version: string;
  status: 'draft' | 'review' | 'published';
  pages: string[];
  metadata: {
    created: number;
    modified: number;
    published: number;
    author: string;
    contributors: string[];
    changes: string[];
  };
}

interface DocumentationSearch {
  id: string;
  query: string;
  results: {
    pageId: string;
    score: number;
    highlights: string[];
  }[];
  metadata: {
    timestamp: number;
    duration: number;
    total: number;
  };
}

export class DocumentationManager extends EventEmitter {
  private static instance: DocumentationManager;
  private settings: DocumentationSettings;
  private pages: Map<string, DocumentationPage>;
  private versions: Map<string, DocumentationVersion>;
  private searches: Map<string, DocumentationSearch>;
  private isInitialized: boolean = false;

  private constructor() {
    super();
    this.settings = {
      enabled: true,
      format: 'markdown',
      versioning: true,
      search: {
        enabled: true,
        engine: 'elastic',
      },
      generation: {
        enabled: true,
        schedule: '0 0 * * *', // Daily at midnight
        output: 'docs',
      },
      publishing: {
        enabled: true,
        platform: 'github',
        branch: 'main',
      },
      backup: {
        enabled: true,
        schedule: '0 0 * * 0', // Weekly on Sunday
        retention: 30, // 30 days
      },
    };
    this.pages = new Map();
    this.versions = new Map();
    this.searches = new Map();
  }

  public static getInstance(): DocumentationManager {
    if (!DocumentationManager.instance) {
      DocumentationManager.instance = new DocumentationManager();
    }
    return DocumentationManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadPages();
      await this.loadVersions();
      await this.loadSearches();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize DocumentationManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'documentation-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'documentation-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async loadPages(): Promise<void> {
    try {
      const pagesPath = path.join(app.getPath('userData'), 'documentation-pages.json');
      const data = await fs.readFile(pagesPath, 'utf-8');
      const pages = JSON.parse(data);

      for (const page of pages) {
        this.pages.set(page.id, page);
      }
    } catch (error) {
      await this.savePages();
    }
  }

  private async savePages(): Promise<void> {
    const pagesPath = path.join(app.getPath('userData'), 'documentation-pages.json');
    await fs.writeFile(pagesPath, JSON.stringify(Array.from(this.pages.values()), null, 2));
  }

  private async loadVersions(): Promise<void> {
    try {
      const versionsPath = path.join(app.getPath('userData'), 'documentation-versions.json');
      const data = await fs.readFile(versionsPath, 'utf-8');
      const versions = JSON.parse(data);

      for (const version of versions) {
        this.versions.set(version.id, version);
      }
    } catch (error) {
      await this.saveVersions();
    }
  }

  private async saveVersions(): Promise<void> {
    const versionsPath = path.join(app.getPath('userData'), 'documentation-versions.json');
    await fs.writeFile(versionsPath, JSON.stringify(Array.from(this.versions.values()), null, 2));
  }

  private async loadSearches(): Promise<void> {
    try {
      const searchesPath = path.join(app.getPath('userData'), 'documentation-searches.json');
      const data = await fs.readFile(searchesPath, 'utf-8');
      const searches = JSON.parse(data);

      for (const search of searches) {
        this.searches.set(search.id, search);
      }
    } catch (error) {
      await this.saveSearches();
    }
  }

  private async saveSearches(): Promise<void> {
    const searchesPath = path.join(app.getPath('userData'), 'documentation-searches.json');
    await fs.writeFile(searchesPath, JSON.stringify(Array.from(this.searches.values()), null, 2));
  }

  public async createPage(
    page: Omit<DocumentationPage, 'id' | 'status' | 'metadata'>
  ): Promise<DocumentationPage> {
    const newPage: DocumentationPage = {
      ...page,
      id: Math.random().toString(36).substr(2, 9),
      status: 'draft',
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        published: 0,
        author: '',
        contributors: [],
        tags: [],
        categories: [],
      },
    };

    this.pages.set(newPage.id, newPage);
    await this.savePages();
    this.emit('page-created', newPage);

    return newPage;
  }

  public async updatePage(
    id: string,
    updates: Partial<DocumentationPage>
  ): Promise<DocumentationPage> {
    const page = this.pages.get(id);
    if (!page) {
      throw new Error(`Page not found: ${id}`);
    }

    const updatedPage = {
      ...page,
      ...updates,
      metadata: {
        ...page.metadata,
        modified: Date.now(),
      },
    };

    this.pages.set(id, updatedPage);
    await this.savePages();
    this.emit('page-updated', updatedPage);

    return updatedPage;
  }

  public async deletePage(id: string): Promise<void> {
    const page = this.pages.get(id);
    if (!page) {
      throw new Error(`Page not found: ${id}`);
    }

    this.pages.delete(id);
    await this.savePages();
    this.emit('page-deleted', page);
  }

  public async createVersion(
    version: Omit<DocumentationVersion, 'id' | 'status' | 'metadata'>
  ): Promise<DocumentationVersion> {
    const newVersion: DocumentationVersion = {
      ...version,
      id: Math.random().toString(36).substr(2, 9),
      status: 'draft',
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        published: 0,
        author: '',
        contributors: [],
        changes: [],
      },
    };

    this.versions.set(newVersion.id, newVersion);
    await this.saveVersions();
    this.emit('version-created', newVersion);

    return newVersion;
  }

  public async updateVersion(
    id: string,
    updates: Partial<DocumentationVersion>
  ): Promise<DocumentationVersion> {
    const version = this.versions.get(id);
    if (!version) {
      throw new Error(`Version not found: ${id}`);
    }

    const updatedVersion = {
      ...version,
      ...updates,
      metadata: {
        ...version.metadata,
        modified: Date.now(),
      },
    };

    this.versions.set(id, updatedVersion);
    await this.saveVersions();
    this.emit('version-updated', updatedVersion);

    return updatedVersion;
  }

  public async deleteVersion(id: string): Promise<void> {
    const version = this.versions.get(id);
    if (!version) {
      throw new Error(`Version not found: ${id}`);
    }

    this.versions.delete(id);
    await this.saveVersions();
    this.emit('version-deleted', version);
  }

  public async search(query: string): Promise<DocumentationSearch> {
    if (!this.settings.search.enabled) {
      throw new Error('Search is not enabled');
    }

    const startTime = Date.now();
    const results = await this.performSearch(query);

    const search: DocumentationSearch = {
      id: Math.random().toString(36).substr(2, 9),
      query,
      results,
      metadata: {
        timestamp: Date.now(),
        duration: Date.now() - startTime,
        total: results.length,
      },
    };

    this.searches.set(search.id, search);
    await this.saveSearches();
    this.emit('search-completed', search);

    return search;
  }

  private async performSearch(query: string): Promise<DocumentationSearch['results']> {
    // Simple search implementation
    const results: DocumentationSearch['results'] = [];

    for (const page of this.pages.values()) {
      if (page.status === 'published') {
        const score = this.calculateSearchScore(page, query);
        if (score > 0) {
          results.push({
            pageId: page.id,
            score,
            highlights: this.generateHighlights(page.content, query),
          });
        }
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  private calculateSearchScore(page: DocumentationPage, query: string): number {
    const titleScore = page.title.toLowerCase().includes(query.toLowerCase()) ? 2 : 0;
    const contentScore = page.content.toLowerCase().includes(query.toLowerCase()) ? 1 : 0;
    return titleScore + contentScore;
  }

  private generateHighlights(content: string, query: string): string[] {
    const highlights: string[] = [];
    const lowerContent = content.toLowerCase();
    const lowerQuery = query.toLowerCase();
    let index = lowerContent.indexOf(lowerQuery);

    while (index !== -1) {
      const start = Math.max(0, index - 50);
      const end = Math.min(content.length, index + query.length + 50);
      highlights.push(content.slice(start, end));
      index = lowerContent.indexOf(lowerQuery, index + 1);
    }

    return highlights;
  }

  public async generateDocumentation(): Promise<void> {
    if (!this.settings.generation.enabled) return;

    const outputPath = path.join(app.getPath('userData'), this.settings.generation.output);
    await fs.mkdir(outputPath, { recursive: true });

    for (const page of this.pages.values()) {
      if (page.status === 'published') {
        const content = await this.convertFormat(page.content, page.format, this.settings.format);
        const filePath = path.join(outputPath, `${page.id}.${this.settings.format}`);
        await fs.writeFile(filePath, content);
      }
    }

    this.emit('documentation-generated');
  }

  private async convertFormat(content: string, from: string, to: string): Promise<string> {
    // Simple format conversion
    if (from === to) return content;

    // Add format conversion logic here
    return content;
  }

  public async backupDocumentation(): Promise<void> {
    if (!this.settings.backup.enabled) return;

    const backupPath = path.join(app.getPath('userData'), 'backups', 'documentation');
    await fs.mkdir(backupPath, { recursive: true });

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupPath, `backup-${timestamp}.zip`);

    const output = createWriteStream(backupFile);
    const archive = archiver('zip', {
      zlib: { level: 9 },
    });

    output.on('close', () => {
      console.log(`Backup created: ${archive.pointer()} bytes`);
    });

    archive.on('error', err => {
      throw err;
    });

    archive.pipe(output);

    // Add documentation files to archive
    const docsPath = path.join(app.getPath('userData'), this.settings.generation.output);
    archive.directory(docsPath, false);

    await archive.finalize();

    // Cleanup old backups
    await this.cleanupOldBackups();
  }

  private async cleanupOldBackups(): Promise<void> {
    const backupPath = path.join(app.getPath('userData'), 'backups', 'documentation');
    const files = await fs.readdir(backupPath);
    const backups = files
      .filter(file => file.startsWith('backup-'))
      .sort()
      .reverse();

    // Keep only the most recent backups
    const toDelete = backups.slice(this.settings.backup.retention);
    for (const file of toDelete) {
      await fs.unlink(path.join(backupPath, file));
    }
  }

  public getPage(id: string): DocumentationPage | undefined {
    return this.pages.get(id);
  }

  public getAllPages(): DocumentationPage[] {
    return Array.from(this.pages.values());
  }

  public getVersion(id: string): DocumentationVersion | undefined {
    return this.versions.get(id);
  }

  public getAllVersions(): DocumentationVersion[] {
    return Array.from(this.versions.values());
  }

  public getSearch(id: string): DocumentationSearch | undefined {
    return this.searches.get(id);
  }

  public getAllSearches(): DocumentationSearch[] {
    return Array.from(this.searches.values());
  }

  public getSettings(): DocumentationSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<DocumentationSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }
}
