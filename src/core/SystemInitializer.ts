/**
 * System Initialization Manager
 * Orchestrates the complete system startup process with proper error handling and rollback
 */

import { EventEmitter } from 'events';
import { logger } from './EnhancedLogger';
import { serviceRegistry } from './ServiceRegistry';
import { microservicesBus } from './MicroservicesBus';
import { container } from './DependencyInjection';

export interface InitializationPhase {
  name: string;
  description: string;
  priority: number;
  timeout: number;
  retries: number;
  rollback?: () => Promise<void>;
  execute: () => Promise<void>;
  healthCheck?: () => Promise<boolean>;
  dependencies?: string[];
}

export interface InitializationConfig {
  phases: InitializationPhase[];
  globalTimeout: number;
  enableRollback: boolean;
  enableHealthChecks: boolean;
  enableParallelExecution: boolean;
  maxConcurrency: number;
}

export interface InitializationResult {
  success: boolean;
  completedPhases: string[];
  failedPhase?: string;
  error?: Error;
  duration: number;
  rollbackPerformed: boolean;
}

export class SystemInitializer extends EventEmitter {
  private config: InitializationConfig;
  private completedPhases: string[] = [];
  private rollbackStack: (() => Promise<void>)[] = [];
  private startTime: number = 0;

  constructor(config: InitializationConfig) {
    super();
    this.config = config;
  }

  /**
   * Initialize the entire system
   */
  async initialize(): Promise<InitializationResult> {
    this.startTime = Date.now();
    logger.info('Starting system initialization', {
      phases: this.config.phases.length,
      enableRollback: this.config.enableRollback,
      enableParallelExecution: this.config.enableParallelExecution,
    });

    try {
      // Sort phases by priority
      const sortedPhases = [...this.config.phases].sort((a, b) => a.priority - b.priority);

      if (this.config.enableParallelExecution) {
        await this.executePhasesInParallel(sortedPhases);
      } else {
        await this.executePhasesSequentially(sortedPhases);
      }

      // Perform health checks if enabled
      if (this.config.enableHealthChecks) {
        await this.performHealthChecks();
      }

      const duration = Date.now() - this.startTime;
      logger.info('System initialization completed successfully', {
        duration,
        completedPhases: this.completedPhases.length,
      });

      this.emit('initialization-complete', {
        success: true,
        completedPhases: this.completedPhases,
        duration,
      });

      return {
        success: true,
        completedPhases: this.completedPhases,
        duration,
        rollbackPerformed: false,
      };
    } catch (error) {
      logger.error('System initialization failed', error);
      
      let rollbackPerformed = false;
      if (this.config.enableRollback) {
        rollbackPerformed = await this.performRollback();
      }

      const duration = Date.now() - this.startTime;
      const result: InitializationResult = {
        success: false,
        completedPhases: this.completedPhases,
        error: error as Error,
        duration,
        rollbackPerformed,
      };

      this.emit('initialization-failed', result);
      return result;
    }
  }

  /**
   * Execute phases sequentially
   */
  private async executePhasesSequentially(phases: InitializationPhase[]): Promise<void> {
    for (const phase of phases) {
      await this.executePhase(phase);
    }
  }

  /**
   * Execute phases in parallel with concurrency control
   */
  private async executePhasesInParallel(phases: InitializationPhase[]): Promise<void> {
    const phaseGroups = this.groupPhasesByDependencies(phases);
    
    for (const group of phaseGroups) {
      const chunks = this.chunkArray(group, this.config.maxConcurrency);
      
      for (const chunk of chunks) {
        await Promise.all(chunk.map(phase => this.executePhase(phase)));
      }
    }
  }

  /**
   * Execute a single initialization phase
   */
  private async executePhase(phase: InitializationPhase): Promise<void> {
    logger.info(`Starting initialization phase: ${phase.name}`, {
      description: phase.description,
      timeout: phase.timeout,
      retries: phase.retries,
    });

    this.emit('phase-starting', { phase: phase.name });

    let lastError: Error | null = null;
    let attempt = 0;

    while (attempt <= phase.retries) {
      try {
        // Execute with timeout
        await this.executeWithTimeout(phase.execute, phase.timeout);
        
        // Add rollback function to stack
        if (phase.rollback && this.config.enableRollback) {
          this.rollbackStack.push(phase.rollback);
        }

        this.completedPhases.push(phase.name);
        logger.info(`Completed initialization phase: ${phase.name}`, {
          attempt: attempt + 1,
          duration: Date.now() - this.startTime,
        });

        this.emit('phase-completed', { phase: phase.name, attempt: attempt + 1 });
        return;
      } catch (error) {
        lastError = error as Error;
        attempt++;

        if (attempt <= phase.retries) {
          logger.warn(`Phase ${phase.name} failed, retrying (${attempt}/${phase.retries})`, {
            error: lastError.message,
          });
          
          this.emit('phase-retry', { 
            phase: phase.name, 
            attempt, 
            error: lastError.message 
          });

          // Wait before retry with exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    // All retries exhausted
    logger.error(`Phase ${phase.name} failed after ${phase.retries + 1} attempts`, lastError);
    this.emit('phase-failed', { phase: phase.name, error: lastError });
    throw lastError;
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);

      fn()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Perform health checks on completed phases
   */
  private async performHealthChecks(): Promise<void> {
    logger.info('Performing post-initialization health checks');

    const healthCheckPromises = this.config.phases
      .filter(phase => 
        this.completedPhases.includes(phase.name) && phase.healthCheck
      )
      .map(async phase => {
        try {
          const healthy = await phase.healthCheck!();
          if (!healthy) {
            throw new Error(`Health check failed for phase: ${phase.name}`);
          }
          logger.debug(`Health check passed: ${phase.name}`);
        } catch (error) {
          logger.error(`Health check failed: ${phase.name}`, error);
          throw error;
        }
      });

    await Promise.all(healthCheckPromises);
    logger.info('All health checks passed');
  }

  /**
   * Perform rollback of completed phases
   */
  private async performRollback(): Promise<boolean> {
    if (this.rollbackStack.length === 0) {
      return false;
    }

    logger.warn('Performing system rollback', {
      phasesToRollback: this.rollbackStack.length,
    });

    this.emit('rollback-starting', { phases: this.rollbackStack.length });

    let rollbackSuccess = true;

    // Execute rollbacks in reverse order
    while (this.rollbackStack.length > 0) {
      const rollbackFn = this.rollbackStack.pop()!;
      
      try {
        await rollbackFn();
      } catch (error) {
        logger.error('Rollback operation failed', error);
        rollbackSuccess = false;
      }
    }

    if (rollbackSuccess) {
      logger.info('System rollback completed successfully');
      this.emit('rollback-complete', { success: true });
    } else {
      logger.error('System rollback completed with errors');
      this.emit('rollback-complete', { success: false });
    }

    return rollbackSuccess;
  }

  /**
   * Group phases by their dependencies
   */
  private groupPhasesByDependencies(phases: InitializationPhase[]): InitializationPhase[][] {
    const groups: InitializationPhase[][] = [];
    const processed = new Set<string>();
    
    while (processed.size < phases.length) {
      const currentGroup: InitializationPhase[] = [];
      
      for (const phase of phases) {
        if (processed.has(phase.name)) continue;
        
        const dependenciesMet = !phase.dependencies || 
          phase.dependencies.every(dep => processed.has(dep));
        
        if (dependenciesMet) {
          currentGroup.push(phase);
          processed.add(phase.name);
        }
      }
      
      if (currentGroup.length === 0) {
        throw new Error('Circular dependency detected in initialization phases');
      }
      
      groups.push(currentGroup);
    }
    
    return groups;
  }

  /**
   * Split array into chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get initialization statistics
   */
  getStats() {
    return {
      totalPhases: this.config.phases.length,
      completedPhases: this.completedPhases.length,
      rollbackStackSize: this.rollbackStack.length,
      duration: this.startTime ? Date.now() - this.startTime : 0,
    };
  }
}

export default SystemInitializer;
