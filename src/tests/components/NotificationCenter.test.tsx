import { fireEvent, screen, waitFor } from '@testing-library/react';
import React from 'react';

import { NotificationCenter } from '../../components/Notifications/NotificationCenter';
import { addNotification, clearNotifications } from '../../store/slices/notificationSlice';
import { render } from '../utils/test-utils';

describe('NotificationCenter', () => {
  beforeEach(() => {
    render(<NotificationCenter />);
  });

  it('renders empty state when no notifications', () => {
    expect(screen.getByText(/no notifications/i)).toBeInTheDocument();
  });

  it('displays notifications when added to store', () => {
    const { store } = render(<NotificationCenter />);
    store.dispatch(
      addNotification({
        title: 'Test notification',
        body: 'Test notification body',
        type: 'info',
        actions: [],
      })
    );

    expect(screen.getByText('Test notification')).toBeInTheDocument();
  });

  it('clears all notifications when clear button is clicked', async () => {
    const { store } = render(<NotificationCenter />);

    // Add multiple notifications
    store.dispatch(
      addNotification({
        title: 'Test notification 1',
        body: 'Test notification 1 body',
        type: 'info',
        actions: [],
      })
    );
    store.dispatch(
      addNotification({
        title: 'Test notification 2',
        body: 'Test notification 2 body',
        type: 'error',
        actions: [],
      })
    );

    // Click clear button
    fireEvent.click(screen.getByRole('button', { name: /clear all/i }));

    // Verify notifications are cleared
    await waitFor(() => {
      expect(screen.getByText(/no notifications/i)).toBeInTheDocument();
    });
  });

  it('displays notifications in correct order (newest first)', () => {
    const { store } = render(<NotificationCenter />);

    const now = Date.now();
    store.dispatch(
      addNotification({
        title: 'Older notification',
        body: 'Older notification body',
        type: 'info',
        actions: [],
      })
    );
    store.dispatch(
      addNotification({
        title: 'Newer notification',
        body: 'Newer notification body',
        type: 'info',
        actions: [],
      })
    );

    const notifications = screen.getAllByRole('listitem');
    expect(notifications[0]).toHaveTextContent('Newer notification');
    expect(notifications[1]).toHaveTextContent('Older notification');
  });

  it('applies correct styles based on notification type', () => {
    const { store } = render(<NotificationCenter />);

    store.dispatch(
      addNotification({
        title: 'Error notification',
        body: 'Error notification body',
        type: 'error',
        actions: [],
      })
    );

    const notification = screen.getByText('Error notification').closest('div');
    expect(notification).toHaveClass('error');
  });

  it('handles click events on notifications', () => {
    const { store } = render(<NotificationCenter />);
    const onClick = jest.fn();

    store.dispatch(
      addNotification({
        title: 'Clickable notification',
        body: 'Clickable notification body',
        type: 'info',
        actions: [
          {
            type: 'button',
            label: 'Click me',
            callback: onClick,
          },
        ],
      })
    );

    fireEvent.click(screen.getByText('Click me'));
    expect(onClick).toHaveBeenCalled();
  });

  it('auto-dismisses notifications with autoDismiss prop', async () => {
    const { store } = render(<NotificationCenter />);

    store.dispatch(
      addNotification({
        title: 'Auto-dismissing notification',
        body: 'Auto-dismissing notification body',
        type: 'info',
        actions: [],
      })
    );

    expect(screen.getByText('Auto-dismissing notification')).toBeInTheDocument();

    await waitFor(
      () => {
        expect(screen.queryByText('Auto-dismissing notification')).not.toBeInTheDocument();
      },
      { timeout: 6000 }
    );
  });

  it('shows notification count badge', () => {
    const { store } = render(<NotificationCenter />);

    store.dispatch(
      addNotification({
        title: 'Test notification 1',
        body: 'Test notification 1 body',
        type: 'info',
        actions: [],
      })
    );
    store.dispatch(
      addNotification({
        title: 'Test notification 2',
        body: 'Test notification 2 body',
        type: 'error',
        actions: [],
      })
    );

    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('groups notifications by type', () => {
    const { store } = render(<NotificationCenter />);

    store.dispatch(
      addNotification({
        title: 'Info notification',
        body: 'Info notification body',
        type: 'info',
        actions: [],
      })
    );
    store.dispatch(
      addNotification({
        title: 'Error notification',
        body: 'Error notification body',
        type: 'error',
        actions: [],
      })
    );

    const infoGroup = screen.getByText('Info');
    const errorGroup = screen.getByText('Error');

    expect(infoGroup).toBeInTheDocument();
    expect(errorGroup).toBeInTheDocument();
  });
});
