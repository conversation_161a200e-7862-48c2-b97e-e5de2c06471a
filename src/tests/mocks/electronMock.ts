/**
 * Mock for Electron APIs in Jest tests
 */

export const electronMock = {
  ipcRenderer: {
    invoke: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    removeListener: jest.fn(),
    removeAllListeners: jest.fn(),
    send: jest.fn(),
    sendSync: jest.fn(),
  },
  shell: {
    openExternal: jest.fn(),
    openPath: jest.fn(),
    showItemInFolder: jest.fn(),
  },
  app: {
    getVersion: jest.fn(() => '1.0.0'),
    getName: jest.fn(() => 'A14 Browser'),
    getPath: jest.fn(() => '/mock/path'),
    quit: jest.fn(),
  },
  dialog: {
    showOpenDialog: jest.fn(),
    showSaveDialog: jest.fn(),
    showMessageBox: jest.fn(),
    showErrorBox: jest.fn(),
  },
  clipboard: {
    writeText: jest.fn(),
    readText: jest.fn(() => ''),
    writeImage: jest.fn(),
    readImage: jest.fn(),
  },
  nativeTheme: {
    shouldUseDarkColors: false,
    themeSource: 'system',
    on: jest.fn(),
    removeListener: jest.fn(),
  },
  screen: {
    getPrimaryDisplay: jest.fn(() => ({
      bounds: { x: 0, y: 0, width: 1920, height: 1080 },
      workArea: { x: 0, y: 0, width: 1920, height: 1040 },
      scaleFactor: 1,
    })),
    getAllDisplays: jest.fn(() => []),
    on: jest.fn(),
    removeListener: jest.fn(),
  },
  powerMonitor: {
    getSystemIdleState: jest.fn(() => 'active'),
    getSystemIdleTime: jest.fn(() => 0),
    on: jest.fn(),
    removeListener: jest.fn(),
  },
  webContents: {
    getAllWebContents: jest.fn(() => []),
    getFocusedWebContents: jest.fn(),
  },
};

// Mock window.electronAPI for renderer process
(global as any).electronAPI = electronMock;

export default electronMock;
