/**
 * Unified Styles
 * Consolidates all duplicate CSS/SCSS files and variables from:
 * - src/renderer/styles.css
 * - src/shared/components/Button.module.css
 * - src/renderer/themes/_variables.css
 * - src/renderer/themes/light.css
 * - src/renderer/themes/modern-dark.css
 */

/* ============================================================================
   RESET AND BASE STYLES
   ============================================================================ */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden; /* Hide scrollbars */
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ============================================================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ============================================================================ */

:root {
  /* Base Colors */
  --color-primary: #3182ce;
  --color-secondary: #718096;
  --color-success: #38a169;
  --color-warning: #d69e2e;
  --color-error: #e53e3e;
  --color-info: #3182ce;

  /* Background Colors */
  --bg-color: #ffffff;
  --bg-secondary: #f7fafc;
  --bg-tertiary: #edf2f7;

  /* Text Colors */
  --text-color: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;

  /* Border Colors */
  --border-color: #e2e8f0;
  --border-secondary: #cbd5e0;

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Browser specific variables */
  --color-tabs-container-bg: #f0f0f0;
  --color-tabs-container-border: #d1d1d1;
  --color-tab-bg: #e0e0e0;
  --color-tab-text: #333;
  --color-tab-hover-bg: #d5d5d5;
  --color-tab-active-bg: #ffffff;
  --color-tab-active-text: #212529;
  --color-tab-active-border: #d1d1d1;
  --color-tab-close-icon: #555;
  --color-tab-close-hover-bg: rgba(0,0,0,0.08);
  --color-tab-close-hover-icon: #000;
  --color-tab-active-close-icon: #333;
  --color-tab-active-close-hover-bg: rgba(0,0,0,0.1);
  --color-new-tab-icon: #555;
  --color-new-tab-hover-bg: #dcdcdc;
  --color-new-tab-hover-icon: #000;
  --color-toolbar-bg: #f8f9fa;
}

/* ============================================================================
   THEME VARIATIONS
   ============================================================================ */

/* Light Theme */
.light-theme,
body.theme-light {
  --bg-color: #ffffff;
  --bg-secondary: #f7fafc;
  --bg-tertiary: #edf2f7;
  --text-color: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-color: #e2e8f0;
  --border-secondary: #cbd5e0;

  --color-background-light: #ffffff;
  --color-text-light: #212529;
}

/* Dark Theme */
.dark-theme,
body.theme-dark {
  --bg-color: #1a202c;
  --bg-secondary: #2d3748;
  --bg-tertiary: #4a5568;
  --text-color: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --border-color: #4a5568;
  --border-secondary: #2d3748;

  --color-background-dark: #1a202c;
  --color-text-dark: #f7fafc;
  --color-tabs-container-bg: #2d3748;
  --color-tabs-container-border: #4a5568;
  --color-tab-bg: #4a5568;
  --color-tab-text: #e2e8f0;
  --color-tab-hover-bg: #718096;
  --color-tab-active-bg: #1a202c;
  --color-tab-active-text: #f7fafc;
  --color-toolbar-bg: #2d3748;
}

/* Modern Dark Theme */
.modern-dark-theme,
body.theme-modern-dark {
  --bg-color: #0f0f23;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --text-color: #eee6ff;
  --text-secondary: #c9c9c9;
  --text-muted: #a0a0a0;
  --border-color: #16213e;
  --border-secondary: #1a1a2e;

  --color-background-modern-dark: #0f0f23;
  --color-text-modern-dark: #eee6ff;
  --color-tabs-container-bg: #1a1a2e;
  --color-tabs-container-border: #16213e;
  --color-tab-bg: #16213e;
  --color-tab-text: #c9c9c9;
  --color-tab-hover-bg: #1e1e3f;
  --color-tab-active-bg: #0f0f23;
  --color-tab-active-text: #eee6ff;
  --color-toolbar-bg: #1a1a2e;
}

/* System Theme (follows OS preference) */
@media (prefers-color-scheme: dark) {
  body.theme-system {
    --bg-color: #1a202c;
    --bg-secondary: #2d3748;
    --bg-tertiary: #4a5568;
    --text-color: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --border-color: #4a5568;
    --border-secondary: #2d3748;
  }
}

@media (prefers-color-scheme: light) {
  body.theme-system {
    --bg-color: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --text-color: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --border-secondary: #cbd5e0;
  }
}

/* ============================================================================
   LAYOUT COMPONENTS
   ============================================================================ */

.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* ============================================================================
   BUTTON COMPONENTS (from Button.module.css)
   ============================================================================ */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  gap: var(--spacing-sm);
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

/* Button Variants */
.button.primary {
  color: white;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.button.primary:hover:not(:disabled) {
  background-color: #2c5282;
  border-color: #2c5282;
}

.button.secondary {
  color: var(--text-color);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

.button.secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
}

.button.outline {
  color: var(--text-color);
  background-color: transparent;
  border-color: var(--border-color);
}

.button.outline:hover:not(:disabled) {
  background-color: var(--bg-secondary);
}

.button.ghost {
  color: var(--text-color);
  background-color: transparent;
  border-color: transparent;
}

.button.ghost:hover:not(:disabled) {
  background-color: var(--bg-secondary);
}

.button.danger {
  color: white;
  background-color: var(--color-error);
  border-color: var(--color-error);
}

.button.danger:hover:not(:disabled) {
  background-color: #c53030;
  border-color: #c53030;
}

/* Button Sizes */
.button.xs {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
}

.button.sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.875rem;
}

.button.md {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
}

.button.lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

.button.xl {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1.25rem;
}

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

/* Spacing */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

/* Text */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Colors */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

/* Border Radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 9999px; }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Transitions */
.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* Visibility */
.hidden { display: none; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ============================================================================
   LEGACY SUPPORT
   ============================================================================ */

/* Storybook Button Support */
.storybook-button {
  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 700;
  border: 0;
  border-radius: 3em;
  cursor: pointer;
  display: inline-block;
  line-height: 1;
}

.storybook-button--primary {
  color: white;
  background-color: #1ea7fd;
}

.storybook-button--secondary {
  color: #333;
  background-color: transparent;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset;
}

.storybook-button--small {
  font-size: 12px;
  padding: 10px 16px;
}

.storybook-button--medium {
  font-size: 14px;
  padding: 11px 20px;
}

.storybook-button--large {
  font-size: 16px;
  padding: 12px 24px;
}

:root {
  /* Color System - Primary */
  --color-primary: #2563eb;
  --color-primary-light: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-primary-hover: #1e40af;

  /* Color System - Secondary */
  --color-secondary: #64748b;
  --color-secondary-light: #94a3b8;
  --color-secondary-dark: #475569;
  --color-secondary-hover: #334155;

  /* Color System - Status */
  --color-success: #22c55e;
  --color-success-light: #4ade80;
  --color-success-dark: #16a34a;
  --color-warning: #f59e0b;
  --color-warning-light: #fbbf24;
  --color-warning-dark: #d97706;
  --color-error: #ef4444;
  --color-error-light: #f87171;
  --color-error-dark: #dc2626;
  --color-info: #3b82f6;
  --color-info-light: #60a5fa;
  --color-info-dark: #2563eb;

  /* Background Colors */
  --color-background: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-tertiary: #f1f5f9;
  --color-surface: #ffffff;
  --color-surface-elevated: #ffffff;

  /* Text Colors */
  --color-text: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;
  --color-text-muted: #64748b;

  /* Border Colors */
  --color-border: #e2e8f0;
  --color-border-light: #f1f5f9;
  --color-border-dark: #cbd5e1;
  --color-border-focus: var(--color-primary);

  /* Shadow Colors */
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-light: rgba(0, 0, 0, 0.05);
  --color-shadow-dark: rgba(0, 0, 0, 0.2);

  /* Spacing System */
  --spacing-0: 0;
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 0.75rem;  /* 12px */
  --spacing-4: 1rem;     /* 16px */
  --spacing-5: 1.25rem;  /* 20px */
  --spacing-6: 1.5rem;   /* 24px */
  --spacing-8: 2rem;     /* 32px */
  --spacing-10: 2.5rem;  /* 40px */
  --spacing-12: 3rem;    /* 48px */
  --spacing-16: 4rem;    /* 64px */
  --spacing-20: 5rem;    /* 80px */
  --spacing-24: 6rem;    /* 96px */

  /* Legacy spacing aliases */
  --spacing-xs: var(--spacing-1);
  --spacing-sm: var(--spacing-2);
  --spacing-md: var(--spacing-4);
  --spacing-lg: var(--spacing-6);
  --spacing-xl: var(--spacing-8);

  /* Border Radius */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;  /* 2px */
  --border-radius-md: 0.25rem;   /* 4px */
  --border-radius-lg: 0.5rem;    /* 8px */
  --border-radius-xl: 0.75rem;   /* 12px */
  --border-radius-2xl: 1rem;     /* 16px */
  --border-radius-full: 9999px;

  /* Legacy border radius aliases */
  --border-radius: var(--border-radius-md);
  --border-radius-round: var(--border-radius-full);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Typography */
  --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  --font-family-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 250ms ease;
  --transition-slow: 350ms ease;
  --transition-all: all var(--transition-base);

  /* Z-Index Scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Animation Speeds */
  --animation-speed-fast: 0.15s;
  --animation-speed-base: 0.25s;
  --animation-speed-slow: 0.35s;
  --animation-speed-theme: 0.3s;
  --animation-speed-interface: 0.2s;

  /* Component Specific Variables */
  --toolbar-height: 48px;
  --sidebar-width: 240px;
  --tab-height: 36px;
  --address-bar-height: 40px;
}

/* ============================================================================
   DARK THEME
   ============================================================================ */

[data-theme="dark"] {
  /* Background Colors */
  --color-background: #0f172a;
  --color-background-secondary: #1e293b;
  --color-background-tertiary: #334155;
  --color-surface: #1e293b;
  --color-surface-elevated: #334155;

  /* Text Colors */
  --color-text: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #0f172a;
  --color-text-muted: #64748b;

  /* Border Colors */
  --color-border: #334155;
  --color-border-light: #475569;
  --color-border-dark: #1e293b;

  /* Shadow Colors */
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-light: rgba(0, 0, 0, 0.2);
  --color-shadow-dark: rgba(0, 0, 0, 0.5);

  /* Component adjustments for dark theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* ============================================================================
   SYSTEM THEME PREFERENCE
   ============================================================================ */

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Apply dark theme variables when system preference is dark */
    --color-background: #0f172a;
    --color-background-secondary: #1e293b;
    --color-background-tertiary: #334155;
    --color-surface: #1e293b;
    --color-surface-elevated: #334155;
    --color-text: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-inverse: #0f172a;
    --color-border: #334155;
    --color-border-light: #475569;
    --color-border-dark: #1e293b;
    --color-shadow: rgba(0, 0, 0, 0.3);
  }
}

/* ============================================================================
   LEGACY VARIABLE ALIASES FOR BACKWARD COMPATIBILITY
   ============================================================================ */

:root {
  /* Legacy color aliases */
  --primary-color: var(--color-primary);
  --secondary-color: var(--color-secondary);
  --accent-color: var(--color-primary);
  --accent-color-light: var(--color-primary-light);
  --background-primary: var(--color-background);
  --background-secondary: var(--color-background-secondary);
  --text-primary: var(--color-text);
  --text-secondary: var(--color-text-secondary);
  --border-color: var(--color-border);

  /* Legacy browser-specific aliases */
  --bg-color: var(--color-background);
  --text-color: var(--color-text);
  --toolbar-bg: var(--color-background-secondary);
  --toolbar-button-hover-bg: var(--color-background-tertiary);
  --tab-bg: var(--color-background-secondary);
  --tab-active-bg: var(--color-background);
  --tab-hover-bg: var(--color-background-tertiary);
  --tab-text-color: var(--color-text);
  --address-bar-bg: var(--color-background);
  --address-bar-text-color: var(--color-text);
  --address-bar-border-color: var(--color-border);
  --panel-bg: var(--color-background);
  --panel-border-color: var(--color-border);
  --panel-header-bg: var(--color-background-secondary);
  --panel-text-color: var(--color-text);
  --panel-item-hover-bg: var(--color-background-tertiary);
  --scrollbar-thumb-bg: var(--color-border-dark);
  --scrollbar-track-bg: var(--color-background-secondary);

  /* Legacy component aliases */
  --color-panel-bg: var(--color-background);
  --color-panel-border: var(--color-border);
  --color-panel-shadow: var(--color-shadow);
  --color-panel-header-bg: var(--color-background-secondary);
  --color-panel-header-text: var(--color-text);
  --color-settings-group-border: var(--color-border-light);
  --color-scrollbar-thumb: var(--color-border-dark);
  --color-scrollbar-track: var(--color-background-secondary);
}

/* ============================================================================
   BASE STYLES
   ============================================================================ */

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
}

body {
  margin: 0;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background-color: inherit;
  transition: background-color var(--transition-base), color var(--transition-base);
}

/* ============================================================================
   UTILITY CLASSES
   ============================================================================ */

/* Spacing utilities */
.p-0 { padding: var(--spacing-0); }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.m-0 { margin: var(--spacing-0); }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

/* Text utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Color utilities */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-background-secondary); }
.bg-surface { background-color: var(--color-surface); }

/* Border utilities */
.border { border: 1px solid var(--color-border); }
.border-t { border-top: 1px solid var(--color-border); }
.border-b { border-bottom: 1px solid var(--color-border); }
.border-l { border-left: 1px solid var(--color-border); }
.border-r { border-right: 1px solid var(--color-border); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: var(--border-radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Transition utilities */
.transition { transition: var(--transition-all); }
.transition-fast { transition-duration: var(--transition-fast); }
.transition-slow { transition-duration: var(--transition-slow); }

/* Accessibility utilities */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
