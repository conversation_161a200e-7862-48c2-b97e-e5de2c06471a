/**
 * Unified Logger
 * Consolidates all logging functionality from Logger.ts, EnhancedLogger.ts, and main/logging.ts
 */

import winston, { format } from 'winston';
import { EventEmitter } from 'events';
import { WriteStream, createWriteStream, createReadStream } from 'fs';
import { join } from 'path';
import { app } from 'electron';

// Enhanced types combining all loggers
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal' | 'trace';

export interface LogEntry {
  id: string;
  level: LogLevel;
  message: string;
  timestamp: number;
  correlationId?: string;
  context?: Record<string, any>;
  stack?: string;
  module?: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

export interface UnifiedLoggerConfig {
  app: {
    name: string;
    version: string;
    environment: string;
    debug: boolean;
    logLevel: LogLevel;
  };
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  enableFile: boolean;
  maxEntries: number;
  maxFileSize: number;
  maxFiles: number;
  remoteEndpoint?: string;
  bufferSize: number;
  flushInterval: number;
  enableStructuredLogging: boolean;
  enablePerformanceLogging: boolean;
  enableRotation: boolean;
}

export class UnifiedLogger extends EventEmitter {
  private static instance: UnifiedLogger;
  private config: UnifiedLoggerConfig;
  private winstonLogger: winston.Logger;
  private fileStream: WriteStream;
  private logs: LogEntry[] = [];
  private buffer: LogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private correlationId: string | null = null;
  private sessionId: string;
  private currentFileSize: number = 0;

  private readonly logLevels = {
    fatal: 0,
    error: 1,
    warn: 2,
    info: 3,
    debug: 4,
    trace: 5,
  };

  private readonly levelPriority: Record<LogLevel, number> = {
    fatal: 0,
    error: 1,
    warn: 2,
    info: 3,
    debug: 4,
    trace: 5,
  };

  private constructor(config?: Partial<UnifiedLoggerConfig>) {
    super();
    this.config = this.getDefaultConfig(config);
    this.sessionId = this.generateSessionId();
    this.initialize();
  }

  public static getInstance(config?: Partial<UnifiedLoggerConfig>): UnifiedLogger {
    if (!UnifiedLogger.instance) {
      UnifiedLogger.instance = new UnifiedLogger(config);
    }
    return UnifiedLogger.instance;
  }

  private getDefaultConfig(config?: Partial<UnifiedLoggerConfig>): UnifiedLoggerConfig {
    return {
      app: {
        name: 'A14-Browser',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        debug: process.env.NODE_ENV !== 'production',
        logLevel: (process.env.LOG_LEVEL as LogLevel) || 'info',
      },
      level: 'info',
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      enableFile: true,
      maxEntries: 1000,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      maxFiles: 5,
      bufferSize: 50,
      flushInterval: 5000,
      enableStructuredLogging: true,
      enablePerformanceLogging: true,
      enableRotation: true,
      ...config,
    };
  }

  private initialize(): void {
    this.initializeFileLogging();
    this.initializeWinstonLogger();
    this.startFlushTimer();
    this.setupProcessHandlers();
  }

  private initializeFileLogging(): void {
    if (!this.config.enableFile) return;

    try {
      const logPath = join(app.getPath('userData'), 'logs', 'app.log');
      this.fileStream = createWriteStream(logPath, { flags: 'a' });
      this.currentFileSize = this.getFileSize(logPath);
    } catch (error) {
      // Fallback for non-Electron environments
      this.fileStream = createWriteStream('logs/app.log', { flags: 'a' });
    }
  }

  private initializeWinstonLogger(): void {
    const { combine, timestamp, printf, colorize } = format;

    const logFormat = printf(({ level, message, timestamp, ...metadata }) => {
      let msg = `${timestamp} [${level.toUpperCase()}]: ${message}`;
      if (Object.keys(metadata).length > 0) {
        msg += `\n${JSON.stringify(metadata, null, 2)}`;
      }
      return msg;
    });

    this.winstonLogger = winston.createLogger({
      level: this.config.app.logLevel,
      format: combine(timestamp(), logFormat),
      transports: [
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
        }),
      ],
    });

    // Add console transport for development
    if (this.config.app.environment !== 'production' && this.config.enableConsole) {
      this.winstonLogger.add(
        new winston.transports.Console({
          format: combine(colorize(), logFormat),
        })
      );
    }
  }

  private setupProcessHandlers(): void {
    // Log uncaught exceptions
    process.on('uncaughtException', error => {
      this.error('Uncaught Exception', error);
    });

    // Log unhandled promise rejections
    process.on('unhandledRejection', reason => {
      this.error(
        'Unhandled Promise Rejection',
        reason instanceof Error ? reason : new Error(String(reason))
      );
    });

    // Log app events
    if (app) {
      app.on('ready', () => this.info('Application ready'));
      app.on('window-all-closed', () => this.info('All windows closed'));
      app.on('activate', () => this.info('Application activated'));
      app.on('quit', () => {
        this.info('Application quitting');
        this.close();
      });
    }
  }

  // Enhanced logging methods combining all loggers
  public error(message: string, error?: Error | any, context?: Record<string, any>): void {
    const logContext = {
      ...context,
      error: error instanceof Error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : error,
    };
    this.log('error', message, logContext);
    this.winstonLogger.error(message, logContext);
  }

  public warn(message: string, meta?: any, context?: Record<string, any>): void {
    const logContext = { ...context, meta };
    this.log('warn', message, logContext);
    this.winstonLogger.warn(message, logContext);
  }

  public info(message: string, meta?: any, context?: Record<string, any>): void {
    const logContext = { ...context, meta };
    this.log('info', message, logContext);
    this.winstonLogger.info(message, logContext);
  }

  public debug(message: string, meta?: any, context?: Record<string, any>): void {
    const logContext = { ...context, meta };
    this.log('debug', message, logContext);
    this.winstonLogger.debug(message, logContext);
  }

  public trace(message: string, meta?: any, context?: Record<string, any>): void {
    const logContext = { ...context, meta };
    this.log('trace', message, logContext);
    this.winstonLogger.silly(message, logContext);
  }

  public fatal(message: string, error?: Error, context?: Record<string, any>): void {
    const logContext = {
      ...context,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    };
    this.log('fatal', message, logContext);
    this.winstonLogger.error(message, logContext);
  }

  public performance(operation: string, duration: number, context?: Record<string, any>): void {
    if (!this.config.enablePerformanceLogging) return;

    this.log('info', `Performance: ${operation}`, {
      ...context,
      performance: {
        operation,
        duration,
        timestamp: Date.now(),
      },
    });
  }

  // Core logging method
  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    if (this.levelPriority[level] > this.levelPriority[this.config.level]) {
      return;
    }

    const entry: LogEntry = {
      id: this.generateLogId(),
      level,
      message,
      timestamp: Date.now(),
      correlationId: this.correlationId || undefined,
      context,
      module: this.getCallerModule(),
      userId: this.getUserId(),
      sessionId: this.sessionId,
      metadata,
    };

    // Add to logs array
    this.logs.push(entry);
    if (this.logs.length > this.config.maxEntries) {
      this.logs.shift();
    }

    // Add to buffer for remote logging
    if (this.config.enableRemote) {
      this.buffer.push(entry);
    }

    // Log to console
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // Store in local storage
    if (this.config.enableStorage) {
      this.storeLog(entry);
    }

    // Write to file
    if (this.config.enableFile) {
      this.writeToFile(entry);
    }

    this.emit('log', entry);
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const prefix = `[${timestamp}] ${entry.level.toUpperCase()}:`;
    
    switch (entry.level) {
      case 'fatal':
      case 'error':
        console.error(prefix, entry.message, entry.context);
        break;
      case 'warn':
        console.warn(prefix, entry.message, entry.context);
        break;
      case 'info':
        console.info(prefix, entry.message, entry.context);
        break;
      case 'debug':
      case 'trace':
        console.debug(prefix, entry.message, entry.context);
        break;
    }
  }

  private storeLog(entry: LogEntry): void {
    try {
      const stored = JSON.parse(localStorage.getItem('browser_logs') || '[]');
      stored.push(entry);
      if (stored.length > this.config.maxEntries) {
        stored.shift();
      }
      localStorage.setItem('browser_logs', JSON.stringify(stored));
    } catch (error) {
      // Ignore storage errors
    }
  }

  private writeToFile(entry: LogEntry): void {
    if (!this.fileStream) return;

    const formattedMessage = this.formatMessage(entry);
    this.fileStream.write(formattedMessage);
    this.currentFileSize += formattedMessage.length;

    // Check for file rotation
    if (this.config.enableRotation && this.currentFileSize > this.config.maxFileSize) {
      this.rotateLogFile();
    }
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString();
    const contextStr = entry.context ? `\n${JSON.stringify(entry.context, null, 2)}` : '';
    return `[${timestamp}] ${entry.level.toUpperCase()}: ${entry.message}${contextStr}\n`;
  }

  private rotateLogFile(): void {
    // Implementation for log file rotation
    try {
      this.fileStream.end();
      // Rotate files logic here
      this.initializeFileLogging();
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private flushBuffer(): void {
    if (this.buffer.length === 0 || !this.config.remoteEndpoint) return;

    const logsToSend = [...this.buffer];
    this.buffer = [];

    fetch(this.config.remoteEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        logs: logsToSend,
        sessionId: this.sessionId,
        timestamp: Date.now(),
      }),
    }).catch(error => {
      console.warn('Failed to send logs to remote endpoint:', error);
      // Re-add logs to buffer for retry
      this.buffer.unshift(...logsToSend);
    });
  }

  private startFlushTimer(): void {
    if (this.config.enableRemote) {
      this.flushTimer = setInterval(() => {
        this.flushBuffer();
      }, this.config.flushInterval);
    }
  }

  private getFileSize(filePath: string): number {
    try {
      const fs = require('fs');
      const stats = fs.statSync(filePath);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCallerModule(): string {
    const stack = new Error().stack;
    if (stack) {
      const lines = stack.split('\n');
      for (let i = 3; i < lines.length; i++) {
        const match = lines[i].match(/at .* \((.+):(\d+):(\d+)\)/);
        if (match) {
          return match[1].split('/').pop() || 'unknown';
        }
      }
    }
    return 'unknown';
  }

  private getUserId(): string | undefined {
    // Implementation to get current user ID
    return undefined;
  }

  // Public utility methods
  public setCorrelationId(id: string): void {
    this.correlationId = id;
  }

  public setLevel(level: LogLevel): void {
    this.config.level = level;
    this.winstonLogger.level = level;
  }

  public getLogs(level?: LogLevel, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return filteredLogs;
  }

  public clearLogs(): void {
    this.logs = [];
    this.buffer = [];
    try {
      localStorage.removeItem('browser_logs');
    } catch {
      // Ignore storage errors
    }
  }

  public updateConfig(config: Partial<UnifiedLoggerConfig>): void {
    this.config = { ...this.config, ...config };

    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.startFlushTimer();
    }
  }

  public getLogger(): winston.Logger {
    return this.winstonLogger;
  }

  public close(): void {
    if (this.fileStream) {
      this.fileStream.end();
    }
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushBuffer();
    this.removeAllListeners();
  }
}

// Create singleton instance
export const logger = UnifiedLogger.getInstance();

// Legacy exports for backward compatibility
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('user-agent'),
      ip: req.ip,
    });
  });
  next();
};

export const errorLogger = (err: Error, req: any, res: any, next: any) => {
  logger.error('HTTP Error', err, {
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
    },
  });
  next(err);
};

export const logError = (error: Error, context?: Record<string, any>) => {
  logger.error(error.message, error, context);
};

export const logWarning = (message: string, data?: any, context?: Record<string, any>) => {
  logger.warn(message, data, context);
};

export const logInfo = (message: string, data?: any, context?: Record<string, any>) => {
  logger.info(message, data, context);
};

export const setupLogging = () => {
  // Already handled in constructor
  logger.info('Unified logging system initialized');
};
