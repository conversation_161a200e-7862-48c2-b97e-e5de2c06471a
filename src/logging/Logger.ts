import winston, { format } from 'winston';
import { WriteStream, createWriteStream } from 'fs';
import { join } from 'path';
import { app } from 'electron';

export interface LoggerConfig {
  app: {
    name: string;
    version: string;
    environment: string;
    debug: boolean;
    logLevel: string;
  };
}

export class Logger {
  private static instance: Logger;
  private winstonLogger: winston.Logger;
  private fileStream: WriteStream;
  private config: LoggerConfig;
  private readonly logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
    trace: 4,
  };

  private constructor(config: LoggerConfig) {
    this.config = config;
    this.initialize();
  }

  public static getInstance(config?: LoggerConfig): Logger {
    if (!Logger.instance) {
      const defaultConfig: LoggerConfig = {
        app: {
          name: 'A14-Browser',
          version: '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          debug: process.env.NODE_ENV !== 'production',
          logLevel: process.env.LOG_LEVEL || 'info',
        },
      };
      Logger.instance = new Logger(config || defaultConfig);
    }
    return Logger.instance;
  }

  private initialize(): void {
    // Initialize file stream for Electron compatibility
    try {
      const logPath = join(app.getPath('userData'), 'logs', 'app.log');
      this.fileStream = createWriteStream(logPath, { flags: 'a' });
    } catch (error) {
      // Fallback for non-Electron environments
      this.fileStream = createWriteStream('logs/app.log', { flags: 'a' });
    }

    // Initialize Winston logger
    const { combine, timestamp, printf, colorize } = format;

    const logFormat = printf(({ level, message, timestamp, ...metadata }) => {
      let msg = `${timestamp} [${level.toUpperCase()}]: ${message}`;
      if (Object.keys(metadata).length > 0) {
        msg += `\n${JSON.stringify(metadata, null, 2)}`;
      }
      return msg;
    });

    this.winstonLogger = winston.createLogger({
      level: this.config.app.logLevel,
      format: combine(timestamp(), logFormat),
      transports: [
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
        }),
      ],
    });

    // Add console transport for development
    if (this.config.app.environment !== 'production') {
      this.winstonLogger.add(
        new winston.transports.Console({
          format: combine(colorize(), logFormat),
        })
      );
    }
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const formattedData = data ? `\n${JSON.stringify(data, null, 2)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedData}\n`;
  }

  private writeToFile(level: string, message: string, data?: any): void {
    if (this.fileStream) {
      const formattedMessage = this.formatMessage(level, message, data);
      this.fileStream.write(formattedMessage);
    }
  }

  public error(message: string, meta?: any): void {
    this.winstonLogger.error(message, meta);
    this.writeToFile('error', message, meta);
  }

  public warn(message: string, meta?: any): void {
    this.winstonLogger.warn(message, meta);
    this.writeToFile('warn', message, meta);
  }

  public info(message: string, meta?: any): void {
    this.winstonLogger.info(message, meta);
    this.writeToFile('info', message, meta);
  }

  public debug(message: string, meta?: any): void {
    this.winstonLogger.debug(message, meta);
    this.writeToFile('debug', message, meta);
  }

  public trace(message: string, meta?: any): void {
    this.winstonLogger.silly(message, meta);
    this.writeToFile('trace', message, meta);
  }

  public fatal(message: string, meta?: any): void {
    this.error(message, meta);
  }

  public log(level: string, message: string, meta?: any): void {
    this.winstonLogger.log(level, message, meta);
    this.writeToFile(level, message, meta);
  }

  public setLevel(level: keyof typeof this.logLevels): void {
    this.winstonLogger.level = level;
  }

  public getLogger(): winston.Logger {
    return this.winstonLogger;
  }

  public close(): void {
    if (this.fileStream) {
      this.fileStream.end();
    }
  }
}

// Create singleton instance
export const logger = Logger.getInstance();

// Add request logging middleware
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('user-agent'),
      ip: req.ip,
    });
  });
  next();
};

// Add error logging middleware
export const errorLogger = (err: Error, req: any, res: any, next: any) => {
  logger.error('HTTP Error', {
    error: {
      message: err.message,
      stack: err.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
    },
  });
  next(err);
};
