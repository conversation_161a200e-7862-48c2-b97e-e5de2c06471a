const { ElectronBlocker } = require('@ghostery/adblocker-electron');
const fetch = require('cross-fetch');
const { session } = require('electron');
const { session } = require('electron');
// const errorHandler = require('../utils/errorHandling'); // Now injected

/**
 * Manages the ad-blocking functionality using the ElectronBlocker library.
 * This class initializes the ad blocker and enables it for the default session.
 */
class AdBlockerManager {
  static dependencies = ['ConfigManager', 'ErrorHandler'];
  /**
   * Creates an instance of AdBlockerManager.
   * @param {object} configManager - The configuration manager instance.
   */
  constructor(configManager, errorHandler) {
    this.configManager = configManager;
    this.errorHandler = errorHandler;
    /**
     * The ElectronBlocker instance.
     * @type {ElectronBlocker|null}
     */
    this.adBlocker = null; // Will be initialized asynchronously
  }

  /**
   * Initializes the ad blocker asynchronously.
   * It loads prebuilt ad and tracking filters and enables blocking for the default session.
   * Logs success or failure of the initialization.
   * @async
   * @returns {Promise<void>}
   */
  async initialize() {
    this.errorHandler.wrapSync(() => {
      global.logger.info('Initializing AdBlockerManager...');
    }, 'AdBlockerManager:initialize');

    try {
      this.adBlocker = await this.errorHandler.wrapAsync(async () => {
        const blocker = await ElectronBlocker.fromPrebuiltAdsAndTracking(fetch);
        blocker.enableBlockingInSession(session.defaultSession);
        return blocker;
      }, 'AdBlockerManager:fromPrebuiltAdsAndTracking')();

      this.errorHandler.wrapSync(() => {
        global.logger.info('AdBlocker initialized and blocking enabled.');
      }, 'AdBlockerManager:initializedAndEnabled');
    } catch (error) {
      this.errorHandler.handleError(error, 'AdBlockerManager:initialize');
    }
  }

  /**
   * Enables ad blocking.
   */
  async enableAdBlocking() {
    if (this.adBlocker) {
      this.adBlocker.enableBlockingInSession(session.defaultSession);
      global.logger.info('AdBlocker enabled.');
    }
  }

  /**
   * Disables ad blocking.
   */
  async disableAdBlocking() {
    if (this.adBlocker) {
      this.adBlocker.disableBlockingInSession(session.defaultSession);
      global.logger.info('AdBlocker disabled.');
    }
  }

  /**
   * Checks if ad blocking is enabled.
   * @returns {boolean} True if ad blocking is enabled, false otherwise.
   */
  isEnabled() {
    return this.adBlocker ? this.adBlocker.isBlockingEnabled(session.defaultSession) : false;
  }
}

module.exports = { AdBlockerManager };
