import Store from 'electron-store';
import { BrowserSettings } from '../../shared/types/settings';

const defaultSettings: BrowserSettings = {
  homepage: 'https://a14browser.com/newtab',
  theme: 'system',
  showBookmarksBar: true,
  blockAds: true,
};

export class SettingsManager {
  private store: Store<BrowserSettings>;

  constructor() {
    this.store = new Store<BrowserSettings>({
      defaults: defaultSettings,
      // Migrations for older config versions can be added here
    });
  }

  public getAllSettings(): BrowserSettings {
    return this.store.store;
  }

  public getSetting<K extends keyof BrowserSettings>(key: K): BrowserSettings[K] {
    return this.store.get(key);
  }

  public setSetting<K extends keyof BrowserSettings>(key: K, value: BrowserSettings[K]): void {
    this.store.set(key, value);
  }
}