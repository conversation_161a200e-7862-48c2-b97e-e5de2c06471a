import * as Sentry from '@sentry/electron';
import { Integrations } from '@sentry/tracing';
import { BrowserWindow, app } from 'electron';

import { logger } from './logging';

interface ErrorReport {
  error: Error;
  context?: Record<string, any>;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
  tags?: Record<string, string>;
  level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
  breadcrumbs?: Array<{
    category: string;
    message: string;
    level: string;
    timestamp: number;
  }>;
}

class ErrorTracker {
  private static instance: ErrorTracker;
  private isInitialized: boolean = false;
  private breadcrumbs: ErrorReport['breadcrumbs'] = [];
  private readonly maxBreadcrumbs: number = 100;

  private constructor() {}

  public static getInstance(): ErrorTracker {
    if (!ErrorTracker.instance) {
      ErrorTracker.instance = new ErrorTracker();
    }
    return ErrorTracker.instance;
  }

  public initialize(dsn: string, environment: string = 'development'): void {
    if (this.isInitialized) {
      return;
    }

    Sentry.init({
      dsn,
      environment,
      integrations: [
        new Integrations.BrowserTracing(),
        new Sentry.Integrations.GlobalHandlers(),
        new Sentry.Integrations.Process(),
        new Sentry.Integrations.Modules(),
      ],
      tracesSampleRate: 1.0,
      beforeSend: (event: Sentry.Event) => {
        // Add custom context
        event.extra = {
          ...event.extra,
          appVersion: app.getVersion(),
          electronVersion: process.versions.electron,
          chromeVersion: process.versions.chrome,
          nodeVersion: process.versions.node,
        };
        return event;
      },
    });

    this.setupGlobalHandlers();
    this.isInitialized = true;
  }

  private setupGlobalHandlers(): void {
    // Handle uncaught exceptions
    process.on('uncaughtException', error => {
      this.captureException(error, {
        tags: { type: 'uncaughtException' },
        level: 'fatal',
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', reason => {
      this.captureException(reason instanceof Error ? reason : new Error(String(reason)), {
        tags: { type: 'unhandledRejection' },
        level: 'error',
      });
    });

    // Handle renderer process crashes
    app.on('render-process-gone', (event, webContents, details) => {
      this.captureException(new Error(`Renderer process crashed: ${details.reason}`), {
        tags: { type: 'renderer-crash', reason: details.reason },
        level: 'fatal',
      });
    });

    // Handle main process crashes
    app.on('child-process-gone', (event, details) => {
      this.captureException(new Error(`Child process crashed: ${details.type}`), {
        tags: { type: 'child-process-crash', processType: details.type },
        level: 'fatal',
      });
    });
  }

  public addBreadcrumb(category: string, message: string, level: string = 'info'): void {
    this.breadcrumbs.push({
      category,
      message,
      level,
      timestamp: Date.now(),
    });

    if (this.breadcrumbs.length > this.maxBreadcrumbs) {
      this.breadcrumbs.shift();
    }
  }

  public captureException(error: Error, options: Partial<ErrorReport> = {}): void {
    const report: ErrorReport = {
      error,
      context: options.context,
      user: options.user,
      tags: options.tags,
      level: options.level || 'error',
      breadcrumbs: this.breadcrumbs,
    };

    // Log to local logger
    logger.error(error.message, { error, context: options.context });

    // Send to Sentry
    Sentry.withScope((scope: Sentry.Scope) => {
      if (report.context) {
        scope.setExtras(report.context);
      }
      if (report.user) {
        scope.setUser(report.user);
      }
      if (report.tags) {
        scope.setTags(report.tags);
      }
      if (report.breadcrumbs && report.breadcrumbs.length > 0) {
        report.breadcrumbs.forEach(crumb => {
          scope.addBreadcrumb(crumb);
        });
      }
      scope.setLevel(report.level as Sentry.Severity);
      Sentry.captureException(error);
    });
  }

  public setUser(user: ErrorReport['user']): void {
    Sentry.setUser(user);
  }

  public clearUser(): void {
    Sentry.setUser(null);
  }

  public addTag(key: string, value: string): void {
    Sentry.setTag(key, value);
  }

  public addContext(key: string, value: any): void {
    Sentry.setExtra(key, value);
  }

  public captureMessage(message: string, options: Partial<ErrorReport> = {}): void {
    const report: ErrorReport = {
      error: new Error(message),
      context: options.context,
      user: options.user,
      tags: options.tags,
      level: options.level || 'info',
      breadcrumbs: this.breadcrumbs,
    };

    // Log to local logger
    logger.info(message, { context: options.context });

    // Send to Sentry
    Sentry.withScope((scope: Sentry.Scope) => {
      if (report.context) {
        scope.setExtras(report.context);
      }
      if (report.user) {
        scope.setUser(report.user);
      }
      if (report.tags) {
        scope.setTags(report.tags);
      }
      if (report.breadcrumbs && report.breadcrumbs.length > 0) {
        report.breadcrumbs.forEach(crumb => {
          scope.addBreadcrumb(crumb);
        });
      }
      scope.setLevel(report.level as Sentry.Severity);
      Sentry.captureMessage(message);
    });
  }

  public flush(): Promise<boolean> {
    return Sentry.flush();
  }
}

export const errorTracker = ErrorTracker.getInstance();

export const setupErrorTracking = (dsn: string, environment?: string): void => {
  errorTracker.initialize(dsn, environment);
};

export const trackError = (error: Error, options: Partial<ErrorReport> = {}): void => {
  errorTracker.captureException(error, options);
};

export const trackMessage = (message: string, options: Partial<ErrorReport> = {}): void => {
  errorTracker.captureMessage(message, options);
};

export const addErrorBreadcrumb = (category: string, message: string, level?: string): void => {
  errorTracker.addBreadcrumb(category, message, level);
};

export const setErrorUser = (user: ErrorReport['user']): void => {
  errorTracker.setUser(user);
};

export const clearErrorUser = (): void => {
  errorTracker.clearUser();
};

export const addErrorTag = (key: string, value: string): void => {
  errorTracker.addTag(key, value);
};

export const addErrorContext = (key: string, value: any): void => {
  errorTracker.addContext(key, value);
};

export const flushErrorTracking = (): Promise<boolean> => {
  return errorTracker.flush();
};
