import { app } from 'electron';
import path from 'path';
import { promises as fs } from 'fs';

const SESSION_FILE = 'session.json';

function getSessionFilePath(): string {
  return path.join(app.getPath('userData'), SESSION_FILE);
}

/**
 * Saves the session data (an array of URLs) to a file.
 * @param urls The array of URLs to save.
 */
export async function saveSessionData(urls: string[]): Promise<void> {
  const filePath = getSessionFilePath();
  try {
    const data = JSON.stringify({ urls });
    await fs.writeFile(filePath, data, 'utf-8');
  } catch (error) {
    console.error('Error saving session data:', error);
  }
}

/**
 * Loads the session data from a file.
 * @returns A promise that resolves to an array of URLs or null if not found or an error occurs.
 */
export async function loadSessionData(): Promise<string[] | null> {
  const filePath = getSessionFilePath();
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    const session = JSON.parse(data);
    return Array.isArray(session?.urls) ? session.urls : null;
  } catch (error) {
    // If the file doesn't exist, it's not an error, just no session to restore.
    if (error.code === 'ENOENT') {
      return null;
    }
    console.error('Error loading session data:', error);
    return null;
  }
}