const Store = require('electron-store');

/**
 * @class ConfigManager
 * @description Manages A11 Browser configuration and user settings using electron-store.
 * Provides methods to get, set, check, delete, clear, and reset configuration values.
 */
class ConfigManager {
  static dependencies = ['app', 'config', 'Error<PERSON>and<PERSON>'];

  constructor(app, config, errorHandler) {
    this.app = app;
    this.store = new Store(config);
    this.errorHandler = errorHandler;

    this.errorHandler.wrapSync(this.initializeDefaults.bind(this));
  }

  /**
   * Retrieves a configuration value by its key.
   * @param {string} key - The key of the configuration item.
   * @returns {*} The value associated with the key, or undefined if not found.
   */
  get(key) {
    return this.store.get(key);
  }

  /**
   * Sets a configuration value for a given key.
   * @param {string} key - The key of the configuration item.
   * @param {*} value - The value to set.
   * @returns {void}
   */
  set(key, value) {
    this.store.set(key, value);
  }

  /**
   * Checks if a configuration key exists.
   * @param {string} key - The key to check.
   * @returns {boolean} True if the key exists, false otherwise.
   */
  has(key) {
    return this.store.has(key);
  }

  /**
   * Deletes a configuration item by its key.
   * @param {string} key - The key of the configuration item to delete.
   * @returns {void}
   */
  delete(key) {
    this.store.delete(key);
  }

  /**
   * Clears all configuration settings, effectively resetting them to an empty state.
   * @returns {void}
   */
  clear() {
    this.store.clear();
  }

  /**
   * Retrieves all configuration settings.
   * @returns {object} An object containing all current configuration settings.
   */
  getAll() {
    return this.store.store;
  }

  /**
   * Resets all configuration settings to their default values.
   * @returns {void}
   */
  reset() {
    // The `reset` method of electron-store resets the store to its default values.
    this.store.reset();
  }
}

module.exports = ConfigManager;
