/**
 * @file errorHandling.js
 * @brief Centralized error handling module for the main process.
 */

class ErrorHandler {
  /**
   * @param {object} logger - The logger instance to use for error logging.
   */
  constructor(logger) {
    this.logger = logger;
    this.errorListeners = [];
  }

  /**
   * @brief Registers a global error handler.
   * @param {Function} handler - The function to call when an error occurs. It receives the error object.
   */
  registerGlobalHandler(handler) {
    if (typeof handler === 'function') {
      this.errorListeners.push(handler);
    }
  }

  /**
   * @brief Unregisters a global error handler.
   * @param {Function} handler - The handler function to remove.
   */
  unregisterGlobalHandler(handler) {
    this.errorListeners = this.errorListeners.filter(l => l !== handler);
  }

  /**
   * @brief Handles an error by logging it and notifying all registered listeners.
   * @param {Error} error - The error object to handle.
   * @param {string} [context='General'] - Optional context for the error (e.g., 'IPC', 'Lifecycle').
   */
  handleError(error, context = 'General') {
    this.logger.error(`[${context} Error]: ${error.message}`, {
      stack: error.stack,
      context: context,
    });
    this.errorListeners.forEach(listener => {
      try {
        listener(error, context);
      } catch (listenerError) {}
    });
  }

  /**
   * @brief Wraps an asynchronous function with error handling.
   * @param {Function} fn - The async function to wrap.
   * @param {string} context - The context for the error handling.
   * @returns {Function} A new async function with error handling.
   */
  wrapAsync(fn, context) {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handleError(error, context);
        throw error; // Re-throw the error so calling code can still handle it if needed
      }
    };
  }

  /**
   * @brief Wraps a synchronous function with error handling.
   * @param {Function} fn - The sync function to wrap.
   * @param {string} context - The context for the error handling.
   * @returns {Function} A new sync function with error handling.
   */
  wrapSync(fn, context) {
    return (...args) => {
      try {
        return fn(...args);
      } catch (error) {
        this.handleError(error, context);
        throw error; // Re-throw the error so calling code can still handle it if needed
      }
    };
  }
}

module.exports = ErrorHandler;
