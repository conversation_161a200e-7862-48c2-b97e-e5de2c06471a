import { app, ipcMain } from 'electron';
import path from 'path';
import { promises as fs } from 'fs';

const SETTINGS_FILE = 'settings.json';

function getSettingsFilePath(): string {
  return path.join(app.getPath('userData'), SETTINGS_FILE);
}

export async function readSettings(): Promise<object> {
  const filePath = getSettingsFilePath();
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    if (error.code === 'ENOENT') {
      return {}; // No settings file yet, return empty object
    }
    console.error('Error reading settings file:', error);
    return {};
  }
}

async function writeSettings(settings: object): Promise<void> {
  const filePath = getSettingsFilePath();
  try {
    await fs.writeFile(filePath, JSON.stringify(settings, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing settings file:', error);
  }
}

export function setupSettingsHandlers(options: { onSettingsChanged: (settings: object) => void }): void {
  ipcMain.handle('get-settings', async () => await readSettings());

  ipcMain.handle('save-settings', async (_event, newSettings: object) => {
    // Read current settings, merge with new ones, and write back
    const currentSettings = await readSettings();
    const updatedSettings = { ...currentSettings, ...newSettings };
    await writeSettings(updatedSettings);
    // Notify the main process to apply any relevant changes
    options.onSettingsChanged(updatedSettings);
    return true;
  });
}