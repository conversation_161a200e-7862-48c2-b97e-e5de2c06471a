import { join } from 'path';

import { BrowserWindow, app, net, session } from 'electron';

import { trackError } from './error-tracking';
import { logger } from './logging';
import { performanceMonitor } from './performance';

interface SecurityConfig {
  csp: {
    defaultSrc: string[];
    scriptSrc: string[];
    styleSrc: string[];
    imgSrc: string[];
    connectSrc: string[];
    fontSrc: string[];
    objectSrc: string[];
    mediaSrc: string[];
    frameSrc: string[];
  };
  permissions: {
    clipboard: boolean;
    notifications: boolean;
    camera: boolean;
    microphone: boolean;
    geolocation: boolean;
    midi: boolean;
    usb: boolean;
    serial: boolean;
    bluetooth: boolean;
  };
  features: {
    sandbox: boolean;
    contextIsolation: boolean;
    nodeIntegration: boolean;
    webSecurity: boolean;
    allowRunningInsecureContent: boolean;
  };
}

interface CertificateError {
  url: string;
  error: string;
  certificate: Electron.Certificate;
  name?: string;
  message?: string;
}

class SecurityManager {
  private static instance: SecurityManager;
  private config: SecurityConfig;
  private readonly defaultConfig: SecurityConfig = {
    csp: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'", 'https:'],
      fontSrc: ["'self'", 'data:', 'https:'],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'self'"],
    },
    permissions: {
      clipboard: true,
      notifications: true,
      camera: false,
      microphone: false,
      geolocation: false,
      midi: false,
      usb: false,
      serial: false,
      bluetooth: false,
    },
    features: {
      sandbox: true,
      contextIsolation: true,
      nodeIntegration: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
    },
  };

  private constructor() {
    this.config = { ...this.defaultConfig };
    this.setupSecurityFeatures();
  }

  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  private setupSecurityFeatures(): void {
    try {
      // Set up CSP
      session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
        const cspHeader = this.generateCSPHeader();
        callback({
          responseHeaders: {
            ...details.responseHeaders,
            'Content-Security-Policy': [cspHeader],
          },
        });
      });

      // Set up permission handlers
      session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
        const url = webContents.getURL();
        const permissionGranted =
          this.config.permissions[permission as keyof typeof this.config.permissions];

        if (permissionGranted) {
          logger.info(`Permission granted: ${permission} for ${url}`);
          callback(true);
        } else {
          logger.warn(`Permission denied: ${permission} for ${url}`);
          callback(false);
        }
      });

      // Set up certificate verification
      app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
        event.preventDefault();
        const errorMessage = `Certificate error for ${url}: ${error}`;
        logger.error('Certificate error', {
          message: errorMessage,
          certificate,
        });
        callback(false);
      });

      // Set up secure headers
      session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
        callback({
          responseHeaders: {
            ...details.responseHeaders,
            'X-Content-Type-Options': ['nosniff'],
            'X-Frame-Options': ['SAMEORIGIN'],
            'X-XSS-Protection': ['1; mode=block'],
            'Strict-Transport-Security': ['max-age=31536000; includeSubDomains'],
            'Referrer-Policy': ['strict-origin-when-cross-origin'],
          },
        });
      });

      // Monitor security events
      this.monitorSecurityEvents();
    } catch (error) {
      trackError(error as Error, {
        context: { component: 'SecurityManager' },
      });
    }
  }

  private generateCSPHeader(): string {
    const { csp } = this.config;
    return Object.entries(csp)
      .map(([key, value]) => `${key} ${value.join(' ')}`)
      .join('; ');
  }

  private monitorSecurityEvents(): void {
    // Monitor for suspicious network activity
    session.defaultSession.webRequest.onBeforeRequest(
      { urls: ['*://*/*'] },
      (details, callback) => {
        const url = new URL(details.url);
        if (this.isSuspiciousURL(url)) {
          logger.warn('Suspicious URL detected', { url: details.url });
          callback({ cancel: true });
        } else {
          callback({});
        }
      }
    );

    // Monitor for suspicious headers
    session.defaultSession.webRequest.onHeadersReceived(
      { urls: ['*://*/*'] },
      (details, callback) => {
        if (details.responseHeaders && this.hasSuspiciousHeaders(details.responseHeaders)) {
          logger.warn('Suspicious headers detected', {
            url: details.url,
            headers: details.responseHeaders,
          });
        }
        callback({ responseHeaders: details.responseHeaders });
      }
    );
  }

  private isSuspiciousURL(url: URL): boolean {
    // Implement URL validation logic
    const suspiciousPatterns = [
      /\.exe$/i,
      /\.dll$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.vbs$/i,
      /\.js$/i,
      /\.jar$/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(url.pathname));
  }

  private hasSuspiciousHeaders(headers: Record<string, string[]>): boolean {
    const suspiciousHeaders = ['x-powered-by', 'server', 'x-aspnet-version', 'x-aspnetmvc-version'];

    return suspiciousHeaders.some(header => header in headers);
  }

  public updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig,
      csp: {
        ...this.config.csp,
        ...(newConfig.csp || {}),
      },
      permissions: {
        ...this.config.permissions,
        ...(newConfig.permissions || {}),
      },
      features: {
        ...this.config.features,
        ...(newConfig.features || {}),
      },
    };

    // Apply new configuration
    this.setupSecurityFeatures();
  }

  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  public resetConfig(): void {
    this.config = { ...this.defaultConfig };
    this.setupSecurityFeatures();
  }

  public validateConfig(config: Partial<SecurityConfig>): boolean {
    try {
      // Validate CSP
      if (config.csp) {
        Object.entries(config.csp).forEach(([key, value]) => {
          if (!Array.isArray(value)) {
            throw new Error(`Invalid CSP ${key}: must be an array`);
          }
        });
      }

      // Validate permissions
      if (config.permissions) {
        Object.entries(config.permissions).forEach(([key, value]) => {
          if (typeof value !== 'boolean') {
            throw new Error(`Invalid permission ${key}: must be a boolean`);
          }
        });
      }

      // Validate features
      if (config.features) {
        Object.entries(config.features).forEach(([key, value]) => {
          if (typeof value !== 'boolean') {
            throw new Error(`Invalid feature ${key}: must be a boolean`);
          }
        });
      }

      return true;
    } catch (error) {
      trackError(error as Error, {
        context: { component: 'SecurityManager', action: 'validateConfig' },
      });
      return false;
    }
  }
}

export const securityManager = SecurityManager.getInstance();

export const configureSecurity = (config: Partial<SecurityConfig>): void => {
  if (securityManager.validateConfig(config)) {
    securityManager.updateConfig(config);
  }
};

export const getSecurityConfig = (): SecurityConfig => {
  return securityManager.getConfig();
};

export const resetSecurityConfig = (): void => {
  securityManager.resetConfig();
};

export const validateSecurityConfig = (config: Partial<SecurityConfig>): boolean => {
  return securityManager.validateConfig(config);
};

export const configureSecurity = (mainWindow: BrowserWindow) => {
  // Set secure headers
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'",
          "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
          "style-src 'self' 'unsafe-inline'",
          "img-src 'self' data: https:",
          "connect-src 'self' https:",
          "font-src 'self'",
          "object-src 'none'",
          "media-src 'self'",
          "frame-src 'none'",
        ].join('; '),
        'X-Content-Type-Options': ['nosniff'],
        'X-Frame-Options': ['DENY'],
        'X-XSS-Protection': ['1; mode=block'],
        'Referrer-Policy': ['strict-origin-when-cross-origin'],
        'Permissions-Policy': [
          'accelerometer=()',
          'camera=()',
          'geolocation=()',
          'gyroscope=()',
          'magnetometer=()',
          'microphone=()',
          'payment=()',
          'usb=()',
        ].join(', '),
      },
    });
  });

  // Configure session
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    const url = webContents.getURL();
    const allowedPermissions = ['notifications', 'clipboard-read', 'clipboard-write'];

    if (allowedPermissions.includes(permission)) {
      callback(true);
    } else {
      callback(false);
    }
  });

  // Configure window
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // Only allow opening URLs in the same origin
    if (url.startsWith('http://localhost:') || url.startsWith('file://')) {
      return { action: 'allow' };
    }
    return { action: 'deny' };
  });

  // Disable navigation
  mainWindow.webContents.on('will-navigate', (event, url) => {
    if (!url.startsWith('http://localhost:') && !url.startsWith('file://')) {
      event.preventDefault();
    }
  });

  // Disable new window creation
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('http://localhost:') || url.startsWith('file://')) {
      return { action: 'allow' };
    }
    return { action: 'deny' };
  });

  // Configure context menu
  mainWindow.webContents.on('context-menu', (e, params) => {
    // Only allow basic context menu options
    const allowedOptions = ['copy', 'paste', 'cut', 'selectAll'];
    if (!allowedOptions.includes(params.menuSourceType)) {
      e.preventDefault();
    }
  });

  // Configure file access
  app.on('web-contents-created', (event, contents) => {
    contents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);
      if (parsedUrl.origin !== 'http://localhost:3000') {
        event.preventDefault();
      }
    });
  });

  // Configure downloads
  session.defaultSession.on('will-download', (event, item, webContents) => {
    const savePath = join(app.getPath('downloads'), item.getFilename());
    item.setSavePath(savePath);

    item.on('updated', (event, state) => {
      if (state === 'interrupted') {
        console.log('Download is interrupted but can be resumed');
      } else if (state === 'progressing') {
        if (item.isPaused()) {
          console.log('Download is paused');
        } else {
          console.log(`Received bytes: ${item.getReceivedBytes()}`);
        }
      }
    });

    item.once('done', (event, state) => {
      if (state === 'completed') {
        console.log('Download successfully');
      } else {
        console.log(`Download failed: ${state}`);
      }
    });
  });

  // Configure cookies
  session.defaultSession.cookies.on('changed', (event, cookie, cause, removed) => {
    if (removed) {
      console.log(`Cookie removed: ${cookie.name}`);
    } else {
      console.log(`Cookie changed: ${cookie.name}`);
    }
  });

  // Configure cache
  session.defaultSession.clearCache().then(() => {
    console.log('Cache cleared');
  });

  // Configure storage
  session.defaultSession
    .clearStorageData({
      storages: [
        'cookies',
        'filesystem',
        'indexdb',
        'localstorage',
        'shadercache',
        'websql',
        'serviceworkers',
        'cachestorage',
      ],
    })
    .then(() => {
      console.log('Storage cleared');
    });
};
