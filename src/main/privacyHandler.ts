import { session } from 'electron';

/**
 * Applies privacy-related settings to the browser session.
 * @param settings The browser settings object.
 */
export function applyPrivacySettings(settings: any): void {
  // This will remove all `onBeforeSendHeaders` listeners and re-add them if necessary.
  // For a more complex app with multiple header listeners, a more sophisticated
  // manager would be needed to handle adding/removing specific listeners.
  session.defaultSession.webRequest.onBeforeSendHeaders(null);

  if (settings.doNotTrack) {
    session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      details.requestHeaders['DNT'] = '1';
      callback({ requestHeaders: details.requestHeaders });
    });
    console.log('[Main] Do Not Track header enabled.');
  } else {
    console.log('[Main] Do Not Track header disabled.');
  }
}