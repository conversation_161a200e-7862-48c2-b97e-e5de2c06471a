import { app, BrowserWindow, clipboard, ipcMain, Menu, session } from 'electron';
import path from 'path';
import { setupBookmarkHandlers } from './bookmarkHandler';
import { setupHistoryHandlers } from './historyHandler';
import { applyPrivacySettings } from './privacyHandler';
import { readSettings, setupSettingsHandlers } from './settingsHandler';
import { loadSessionData, saveSessionData } from './sessionHandler';

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    webPreferences: {
      // __dirname will point to 'dist/main' directory after build
      preload: path.join(__dirname, '../preload/preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      webviewTag: true, // Essential for <webview> functionality
    },
  });

  // Vite dev server URL
  if (process.env.VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }
}

app.whenReady().then(async () => {
  // --- Регистрация IPC обработчиков ---

  // Обработчики для закладок
  setupBookmarkHandlers();

  // Обработчики для истории
  setupHistoryHandlers();

  // Обработчики для настроек
  setupSettingsHandlers({
    onSettingsChanged: settings => {
      applyPrivacySettings(settings);
      // Другие настройки, требующие реакции в main процессе, можно применить здесь
    },
  });

  // Применяем глобальные настройки при запуске
  const settings = await readSettings();
  applyPrivacySettings(settings);

  // Сохранение сессии (однонаправленное)
  ipcMain.on('save-session', (_event, urls: string[]) => {
    saveSessionData(urls);
  });

  // Восстановление сессии (двунаправленное)
  ipcMain.handle('restore-session', async () => {
    return await loadSessionData();
  });

  // Пример обработчика для контекстного меню
  ipcMain.on('show-context-menu', (event, context: { type: string; data: any }) => {
    const webContents = event.sender;
    const window = BrowserWindow.fromWebContents(webContents);
    if (!window) return;

    const standardTemplate: Electron.MenuItemConstructorOptions[] = [
      { label: 'Назад', click: () => webContents.goBack(), enabled: webContents.canGoBack() },
      { label: 'Вперед', click: () => webContents.goForward(), enabled: webContents.canGoForward() },
      { label: 'Перезагрузить', click: () => webContents.reload() },
      { type: 'separator' },
    ];

    let contextTemplate: Electron.MenuItemConstructorOptions[] = [];

    if (context.type === 'link' && context.data.url) {
      contextTemplate = [
        { label: 'Открыть ссылку в новой вкладке', click: () => window.webContents.send('context-menu-command', { command: 'open-link-in-new-tab', url: context.data.url }) },
        { type: 'separator' },
        { label: 'Копировать адрес ссылки', click: () => clipboard.writeText(context.data.url) },
      ];
    } else if (context.type === 'image' && context.data.url) {
      contextTemplate = [
        { label: 'Открыть изображение в новой вкладке', click: () => window.webContents.send('context-menu-command', { command: 'open-link-in-new-tab', url: context.data.url }) },
        { label: 'Копировать URL изображения', click: () => clipboard.writeText(context.data.url) },
        { label: 'Сохранить изображение как...', click: () => {
            // This will trigger the download and automatically prompt the user to save the file.
            webContents.downloadURL(context.data.url);
        }},
      ];
    }

    const finalTemplate: Electron.MenuItemConstructorOptions[] = [...contextTemplate, ...standardTemplate, { label: 'Проверить код', click: () => webContents.openDevTools({ mode: 'detach' }) }];

    const menu = Menu.buildFromTemplate(finalTemplate);
    menu.popup({ window });
  });

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});