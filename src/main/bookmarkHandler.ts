import { app, ipcMain } from 'electron';
import path from 'path';
import { promises as fs } from 'fs';
import { v4 as uuidv4 } from 'uuid';

const BOOKMARKS_FILE = 'bookmarks.json';

interface Bookmark {
  id: string;
  url: string;
  title: string;
  createdAt: number;
}

function getBookmarksFilePath(): string {
  return path.join(app.getPath('userData'), BOOKMARKS_FILE);
}

async function readBookmarks(): Promise<Bookmark[]> {
  const filePath = getBookmarksFilePath();
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    const bookmarks = JSON.parse(data);
    return Array.isArray(bookmarks) ? bookmarks : [];
  } catch (error) {
    if (error.code === 'ENOENT') {
      return []; // File doesn't exist, which is normal on first run.
    }
    console.error('Error reading bookmarks file:', error);
    return [];
  }
}

async function writeBookmarks(bookmarks: Bookmark[]): Promise<void> {
  const filePath = getBookmarksFilePath();
  try {
    await fs.writeFile(filePath, JSON.stringify(bookmarks, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing bookmarks file:', error);
  }
}

export function setupBookmarkHandlers(): void {
  ipcMain.handle('get-bookmarks', async () => await readBookmarks());

  ipcMain.handle('add-bookmark', async (_event, { url, title }: { url: string; title: string }) => {
    const bookmarks = await readBookmarks();
    const newBookmark: Bookmark = { id: uuidv4(), url, title, createdAt: Date.now() };
    await writeBookmarks([...bookmarks, newBookmark]);
    return newBookmark;
  });

  ipcMain.handle('remove-bookmark', async (_event, id: string) => {
    const bookmarks = await readBookmarks();
    await writeBookmarks(bookmarks.filter(b => b.id !== id));
    return true;
  });
}