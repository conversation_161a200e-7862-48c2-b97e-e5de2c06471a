const { ipcMain } = require('electron');

/**
 * Manages Inter-Process Communication (IPC) between the main and renderer processes.
 * Provides methods to handle incoming messages from the renderer and send messages to it.
 */
class IPCManager {
  /**
   * @param {BrowserWindow} mainWindow - The main Electron browser window.
   */
  constructor(mainWindow) {
    this.mainWindow = mainWindow;
    /**
     * Stores registered IPC handlers, mapping channel names to their handler functions.
     * @type {Map<string, Function>}
     */
    this.handlers = new Map();
  }

  /**
   * Sets or updates the main window instance.
   * This is useful if the main window is re-created or needs to be updated after initialization.
   * @param {BrowserWindow} mainWindow - The new main Electron browser window instance.
   * @throws {Error} If the provided mainWindow is null or undefined.
   */
  setMainWindow(mainWindow) {
    if (!mainWindow) {
      throw new Error('MainWindow cannot be null or undefined.');
    }
    this.mainWindow = mainWindow;
  }

  /**
   * Initializes default IPC handlers. These are typically basic handlers that are always present.
   * More specialized handlers are registered via dedicated IPC modules.
   */
  initHandlers() {
    // No default handlers are registered here. Specialized handlers are registered
    // through dedicated IPC modules (e.g., systemIPC, adblockerIPC) in AppInitializer.
  }

  /**
   * Registers an IPC handler for a specific channel.
   * If a handler for the channel already exists, it will be overwritten.
   * @param {string} channel - The name of the IPC channel.
   * @param {Function} handler - The function to be called when a message is received on the channel.
   */
  handle(channel, handler) {
    if (this.handlers.has(channel)) {
      ipcMain.removeHandler(channel);
    }
    ipcMain.handle(channel, handler);
    this.handlers.set(channel, handler);
  }

  /**
   * Sends a message to the renderer process on a specified channel.
   * The message will only be sent if the main window is available and not destroyed.
   * @param {string} channel - The name of the IPC channel.
   * @param {...any} args - Arguments to send along with the message.
   */
  send(channel, ...args) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, ...args);
    } else {
    }
  }

  /**
   * Removes a registered IPC handler for a specific channel.
   * @param {string} channel - The name of the IPC channel to remove the handler from.
   */
  removeHandler(channel) {
    if (this.handlers.has(channel)) {
      ipcMain.removeHandler(channel);
      this.handlers.delete(channel);
    }
  }

  /**
   * Clears all registered IPC handlers.
   * This is typically used during application shutdown or window re-creation to prevent memory leaks.
   */
  clearHandlers() {
    this.handlers.forEach((handler, channel) => {
      ipcMain.removeHandler(channel);
    });
    this.handlers.clear();
  }
}

module.exports = IPCManager;
