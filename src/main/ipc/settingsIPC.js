const { ipcMain } = require('electron');

const errorHandler = require('../utils/errorHandling');

let configManagerInstance = null;

/**
 * Initializes IPC handlers for settings-related operations.
 * @param {object} configManager - The ConfigManager instance for managing settings.
 */
function initializeSettingsIPC(configManager) {
  configManagerInstance = configManager;

  /**
   * <PERSON><PERSON> requests to get a specific setting.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} key - The key of the setting to retrieve.
   * @returns {*} - The value of the setting.
   */
  ipcMain.handle(
    'get-setting',
    errorHandler.wrapSync((event, key) => {
      return configManagerInstance.get(key);
    }, 'IPC:get-setting')
  );

  /**
   * <PERSON><PERSON> requests to set a specific setting.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} key - The key of the setting to set.
   * @param {*} value - The value to set for the setting.
   * @returns {boolean} - True if the setting was successfully set.
   */
  ipcMain.handle(
    'set-setting',
    errorHandler.wrapSync((event, key, value) => {
      configManagerInstance.set(key, value);
      return true;
    }, 'IPC:set-setting')
  );

  /**
   * Handles requests to get all settings.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @returns {object} - An object containing all settings.
   */
  ipcMain.handle(
    'get-all-settings',
    errorHandler.wrapSync(() => {
      return configManagerInstance.getAll();
    }, 'IPC:get-all-settings')
  );

  /**
   * Handles requests to reset all settings to their default values.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @returns {boolean} - True if settings were successfully reset.
   */
  ipcMain.handle(
    'reset-settings',
    errorHandler.wrapSync(() => {
      configManagerInstance.reset();
      return true;
    }, 'IPC:reset-settings')
  );

  // Remove applyTheme and addCustomTheme as they are handled in renderer process
  // ipcMain.handle('apply-theme', errorHandler.wrapSync((event, themeName) => {
  //   // This logic should ideally be in the renderer process

  //   return true;
  // }));

  // ipcMain.handle('add-custom-theme', errorHandler.wrapSync((event, themeName, themeCSS) => {
  //   // This logic should ideally be in the renderer process

  //   return true;
  // }));
}

module.exports = { initializeSettingsIPC };
