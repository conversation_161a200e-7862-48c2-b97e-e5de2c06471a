const { ipcMain } = require('electron');

const { AdBlockerManager } = require('../adblocker/adBlockerManager');
const { errorManager } = require('../../core/ErrorManager');

// No specific IPC handlers needed for adblocker with @ghostery/adblocker-electron
// as it handles blocking internally and does not expose methods for user rules or list updates via IPC.
// If custom IPC is needed in the future, it can be added here.

/**
 * Initializes IPC handlers for the adblocker module.
 * Currently, no specific IPC handlers are exposed for the adblocker as it operates automatically.
 * This function serves as a placeholder for future IPC needs related to adblocker settings or controls.
 * @param {AdBlockerManager} adblockerManager - The adblocker manager instance.
 */
function initializeAdblockerIPC(adblockerManager) {
  ipcMain.handle('adblocker:toggle', async (event, enabled) => {
    if (enabled) {
      await adblockerManager.enableAdBlocking();
    } else {
      await adblockerManager.disableAdBlocking();
    }
    return adblockerManager.isEnabled();
  });

  ipcMain.handle('adblocker:getStatus', () => {
    return adblockerManager.isEnabled();
  });
}

module.exports = { initializeAdblockerIPC };
