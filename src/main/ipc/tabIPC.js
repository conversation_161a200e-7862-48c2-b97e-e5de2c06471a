const { ipcMain } = require('electron');

const errorHandler = require('../utils/errorHandling');

let mainWindowInstance = null;

/**
 * Initializes IPC handlers for tab-related operations.
 * @param {Electron.BrowserWindow} mainWindow - The main browser window instance.
 */
function initializeTabIPC(mainWindow) {
  mainWindowInstance = mainWindow;

  /**
   * <PERSON><PERSON> requests to create a new tab.
   * Sends a 'tabs-changed' event to the renderer process with action 'create'.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} url - The URL to open in the new tab.
   * @returns {boolean} - True if the event was sent, false otherwise.
   */
  ipcMain.handle(
    'create-tab',
    errorHandler.wrapSync((event, url) => {
      // Send tab creation event to the renderer
      // Actual tab creation happens on the renderer side
      if (mainWindowInstance && !mainWindowInstance.isDestroyed()) {
        mainWindowInstance.webContents.send('tabs-changed', { action: 'create', url });
        return true;
      }
      return false;
    }, 'IPC:create-tab')
  );

  /**
   * <PERSON><PERSON> requests to close a tab.
   * Sends a 'tabs-changed' event to the renderer process with action 'close'.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} tabId - The ID of the tab to close.
   * @returns {boolean} - True if the event was sent, false otherwise.
   */
  ipcMain.handle(
    'close-tab',
    errorHandler.wrapSync((event, tabId) => {
      // Send tab close event to the renderer
      if (mainWindowInstance && !mainWindowInstance.isDestroyed()) {
        mainWindowInstance.webContents.send('tabs-changed', { action: 'close', tabId });
        return true;
      }
      return false;
    }, 'IPC:close-tab')
  );

  /**
   * Handles requests to switch to a specific tab.
   * Sends a 'tabs-changed' event to the renderer process with action 'switch'.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} tabId - The ID of the tab to switch to.
   * @returns {boolean} - True if the event was sent, false otherwise.
   */
  ipcMain.handle(
    'switch-tab',
    errorHandler.wrapSync((event, tabId) => {
      // Send tab switch event to the renderer
      if (mainWindowInstance && !mainWindowInstance.isDestroyed()) {
        mainWindowInstance.webContents.send('tabs-changed', { action: 'switch', tabId });
        return true;
      }
      return false;
    }, 'IPC:switch-tab')
  );

  /**
   * Handles requests to get the list of tabs.
   * Note: Getting the list of tabs happens on the renderer side.
   * This handler can be extended in the future for tab synchronization between windows.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @returns {Array<object>} - An empty array, as tab data is managed in the renderer process.
   */
  ipcMain.handle(
    'get-tabs',
    errorHandler.wrapSync(event => {
      // Getting the list of tabs happens on the renderer side
      // This handler can be extended in the future for tab synchronization between windows
      return [];
    }, 'IPC:get-tabs')
  );
}

module.exports = { initializeTabIPC };
