const { ipcMain } = require('electron');

const { errorManager } = require('../../core/ErrorManager');

let mainWindowInstance = null;

/**
 * Initializes IPC handlers for navigation-related operations.
 * @param {Electron.BrowserWindow} mainWindow - The main browser window instance.
 */
function initializeNavigationIPC(mainWindow) {
  mainWindowInstance = mainWindow;

  /**
   * <PERSON>les requests to navigate the main window to a specified URL.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} url - The URL to navigate to.
   * @returns {boolean} - True if navigation was attempted, false otherwise.
   */
  ipcMain.handle(
    'navigate',
    errorHandler.wrapSync((event, url) => {
      if (mainWindowInstance && !mainWindowInstance.isDestroyed()) {
        mainWindowInstance.webContents.loadURL(url);
        return true;
      }
      return false;
    }, 'IPC:navigate')
  );

  /**
   * <PERSON>les requests to go back in the main window's history.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @returns {boolean} - True if navigation back was attempted, false otherwise.
   */
  ipcMain.handle(
    'go-back',
    errorHandler.wrapSync(event => {
      if (
        mainWindowInstance &&
        !mainWindowInstance.isDestroyed() &&
        mainWindowInstance.webContents.canGoBack()
      ) {
        mainWindowInstance.webContents.goBack();
        return true;
      }
      return false;
    }, 'IPC:go-back')
  );

  /**
   * Handles requests to go forward in the main window's history.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @returns {boolean} - True if navigation forward was attempted, false otherwise.
   */
  ipcMain.handle(
    'go-forward',
    errorHandler.wrapSync(event => {
      if (
        mainWindowInstance &&
        !mainWindowInstance.isDestroyed() &&
        mainWindowInstance.webContents.canGoForward()
      ) {
        mainWindowInstance.webContents.goForward();
        return true;
      }
      return false;
    }, 'IPC:go-forward')
  );

  /**
   * Handles requests to reload the current page in the main window.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @returns {boolean} - True if reload was attempted, false otherwise.
   */
  ipcMain.handle(
    'reload',
    errorHandler.wrapSync(event => {
      if (mainWindowInstance && !mainWindowInstance.isDestroyed()) {
        mainWindowInstance.webContents.reload();
        return true;
      }
      return false;
    }, 'IPC:reload')
  );
}

module.exports = { initializeNavigationIPC };
