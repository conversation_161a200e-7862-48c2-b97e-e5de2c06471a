import { app, ipcMain } from 'electron';
import path from 'path';
import { promises as fs } from 'fs';

const HISTORY_FILE = 'history.json';
const MAX_HISTORY_ITEMS = 1000; // Limit the number of items to prevent the file from growing indefinitely

export interface HistoryItem {
  id: string; // Using timestamp + random for a unique ID
  url: string;
  title: string;
  timestamp: number;
}

function getHistoryFilePath(): string {
  return path.join(app.getPath('userData'), HISTORY_FILE);
}

async function readHistory(): Promise<HistoryItem[]> {
  const filePath = getHistoryFilePath();
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    const history = JSON.parse(data);
    return Array.isArray(history) ? history : [];
  } catch (error) {
    if (error.code === 'ENOENT') {
      return [];
    }
    console.error('Error reading history file:', error);
    return [];
  }
}

async function writeHistory(history: HistoryItem[]): Promise<void> {
  const filePath = getHistoryFilePath();
  try {
    // Sort by timestamp descending and limit the size
    const sortedAndTrimmedHistory = history.sort((a, b) => b.timestamp - a.timestamp).slice(0, MAX_HISTORY_ITEMS);
    await fs.writeFile(filePath, JSON.stringify(sortedAndTrimmedHistory, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing history file:', error);
  }
}

export function setupHistoryHandlers(): void {
  ipcMain.handle('get-history', async () => await readHistory());

  ipcMain.handle('add-history-item', async (_event, item: Omit<HistoryItem, 'id'>) => {
    const history = await readHistory();
    const newHistoryItem: HistoryItem = { ...item, id: `${item.timestamp}-${Math.random()}` };
    await writeHistory([newHistoryItem, ...history]);
    return newHistoryItem;
  });

  ipcMain.handle('clear-history', async () => {
    await writeHistory([]);
    return true;
  });

  ipcMain.handle('clear-history-range', async (_event, sinceTimestamp: number) => {
    const history = await readHistory();
    // Keep items that are OLDER than the timestamp
    const filteredHistory = history.filter(item => item.timestamp < sinceTimestamp);
    await writeHistory(filteredHistory);
    return true;
  });
}