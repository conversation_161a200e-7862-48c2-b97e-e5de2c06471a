/**
 * Unified Validator
 * Consolidates all validation functionality from validators.ts, validation.ts, and securityUtils.ts
 */

import { z } from 'zod';
import { EventEmitter } from 'events';
import LRUCache from 'lru-cache';

import { logger } from '../logging/Logger';
import { errorManager } from '../core/ErrorManager';

// Enhanced validation types combining all validators
export type ValidationType =
  | 'required'
  | 'string'
  | 'number'
  | 'boolean'
  | 'date'
  | 'email'
  | 'url'
  | 'pattern'
  | 'min'
  | 'max'
  | 'range'
  | 'length'
  | 'custom';

export type ValidationSeverity = 'error' | 'warning' | 'info';

export interface ValidationRule {
  type: ValidationType;
  severity: ValidationSeverity;
  message: string;
  params?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface ValidationConfig {
  id: string;
  name: string;
  rules: ValidationRule[];
  stopOnFirstError: boolean;
  metadata: Record<string, any>;
}

export interface ValidationSchema {
  type: string;
  required?: boolean;
  rules?: Record<string, any>;
  properties?: Record<string, ValidationSchema>;
  items?: ValidationSchema;
  metadata?: Record<string, any>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  data?: any;
  metadata?: Record<string, any>;
}

export interface ValidationError extends Error {
  details: Array<{
    type: string;
    message: string;
    context?: Record<string, any>;
  }>;
}

export class UnifiedValidator extends EventEmitter {
  private static instance: UnifiedValidator;
  private validators: Map<string, z.ZodType<any>>;
  private schemas: Map<string, ValidationSchema>;
  private configs: Map<string, ValidationConfig>;
  private cache: LRUCache<string, ValidationResult>;

  private constructor() {
    super();
    this.validators = new Map();
    this.schemas = new Map();
    this.configs = new Map();
    this.cache = new LRUCache<string, ValidationResult>({
      max: 1000,
      ttl: 60 * 60 * 1000, // 1 hour
    });
    this.initializeValidators();
    this.initializeSchemas();
  }

  public static getInstance(): UnifiedValidator {
    if (!UnifiedValidator.instance) {
      UnifiedValidator.instance = new UnifiedValidator();
    }
    return UnifiedValidator.instance;
  }

  private initializeValidators(): void {
    // User validators
    this.validators.set(
      'user',
      z.object({
        id: z.string().uuid(),
        email: z.string().email(),
        username: z.string().min(3).max(50),
        role: z.enum(['admin', 'user', 'guest']),
        permissions: z.array(z.string()),
        settings: z.record(z.unknown()),
        createdAt: z.date(),
        updatedAt: z.date(),
      })
    );

    // Security validators
    this.validators.set(
      'security',
      z.object({
        encryption: z.object({
          algorithm: z.string(),
          keySize: z.number().min(128).max(512),
          iterations: z.number().min(1000),
        }),
        jwt: z.object({
          secret: z.string().min(32),
          expiresIn: z.string(),
          refreshExpiresIn: z.string(),
        }),
      })
    );

    // Performance validators
    this.validators.set(
      'performance',
      z.object({
        cache: z.object({
          enabled: z.boolean(),
          ttl: z.number().min(0),
          maxSize: z.number().min(1),
        }),
        compression: z.object({
          enabled: z.boolean(),
          level: z.number().min(0).max(9),
        }),
      })
    );

    // Analytics validators
    this.validators.set(
      'analytics',
      z.object({
        enabled: z.boolean(),
        providers: z.array(z.string()),
        events: z.array(z.string()),
      })
    );

    // Accessibility validators
    this.validators.set(
      'accessibility',
      z.object({
        enabled: z.boolean(),
        features: z.array(z.string()),
        compliance: z.array(z.string()),
      })
    );

    // Extension validators
    this.validators.set(
      'extension',
      z.object({
        id: z.string().regex(/^[a-z0-9-]+$/),
        name: z.string().min(1).max(50),
        version: z.string().regex(/^\d+\.\d+\.\d+$/),
        description: z.string().min(10).max(500),
        author: z.string(),
        compatibility: z.object({
          minVersion: z.string(),
          maxVersion: z.string(),
        }),
        license: z.string(),
      })
    );

    // Form validators
    this.validators.set(
      'form',
      z.object({
        email: z.string().email(),
        password: z.string().min(8).max(128),
        confirmPassword: z.string(),
        firstName: z.string().min(1).max(50),
        lastName: z.string().min(1).max(50),
        phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/),
        url: z.string().url(),
      })
    );

    // API validators
    this.validators.set(
      'api',
      z.object({
        method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
        url: z.string().url(),
        headers: z.record(z.string()),
        body: z.unknown().optional(),
        timeout: z.number().min(0).max(300000),
      })
    );
  }

  private initializeSchemas(): void {
    // User schema
    this.schemas.set('user', {
      type: 'object',
      required: true,
      properties: {
        id: { type: 'string', required: true },
        email: { type: 'string', required: true },
        username: { type: 'string', required: true },
        role: { type: 'string', required: true },
      },
    });

    // Extension schema
    this.schemas.set('extension', {
      type: 'object',
      required: true,
      properties: {
        id: { type: 'string', required: true },
        name: { type: 'string', required: true },
        version: { type: 'string', required: true },
        description: { type: 'string', required: true },
      },
    });
  }

  // Main validation method
  public validate<T>(type: string, data: unknown): ValidationResult {
    const cacheKey = `${type}:${JSON.stringify(data)}`;
    const cached = this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    const validator = this.validators.get(type);
    if (!validator) {
      const result: ValidationResult = {
        isValid: false,
        errors: [`No validator found for type: ${type}`],
        warnings: [],
      };
      this.cache.set(cacheKey, result);
      return result;
    }

    try {
      const parseResult = validator.safeParse(data);
      const result: ValidationResult = {
        isValid: parseResult.success,
        errors: parseResult.success ? [] : parseResult.error.errors.map(err => err.message),
        warnings: [],
        data: parseResult.success ? parseResult.data : undefined,
      };

      this.cache.set(cacheKey, result);
      this.emit('validation-completed', { type, result });
      
      if (!result.isValid) {
        logger.warn('Validation failed', { type, errors: result.errors });
      }

      return result;
    } catch (error) {
      const result: ValidationResult = {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error'],
        warnings: [],
      };
      
      this.cache.set(cacheKey, result);
      errorManager.handleError(error as Error, 'error', 'validation');
      return result;
    }
  }

  // Enhanced validation with recovery
  public async validateWithRecovery<T>(type: string, data: T): Promise<T> {
    try {
      const validator = this.validators.get(type);
      if (!validator) {
        throw new Error(`No validator found for type: ${type}`);
      }

      const result = await validator.parseAsync(data);
      return result;
    } catch (error) {
      if (this.isValidationError(error)) {
        const errorKey = `${error.details[0].type}:${error.details[0].context?.key}`;
        const cached = this.cache.get(errorKey);
        return cached?.data ?? this.applyFallbackStrategy(error);
      }
      throw error;
    }
  }

  private isValidationError(error: any): error is ValidationError {
    return error && Array.isArray(error.details);
  }

  private applyFallbackStrategy(error: ValidationError): any {
    logger.warn('Applying validation fallback for:', error.message);
    return { __fallback: true, timestamp: Date.now() };
  }

  // Schema validation
  public validateSchema(schemaName: string, data: unknown): ValidationResult {
    const schema = this.schemas.get(schemaName);
    if (!schema) {
      return {
        isValid: false,
        errors: [`No schema found: ${schemaName}`],
        warnings: [],
      };
    }

    return this.validateAgainstSchema(schema, data);
  }

  private validateAgainstSchema(schema: ValidationSchema, data: unknown): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (schema.required && (data === null || data === undefined)) {
      errors.push('Value is required');
    }

    if (schema.type === 'object' && typeof data === 'object' && data !== null) {
      if (schema.properties) {
        for (const [key, propSchema] of Object.entries(schema.properties)) {
          const propData = (data as any)[key];
          const propResult = this.validateAgainstSchema(propSchema, propData);
          errors.push(...propResult.errors.map(err => `${key}: ${err}`));
          warnings.push(...propResult.warnings.map(warn => `${key}: ${warn}`));
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // Utility validation methods
  public validateEmail(email: string): boolean {
    const emailSchema = z.string().email();
    try {
      emailSchema.parse(email);
      return true;
    } catch {
      return false;
    }
  }

  public validateUrl(url: string): boolean {
    const urlSchema = z.string().url();
    try {
      urlSchema.parse(url);
      return true;
    } catch {
      return false;
    }
  }

  public validatePassword(password: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }
    if (!/[A-Z]/.test(password)) {
      warnings.push('Password should contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      warnings.push('Password should contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
      warnings.push('Password should contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      warnings.push('Password should contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // Management methods
  public addValidator(type: string, schema: z.ZodType<any>): void {
    this.validators.set(type, schema);
    this.emit('validator-added', { type });
  }

  public removeValidator(type: string): void {
    this.validators.delete(type);
    this.emit('validator-removed', { type });
  }

  public getValidator(type: string): z.ZodType<any> | undefined {
    return this.validators.get(type);
  }

  public addSchema(name: string, schema: ValidationSchema): void {
    this.schemas.set(name, schema);
    this.emit('schema-added', { name });
  }

  public removeSchema(name: string): void {
    this.schemas.delete(name);
    this.emit('schema-removed', { name });
  }

  public getSchema(name: string): ValidationSchema | undefined {
    return this.schemas.get(name);
  }

  public clearCache(): void {
    this.cache.clear();
    this.emit('cache-cleared');
  }

  public getValidators(): string[] {
    return Array.from(this.validators.keys());
  }

  public getSchemas(): string[] {
    return Array.from(this.schemas.keys());
  }

  public cleanup(): void {
    this.validators.clear();
    this.schemas.clear();
    this.configs.clear();
    this.cache.clear();
    this.removeAllListeners();
  }
}

// Create singleton instance
export const validator = UnifiedValidator.getInstance();

// Legacy exports for backward compatibility
export const validateEmail = (email: string): boolean => validator.validateEmail(email);
export const validateUrl = (url: string): boolean => validator.validateUrl(url);
export const validatePassword = (password: string): ValidationResult => validator.validatePassword(password);

// Export class for direct usage
export { UnifiedValidator as Validator };
