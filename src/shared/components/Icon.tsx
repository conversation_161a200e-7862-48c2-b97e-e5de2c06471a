import React from 'react';

import styles from './Icon.module.css';

interface IconProps {
  name: string;
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export const Icon: React.FC<IconProps> = ({ name, size = 'medium', color }) => {
  return (
    <span
      className={`${styles.icon} ${styles[size]}`}
      style={{ color }}
      role="img"
      aria-label={name}
    >
      {getIconSvg(name)}
    </span>
  );
};

const getIconSvg = (name: string): string => {
  switch (name) {
    case 'close':
      return '×';
    case 'check':
      return '✓';
    case 'warning':
      return '⚠';
    case 'error':
      return '✕';
    case 'info':
      return 'ℹ';
    case 'settings':
      return '⚙';
    case 'menu':
      return '☰';
    case 'search':
      return '🔍';
    case 'refresh':
      return '↻';
    case 'back':
      return '←';
    case 'forward':
      return '→';
    case 'home':
      return '⌂';
    case 'bookmark':
      return '★';
    case 'download':
      return '↓';
    case 'upload':
      return '↑';
    case 'share':
      return '↗';
    case 'more':
      return '⋮';
    default:
      return '';
  }
};
