import protobuf from 'protobufjs';

import { lruCache } from '../utils/cache';

const CACHE_SIZE = 100;
const schemaCache = lruCache(CACHE_SIZE);

const protoDefinition = `
syntax = "proto3";

message ExtensionMessage {
  string extensionId = 1;
  bytes payload = 2;
  map<string, string> metadata = 3;
  int64 timestamp = 4;
}
`;

const schemaCache = new Map();

export const serializer = {
  serialize: async data => {
    const cacheKey = JSON.stringify(data.structure);

    if (!schemaCache.has(cacheKey)) {
      schemaCache.set(cacheKey, this._compileSchema(data.structure));
    }

    return Buffer.from(
      JSON.stringify({
        ...data,
        _cachedSchema: cacheKey,
      })
    );
  },

  deserialize: async buffer => {
    const parsed = JSON.parse(buffer.toString());
    if (schemaCache.has(parsed._cachedSchema)) {
      return schemaCache.get(parsed._cachedSchema).decode(parsed);
    }
    return parsed;
  },

  invalidateCache: key => {
    schemaCache.delete(key);
  },

  _compileSchema: structure => {
    // Schema compilation logic
    return { decode: data => data };
  },
};
