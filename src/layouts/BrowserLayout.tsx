import { App<PERSON><PERSON>, <PERSON>, IconButton, <PERSON>Field, Tool<PERSON>, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { RootState } from '@store';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Icon } from '@shared/components/Icon';
import { setUrl } from '@store/slices/browserSlice';

import styles from './BrowserLayout.module.css';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  boxShadow: 'none',
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledToolbar = styled(Toolbar)(({ theme }) => ({
  minHeight: 48,
  padding: theme.spacing(0, 2),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  flex: 1,
  margin: theme.spacing(0, 2),
  '& .MuiOutlinedInput-root': {
    backgroundColor: theme.palette.background.default,
    borderRadius: theme.spacing(2),
    '& fieldset': {
      borderColor: theme.palette.divider,
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
}));

const BrowserLayout: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { url, title, isLoading } = useSelector((state: RootState) => state.browser);

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setUrl(event.target.value));
  };

  const handleUrlSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    // TODO: Implement navigation
  };

  const handleBack = () => {
    navigate(-1);
  };

  const handleForward = () => {
    navigate(1);
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleHome = () => {
    navigate('/');
  };

  return (
    <Box className={styles.container}>
      <StyledAppBar position="static">
        <StyledToolbar>
          <IconButton onClick={handleBack} size="small">
            <Icon name="arrow_back" />
          </IconButton>
          <IconButton onClick={handleForward} size="small">
            <Icon name="arrow_forward" />
          </IconButton>
          <IconButton onClick={handleRefresh} size="small">
            <Icon name="refresh" />
          </IconButton>
          <IconButton onClick={handleHome} size="small">
            <Icon name="home" />
          </IconButton>
          <form onSubmit={handleUrlSubmit} className={styles.urlForm}>
            <StyledTextField
              value={url}
              onChange={handleUrlChange}
              placeholder={t('browser.urlPlaceholder')}
              variant="outlined"
              size="small"
              fullWidth
              InputProps={{
                startAdornment: isLoading && <Icon name="sync" className={styles.loadingIcon} />,
              }}
            />
          </form>
          <IconButton size="small">
            <Icon name="more_vert" />
          </IconButton>
        </StyledToolbar>
      </StyledAppBar>
      <Box className={styles.content}>
        <Typography variant="h6" className={styles.title}>
          {title}
        </Typography>
        {/* TODO: Add browser content */}
      </Box>
    </Box>
  );
};

export default BrowserLayout;
