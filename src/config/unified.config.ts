import { join } from 'path';
import { z } from 'zod';

// Environment schema for validation
const envSchema = z.object({
  API_URL: z.string().url().optional(),
  DEBUG_MODE: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  SENTRY_DSN: z.string().optional(),
  ENVIRONMENT: z.enum(['development', 'production', 'staging', 'test']).optional(),
  JWT_SECRET: z.string().optional(),
  PORT: z.string().transform(val => parseInt(val, 10)).optional(),
  HOST: z.string().optional(),
});

export type EnvConfig = z.infer<typeof envSchema>;

// Validate environment variables
export const envConfig = envSchema.parse({
  API_URL: process.env.VITE_API_URL || process.env.API_URL || 'http://localhost:3000',
  DEBUG_MODE: process.env.VITE_DEBUG_MODE || process.env.DEBUG_MODE || 'false',
  SENTRY_DSN: process.env.VITE_SENTRY_DSN || process.env.SENTRY_DSN,
  ENVIRONMENT: process.env.NODE_ENV || 'development',
  JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',
  PORT: process.env.PORT || '3000',
  HOST: process.env.HOST || 'localhost',
});

const isDev = envConfig.ENVIRONMENT === 'development';
const isProd = envConfig.ENVIRONMENT === 'production';
const isTest = envConfig.ENVIRONMENT === 'test';

// Unified configuration schema
export const AppConfigSchema = z.object({
  app: z.object({
    name: z.string(),
    version: z.string(),
    description: z.string(),
    author: z.string(),
    homepage: z.string().optional(),
    repository: z.string().optional(),
    license: z.string(),
    environment: z.enum(['development', 'production', 'staging', 'test']),
    debug: z.boolean(),
    logLevel: z.enum(['error', 'warn', 'info', 'debug']),
  }),
  api: z.object({
    baseUrl: z.string().url(),
    timeout: z.number(),
    retryAttempts: z.number(),
    retryDelay: z.number(),
  }),
  security: z.object({
    encryption: z.object({
      algorithm: z.string(),
      keySize: z.number(),
      iterations: z.number(),
    }),
    jwt: z.object({
      secret: z.string(),
      expiresIn: z.string(),
      refreshExpiresIn: z.string(),
    }),
    sandbox: z.boolean(),
    contextIsolation: z.boolean(),
    nodeIntegration: z.boolean(),
    webSecurity: z.boolean(),
    allowRunningInsecureContent: z.boolean(),
    enableRemoteModule: z.boolean(),
  }),
  browser: z.object({
    defaultUserAgent: z.string(),
    enableDevTools: z.boolean(),
    enableWebGL: z.boolean(),
    enableWebGL2: z.boolean(),
    enableWebGPU: z.boolean(),
    enableWebAssembly: z.boolean(),
    enableSharedArrayBuffer: z.boolean(),
    enableWebWorkers: z.boolean(),
    enableServiceWorkers: z.boolean(),
    enableBackgroundThrottling: z.boolean(),
    enableHardwareAcceleration: z.boolean(),
    maxMemoryUsage: z.number(),
    maxConcurrentDownloads: z.number(),
    cacheSize: z.number(),
  }),
  window: z.object({
    width: z.number(),
    height: z.number(),
    minWidth: z.number(),
    minHeight: z.number(),
    resizable: z.boolean(),
    maximizable: z.boolean(),
    minimizable: z.boolean(),
    closable: z.boolean(),
    alwaysOnTop: z.boolean(),
    fullscreenable: z.boolean(),
    skipTaskbar: z.boolean(),
    kiosk: z.boolean(),
    title: z.string(),
    icon: z.string().optional(),
    show: z.boolean(),
    frame: z.boolean(),
    transparent: z.boolean(),
    hasShadow: z.boolean(),
    opacity: z.number(),
    vibrancy: z.string().optional(),
  }),
  privacy: z.object({
    doNotTrack: z.boolean(),
    blockAds: z.boolean(),
    blockTrackers: z.boolean(),
    blockFingerprinting: z.boolean(),
    blockWebRTC: z.boolean(),
    clearOnExit: z.boolean(),
  }),
  sync: z.object({
    enabled: z.boolean(),
    interval: z.number(),
    maxRetries: z.number(),
    timeout: z.number(),
  }),
  updates: z.object({
    autoUpdate: z.boolean(),
    checkInterval: z.number(),
    channel: z.enum(['stable', 'beta', 'nightly']),
  }),
  logging: z.object({
    level: z.enum(['error', 'warn', 'info', 'debug']),
    maxSize: z.number(),
    maxFiles: z.number(),
  }),
  paths: z.object({
    root: z.string(),
    src: z.string(),
    dist: z.string(),
    assets: z.string(),
    locales: z.string(),
    extensions: z.string(),
    userData: z.string(),
    logs: z.string(),
    temp: z.string(),
  }),
  errorReporting: z.object({
    enabled: z.boolean(),
    dsn: z.string().optional(),
    environment: z.string(),
  }),
});

export type AppConfig = z.infer<typeof AppConfigSchema>;

// Default configuration
export const defaultConfig: AppConfig = {
  app: {
    name: 'A14-Browser',
    version: process.env.npm_package_version || '1.0.0',
    description: process.env.npm_package_description || 'Advanced Browser Application',
    author: process.env.npm_package_author || 'A14 Team',
    homepage: process.env.npm_package_homepage || '',
    repository: process.env.npm_package_repository || '',
    license: process.env.npm_package_license || 'MIT',
    environment: envConfig.ENVIRONMENT || 'development',
    debug: envConfig.DEBUG_MODE || isDev,
    logLevel: isDev ? 'debug' : 'info',
  },
  api: {
    baseUrl: envConfig.API_URL || 'http://localhost:3000',
    timeout: 5000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  security: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keySize: 32,
      iterations: 100000,
    },
    jwt: {
      secret: envConfig.JWT_SECRET || 'your-secret-key',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    },
    sandbox: true,
    contextIsolation: true,
    nodeIntegration: false,
    webSecurity: true,
    allowRunningInsecureContent: false,
    enableRemoteModule: false,
  },
  browser: {
    defaultUserAgent: 'A14-Browser/1.0.0',
    enableDevTools: isDev,
    enableWebGL: true,
    enableWebGL2: true,
    enableWebGPU: false,
    enableWebAssembly: true,
    enableSharedArrayBuffer: false,
    enableWebWorkers: true,
    enableServiceWorkers: true,
    enableBackgroundThrottling: true,
    enableHardwareAcceleration: true,
    maxMemoryUsage: 2048,
    maxConcurrentDownloads: 6,
    cacheSize: 100,
  },
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    alwaysOnTop: false,
    fullscreenable: true,
    skipTaskbar: false,
    kiosk: false,
    title: 'A14-Browser',
    icon: undefined,
    show: true,
    frame: true,
    transparent: false,
    hasShadow: true,
    opacity: 1.0,
    vibrancy: undefined,
  },
  privacy: {
    doNotTrack: true,
    blockAds: false,
    blockTrackers: false,
    blockFingerprinting: false,
    blockWebRTC: false,
    clearOnExit: false,
  },
  sync: {
    enabled: false,
    interval: 300000,
    maxRetries: 3,
    timeout: 10000,
  },
  updates: {
    autoUpdate: true,
    checkInterval: 86400000,
    channel: 'stable',
  },
  logging: {
    level: isDev ? 'debug' : 'info',
    maxSize: 10485760,
    maxFiles: 5,
  },
  paths: {
    root: process.cwd(),
    src: join(process.cwd(), 'src'),
    dist: join(process.cwd(), 'dist'),
    assets: join(process.cwd(), 'src/assets'),
    locales: join(process.cwd(), 'src/i18n/locales'),
    extensions: join(process.cwd(), 'extensions'),
    userData: join(process.cwd(), 'userData'),
    logs: join(process.cwd(), 'logs'),
    temp: join(process.cwd(), 'temp'),
  },
  errorReporting: {
    enabled: !!envConfig.SENTRY_DSN,
    dsn: envConfig.SENTRY_DSN,
    environment: envConfig.ENVIRONMENT || 'development',
  },
};

// Validate and export the configuration
export const config = AppConfigSchema.parse(defaultConfig);

// Export legacy interfaces for backward compatibility
export const APP_CONFIG = config;

// Environment helpers
export const isProduction = isProd;
export const isDevelopment = isDev;
export const isTesting = isTest;

// Configuration utilities
export function getConfig(): AppConfig {
  return config;
}

export function updateConfig(updates: Partial<AppConfig>): AppConfig {
  const updatedConfig = { ...config, ...updates };
  return AppConfigSchema.parse(updatedConfig);
}

export function validateConfig(configToValidate: unknown): AppConfig {
  return AppConfigSchema.parse(configToValidate);
}
