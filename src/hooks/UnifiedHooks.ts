/**
 * Unified Hooks
 * Consolidates all React hooks from different files into one comprehensive module
 */

import { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { logger } from '../logging/Logger';
import { errorManager } from '../core/ErrorManager';

// Re-export existing useDebounce
export { useDebounce, useSimpleDebounce, type UseDebounceOptions, type UseDebounceReturn } from './useDebounce';

// ============================================================================
// STORAGE HOOKS
// ============================================================================

export interface UseStorageOptions {
  serializer?: {
    parse: (value: string) => any;
    stringify: (value: any) => string;
  };
  syncAcrossTabs?: boolean;
  onError?: (error: Error) => void;
}

export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: UseStorageOptions = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  const {
    serializer = JSON,
    syncAcrossTabs = true,
    onError = (error) => errorManager.handleError(error, 'error', 'storage'),
  } = options;

  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window === 'undefined') return defaultValue;
      
      const item = window.localStorage.getItem(key);
      return item ? serializer.parse(item) : defaultValue;
    } catch (error) {
      onError(error as Error);
      return defaultValue;
    }
  });

  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, serializer.stringify(valueToStore));
        }
      } catch (error) {
        onError(error as Error);
      }
    },
    [key, storedValue, serializer, onError]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(defaultValue);
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      onError(error as Error);
    }
  }, [key, defaultValue, onError]);

  // Sync across tabs
  useEffect(() => {
    if (!syncAcrossTabs || typeof window === 'undefined') return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(serializer.parse(e.newValue));
        } catch (error) {
          onError(error as Error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, serializer, onError, syncAcrossTabs]);

  return [storedValue, setValue, removeValue];
}

export function useSessionStorage<T>(
  key: string,
  defaultValue: T,
  options: UseStorageOptions = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  const {
    serializer = JSON,
    onError = (error) => errorManager.handleError(error, 'error', 'storage'),
  } = options;

  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window === 'undefined') return defaultValue;
      
      const item = window.sessionStorage.getItem(key);
      return item ? serializer.parse(item) : defaultValue;
    } catch (error) {
      onError(error as Error);
      return defaultValue;
    }
  });

  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        
        if (typeof window !== 'undefined') {
          window.sessionStorage.setItem(key, serializer.stringify(valueToStore));
        }
      } catch (error) {
        onError(error as Error);
      }
    },
    [key, storedValue, serializer, onError]
  );

  const removeValue = useCallback(() => {
    try {
      setStoredValue(defaultValue);
      if (typeof window !== 'undefined') {
        window.sessionStorage.removeItem(key);
      }
    } catch (error) {
      onError(error as Error);
    }
  }, [key, defaultValue, onError]);

  return [storedValue, setValue, removeValue];
}

// ============================================================================
// API HOOKS
// ============================================================================

export interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  retries?: number;
  retryDelay?: number;
}

export interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
}

export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
): UseApiReturn<T> {
  const {
    immediate = false,
    onSuccess,
    onError,
    retries = 0,
    retryDelay = 1000,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      setLoading(true);
      setError(null);

      let lastError: Error;
      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const result = await apiFunction(...args);
          setData(result);
          setLoading(false);
          onSuccess?.(result);
          return result;
        } catch (err) {
          lastError = err as Error;
          if (attempt < retries) {
            await new Promise(resolve => setTimeout(resolve, retryDelay));
          }
        }
      }

      setError(lastError!);
      setLoading(false);
      onError?.(lastError!);
      throw lastError!;
    },
    [apiFunction, retries, retryDelay, onSuccess, onError]
  );

  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    setError(null);
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return { data, loading, error, execute, reset };
}

// ============================================================================
// FORM HOOKS
// ============================================================================

export interface UseFormOptions<T> {
  initialValues: T;
  validate?: (values: T) => Record<string, string>;
  onSubmit?: (values: T) => void | Promise<void>;
}

export interface UseFormReturn<T> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
  handleChange: (name: keyof T) => (value: any) => void;
  handleBlur: (name: keyof T) => () => void;
  handleSubmit: (e?: React.FormEvent) => void;
  setFieldValue: (name: keyof T, value: any) => void;
  setFieldError: (name: keyof T, error: string) => void;
  resetForm: () => void;
}

export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T>
): UseFormReturn<T> {
  const { initialValues, validate, onSubmit } = options;

  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);

  const handleChange = useCallback(
    (name: keyof T) => (value: any) => {
      setValues(prev => ({ ...prev, [name]: value }));
      
      // Clear error when user starts typing
      if (errors[name as string]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name as string];
          return newErrors;
        });
      }
    },
    [errors]
  );

  const handleBlur = useCallback(
    (name: keyof T) => () => {
      setTouched(prev => ({ ...prev, [name as string]: true }));
      
      // Validate field on blur
      if (validate) {
        const fieldErrors = validate(values);
        if (fieldErrors[name as string]) {
          setErrors(prev => ({ ...prev, [name as string]: fieldErrors[name as string] }));
        }
      }
    },
    [values, validate]
  );

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      
      if (validate) {
        const formErrors = validate(values);
        setErrors(formErrors);
        
        if (Object.keys(formErrors).length > 0) {
          return;
        }
      }

      setIsSubmitting(true);
      try {
        await onSubmit?.(values);
      } catch (error) {
        logger.error('Form submission failed:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [values, validate, onSubmit]
  );

  const setFieldValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }));
  }, []);

  const setFieldError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name as string]: error }));
  }, []);

  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
    resetForm,
  };
}

// ============================================================================
// THEME HOOKS
// ============================================================================

export type Theme = 'light' | 'dark' | 'system';

export interface UseThemeReturn {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
}

export function useTheme(): UseThemeReturn {
  const [theme, setThemeState] = useLocalStorage<Theme>('theme', 'system');

  const setTheme = useCallback((newTheme: Theme) => {
    setThemeState(newTheme);
    
    // Apply theme to document
    const root = document.documentElement;
    if (newTheme === 'system') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.classList.toggle('dark', prefersDark);
    } else {
      root.classList.toggle('dark', newTheme === 'dark');
    }
  }, [setThemeState]);

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  }, [theme, setTheme]);

  // Listen for system theme changes
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        document.documentElement.classList.toggle('dark', mediaQuery.matches);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      handleChange(); // Apply initial state
      
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  return {
    theme,
    setTheme,
    toggleTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
    isSystem: theme === 'system',
  };
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

export function useToggle(initialValue = false): [boolean, () => void, (value: boolean) => void] {
  const [value, setValue] = useState(initialValue);
  
  const toggle = useCallback(() => setValue(prev => !prev), []);
  const setToggle = useCallback((newValue: boolean) => setValue(newValue), []);
  
  return [value, toggle, setToggle];
}

export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value;
  });
  
  return ref.current;
}

export function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;

    const id = setInterval(() => savedCallback.current(), delay);
    return () => clearInterval(id);
  }, [delay]);
}

export function useTimeout(callback: () => void, delay: number | null) {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;

    const id = setTimeout(() => savedCallback.current(), delay);
    return () => clearTimeout(id);
  }, [delay]);
}

// ============================================================================
// CLICK OUTSIDE HOOKS (consolidated from useClickOutside.ts and useOnClickOutside.ts)
// ============================================================================

type ClickEvent = MouseEvent | TouchEvent;

export function useClickOutside<T extends HTMLElement = HTMLElement>(
  ref: RefObject<T>,
  handler: (event: ClickEvent) => void,
  mouseEvent: 'mousedown' | 'mouseup' = 'mousedown'
): void {
  useEffect(() => {
    const listener = (event: ClickEvent) => {
      const el = ref?.current;
      const target = event.target as Node;

      // Do nothing if clicking ref's element or descendent elements
      if (!el || el.contains(target)) {
        return;
      }

      handler(event);
    };

    document.addEventListener(mouseEvent, listener);
    document.addEventListener('touchstart', listener);

    return () => {
      document.removeEventListener(mouseEvent, listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler, mouseEvent]);
}

// ============================================================================
// KEY PRESS AND HOTKEY HOOKS (consolidated from useKeyPress.ts and useHotkeys.ts)
// ============================================================================

type KeyFilter = string | string[] | ((event: KeyboardEvent) => boolean);

export function useKeyPress(
  keyFilter: KeyFilter,
  options: {
    target?: Window | Document | HTMLElement;
    event?: 'keydown' | 'keyup' | 'keypress';
    exact?: boolean;
  } = {}
): boolean {
  const [pressed, setPressed] = useState(false);

  useEffect(() => {
    const target = options.target || window;
    const eventName = options.event || 'keydown';

    const downHandler = (event: KeyboardEvent) => {
      if (typeof keyFilter === 'function') {
        if (keyFilter(event)) {
          setPressed(true);
        }
      } else {
        const keys = Array.isArray(keyFilter) ? keyFilter : [keyFilter];
        const isExact = options.exact || false;

        if (isExact) {
          if (keys.includes(event.key)) {
            setPressed(true);
          }
        } else {
          if (keys.some(key => event.key.toLowerCase() === key.toLowerCase())) {
            setPressed(true);
          }
        }
      }
    };

    const upHandler = (event: KeyboardEvent) => {
      if (typeof keyFilter === 'function') {
        if (keyFilter(event)) {
          setPressed(false);
        }
      } else {
        const keys = Array.isArray(keyFilter) ? keyFilter : [keyFilter];
        const isExact = options.exact || false;

        if (isExact) {
          if (keys.includes(event.key)) {
            setPressed(false);
          }
        } else {
          if (keys.some(key => event.key.toLowerCase() === key.toLowerCase())) {
            setPressed(false);
          }
        }
      }
    };

    target.addEventListener(eventName, downHandler);
    target.addEventListener('keyup', upHandler);

    return () => {
      target.removeEventListener(eventName, downHandler);
      target.removeEventListener('keyup', upHandler);
    };
  }, [keyFilter, options.target, options.event, options.exact]);

  return pressed;
}

// Enhanced hotkeys hook with Redux integration
export interface Hotkey {
  id: string;
  key: string;
  enabled: boolean;
  action: () => void;
  description?: string;
}

export function useHotkeys(hotkeys: Hotkey[] = []) {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const pressedKey = event.key.toLowerCase();
      const isCtrlPressed = event.ctrlKey;
      const isShiftPressed = event.shiftKey;
      const isAltPressed = event.altKey;
      const isMetaPressed = event.metaKey;

      const keyCombination = [
        isCtrlPressed ? 'ctrl' : '',
        isShiftPressed ? 'shift' : '',
        isAltPressed ? 'alt' : '',
        isMetaPressed ? 'meta' : '',
        pressedKey,
      ]
        .filter(Boolean)
        .join('+')
        .toLowerCase();

      const matchingHotkey = hotkeys.find(
        hotkey =>
          hotkey.enabled &&
          hotkey.key.toLowerCase() === keyCombination
      );

      if (matchingHotkey) {
        event.preventDefault();
        matchingHotkey.action();
      }
    },
    [hotkeys]
  );

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    hotkeys,
    registerHotkey: (hotkey: Hotkey) => {
      // This would typically dispatch to a store
      logger.info('Hotkey registered:', hotkey.id);
    },
    unregisterHotkey: (id: string) => {
      // This would typically dispatch to a store
      logger.info('Hotkey unregistered:', id);
    },
  };
}

// ============================================================================
// VIEWPORT HOOKS (consolidated from multiple viewport hook files)
// ============================================================================

interface ViewportMargin {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

interface ViewportOptions {
  trackVisibility?: boolean;
  delay?: number;
}

export function useIsInViewport<T extends HTMLElement = HTMLElement>(
  options: {
    threshold?: number | number[];
    root?: Element | null;
    rootMargin?: string;
    margin?: ViewportMargin;
    thresholdPercentage?: number;
    rootMarginOptions?: ViewportMargin;
    trackVisibility?: boolean;
    delay?: number;
    callback?: (isInViewport: boolean, entry?: IntersectionObserverEntry) => void;
    debug?: boolean;
  } = {}
): [RefObject<T>, boolean] {
  const [isInViewport, setIsInViewport] = useState(false);
  const elementRef = useRef<T>(null);

  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = { top: 0, right: 0, bottom: 0, left: 0 },
    thresholdPercentage = 0,
    rootMarginOptions = { top: 0, right: 0, bottom: 0, left: 0 },
    trackVisibility = false,
    delay = 0,
    callback,
    debug = false,
  } = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const computedRootMargin = rootMargin ||
      `${rootMarginOptions.top}px ${rootMarginOptions.right}px ${rootMarginOptions.bottom}px ${rootMarginOptions.left}px`;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        const { isIntersecting, intersectionRect, boundingClientRect } = entry;

        let inViewport = isIntersecting;

        // Apply custom threshold percentage if specified
        if (thresholdPercentage > 0) {
          const elementArea = boundingClientRect.width * boundingClientRect.height;
          const intersectionArea = intersectionRect.width * intersectionRect.height;
          const intersectionPercentage = elementArea > 0 ? (intersectionArea / elementArea) * 100 : 0;
          inViewport = intersectionPercentage >= thresholdPercentage;
        }

        // Apply margin-based calculation if specified
        if (margin.top || margin.right || margin.bottom || margin.left) {
          const rect = element.getBoundingClientRect();
          const windowHeight = window.innerHeight || document.documentElement.clientHeight;
          const windowWidth = window.innerWidth || document.documentElement.clientWidth;

          inViewport = (
            rect.top >= -margin.top &&
            rect.left >= -margin.left &&
            rect.bottom <= windowHeight + margin.bottom &&
            rect.right <= windowWidth + margin.right
          );
        }

        if (debug) {
          logger.debug('Viewport intersection:', {
            isIntersecting,
            inViewport,
            thresholdPercentage,
            element: element.tagName,
          });
        }

        setIsInViewport(inViewport);
        callback?.(inViewport, entry);
      },
      {
        threshold,
        root,
        rootMargin: computedRootMargin,
        trackVisibility,
        delay,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, root, rootMargin, margin, thresholdPercentage, rootMarginOptions, trackVisibility, delay, callback, debug]);

  return [elementRef, isInViewport];
}

// Predefined viewport configurations
export const viewportMargins = {
  none: { top: 0, right: 0, bottom: 0, left: 0 },
  small: { top: 10, right: 10, bottom: 10, left: 10 },
  medium: { top: 20, right: 20, bottom: 20, left: 20 },
  large: { top: 50, right: 50, bottom: 50, left: 50 },
};

export const viewportThresholds = {
  none: 0,
  quarter: 0.25,
  half: 0.5,
  threeQuarters: 0.75,
  full: 1.0,
};

export const viewportOptions = {
  default: { trackVisibility: false, delay: 0 },
  delayed: { trackVisibility: false, delay: 100 },
  tracked: { trackVisibility: true, delay: 0 },
  trackedAndDelayed: { trackVisibility: true, delay: 100 },
};

// Legacy exports for backward compatibility
export const useFetch = useApi;
export const useStorage = useLocalStorage;
export const usePersistedState = useLocalStorage;
export const useOnClickOutside = useClickOutside;
