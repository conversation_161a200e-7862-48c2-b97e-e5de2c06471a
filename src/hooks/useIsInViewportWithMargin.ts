import { useEffect, useRef, useState } from 'react';

interface UseIsInViewportWithMarginProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
}

export function useIsInViewportWithMargin<T extends HTMLElement = HTMLElement>(
  options: UseIsInViewportWithMarginProps = {}
): [React.RefObject<T>, boolean] {
  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
  } = options;

  const [isInViewport, setIsInViewport] = useState(false);
  const elementRef = useRef<T>(null);

  useEffect(() => {
    const element = elementRef.current;

    if (!element) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        const rect = entry.boundingClientRect;
        const rootRect = root
          ? root.getBoundingClientRect()
          : {
              top: 0,
              right: window.innerWidth,
              bottom: window.innerHeight,
              left: 0,
            };

        const isInViewportWithMargin =
          rect.top - margin.top <= rootRect.bottom &&
          rect.right + margin.right >= rootRect.left &&
          rect.bottom + margin.bottom >= rootRect.top &&
          rect.left - margin.left <= rootRect.right;

        setIsInViewport(isInViewportWithMargin);
      },
      {
        threshold,
        root,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, root, rootMargin, margin]);

  return [elementRef, isInViewport];
}

// Common margins
export const margins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};
