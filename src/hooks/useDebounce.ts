import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * Enhanced debounce hook with multiple features
 * @param value The value to debounce
 * @param delay The debounce delay in milliseconds
 * @param options Additional options for debouncing
 * @returns The debounced value and utility functions
 */
export interface UseDebounceOptions {
  leading?: boolean; // Execute on the leading edge
  trailing?: boolean; // Execute on the trailing edge (default: true)
  maxWait?: number; // Maximum time to wait before executing
}

export interface UseDebounceReturn<T> {
  debouncedValue: T;
  cancel: () => void;
  flush: () => void;
  isPending: boolean;
}

export function useDebounce<T>(
  value: T,
  delay: number,
  options: UseDebounceOptions = {}
): UseDebounceReturn<T> {
  const { leading = false, trailing = true, maxWait } = options;

  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const [isPending, setIsPending] = useState(false);

  const timeoutRef = useRef<NodeJS.Timeout>();
  const maxTimeoutRef = useRef<NodeJS.Timeout>();
  const lastCallTimeRef = useRef<number>();
  const lastInvokeTimeRef = useRef<number>(0);
  const leadingRef = useRef<boolean>(true);

  const invokeFunc = useCallback((newValue: T) => {
    setDebouncedValue(newValue);
    setIsPending(false);
    lastInvokeTimeRef.current = Date.now();
  }, []);

  const leadingEdge = useCallback((newValue: T) => {
    lastInvokeTimeRef.current = Date.now();
    if (leading) {
      invokeFunc(newValue);
    }
  }, [leading, invokeFunc]);

  const remainingWait = useCallback((time: number) => {
    const timeSinceLastCall = time - (lastCallTimeRef.current || 0);
    const timeSinceLastInvoke = time - lastInvokeTimeRef.current;
    const timeWaiting = delay - timeSinceLastCall;

    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  }, [delay, maxWait]);

  const shouldInvoke = useCallback((time: number) => {
    const timeSinceLastCall = time - (lastCallTimeRef.current || 0);
    const timeSinceLastInvoke = time - lastInvokeTimeRef.current;

    return (
      lastCallTimeRef.current === undefined ||
      timeSinceLastCall >= delay ||
      timeSinceLastCall < 0 ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    );
  }, [delay, maxWait]);

  const timerExpired = useCallback((newValue: T) => {
    const time = Date.now();
    if (shouldInvoke(time)) {
      if (trailing) {
        invokeFunc(newValue);
      }
      return;
    }

    const remaining = remainingWait(time);
    timeoutRef.current = setTimeout(() => timerExpired(newValue), remaining);
  }, [shouldInvoke, trailing, invokeFunc, remainingWait]);

  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current);
      maxTimeoutRef.current = undefined;
    }
    lastInvokeTimeRef.current = 0;
    lastCallTimeRef.current = undefined;
    leadingRef.current = true;
    setIsPending(false);
  }, []);

  const flush = useCallback(() => {
    if (timeoutRef.current) {
      invokeFunc(value);
      cancel();
    }
  }, [value, invokeFunc, cancel]);

  useEffect(() => {
    const time = Date.now();
    const isInvoking = shouldInvoke(time);

    lastCallTimeRef.current = time;
    setIsPending(true);

    if (isInvoking) {
      if (timeoutRef.current === undefined) {
        leadingEdge(value);
        leadingRef.current = false;
      }

      if (maxWait !== undefined) {
        maxTimeoutRef.current = setTimeout(() => {
          if (trailing) {
            invokeFunc(value);
          }
        }, maxWait);
      }
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => timerExpired(value), delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current);
      }
    };
  }, [value, delay, shouldInvoke, leadingEdge, timerExpired, trailing, invokeFunc, maxWait]);

  // Cleanup on unmount
  useEffect(() => {
    return cancel;
  }, [cancel]);

  return {
    debouncedValue,
    cancel,
    flush,
    isPending,
  };
}

// Simple version for backward compatibility
export function useSimpleDebounce<T>(value: T, delay: number): T {
  const { debouncedValue } = useDebounce(value, delay);
  return debouncedValue;
}
