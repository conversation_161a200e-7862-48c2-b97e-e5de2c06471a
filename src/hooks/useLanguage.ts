import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import {
  LanguageCode,
  getLanguageDirection,
  getLanguageName,
  getNativeLanguageName,
  isRTL,
  setLanguage,
} from '../i18n/config';

export const useLanguage = () => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language as LanguageCode;

  const changeLanguage = useCallback(
    async (code: LanguageCode) => {
      if (code !== currentLanguage) {
        await setLanguage(code);
      }
    },
    [currentLanguage]
  );

  const getLanguageDisplayName = useCallback((code: LanguageCode) => {
    return `${getNativeLanguageName(code)} (${getLanguageName(code)})`;
  }, []);

  const getCurrentLanguageDisplayName = useCallback(() => {
    return getLanguageDisplayName(currentLanguage);
  }, [currentLanguage, getLanguageDisplayName]);

  const getCurrentLanguageDirection = useCallback(() => {
    return getLanguageDirection(currentLanguage);
  }, [currentLanguage]);

  const isCurrentLanguageRTL = useCallback(() => {
    return isRTL(currentLanguage);
  }, [currentLanguage]);

  return {
    currentLanguage,
    changeLanguage,
    getLanguageDisplayName,
    getCurrentLanguageDisplayName,
    getCurrentLanguageDirection,
    isCurrentLanguageRTL,
  };
};

export default useLanguage;
