export type SessionType = 'web' | 'mobile' | 'desktop' | 'api' | 'custom';
export type SessionStatus = 'active' | 'expired' | 'revoked' | 'archived';
export type SessionProvider = 'local' | 'oauth' | 'saml' | 'custom';

export interface SessionConfig {
  management: {
    enabled: boolean;
    types: SessionType[];
    providers: SessionProvider[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'redis' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  authentication: {
    enabled: boolean;
    methods?: {
      token?: boolean;
      cookie?: boolean;
      header?: boolean;
    };
    token?: {
      enabled: boolean;
      type: 'jwt' | 'opaque' | 'custom';
      config: Record<string, any>;
    };
    cookie?: {
      enabled: boolean;
      name: string;
      options: {
        httpOnly: boolean;
        secure: boolean;
        sameSite: 'strict' | 'lax' | 'none';
        path: string;
        domain?: string;
        maxAge?: number;
      };
    };
  };
  authorization: {
    enabled: boolean;
    scopes?: string[];
    permissions?: string[];
    policies?: {
      name: string;
      rules: Record<string, any>;
      description?: string;
    }[];
  };
  security: {
    enabled: boolean;
    features?: {
      csrf?: boolean;
      xss?: boolean;
      rateLimit?: boolean;
    };
    csrf?: {
      enabled: boolean;
      token: string;
      header: string;
    };
    xss?: {
      enabled: boolean;
      sanitize: boolean;
      escape: boolean;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      activity?: boolean;
      performance?: boolean;
      security?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    refresh?: boolean;
    revoke?: boolean;
    activity?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Session {
  id: string;
  config: SessionConfig;
  type: SessionType;
  status: SessionStatus;
  provider: SessionProvider;
  user: string;
  token: string;
  refreshToken?: string;
  device?: {
    id: string;
    name: string;
    type: string;
    os?: string;
    browser?: string;
    ip: string;
    location?: string;
  };
  scopes?: string[];
  permissions?: string[];
  activity?: {
    lastActive: Date;
    requests: number;
    errors: number;
  };
  security?: {
    csrf?: {
      token: string;
      verified: boolean;
    };
    xss?: {
      sanitized: boolean;
      escaped: boolean;
    };
    rateLimit?: {
      current: number;
      reset: Date;
    };
  };
  stats?: {
    duration: number;
    requests: number;
    errors: number;
  };
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
  metadata?: Record<string, any>;
}

export interface SessionLog {
  id: string;
  session: string;
  action: 'create' | 'update' | 'delete' | 'refresh' | 'revoke' | 'activity' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface SessionStats {
  total: number;
  byType: Record<SessionType, number>;
  byStatus: Record<SessionStatus, number>;
  byProvider: Record<SessionProvider, number>;
  activity: {
    active: {
      total: number;
      byType?: Record<SessionType, number>;
      byProvider?: Record<SessionProvider, number>;
    };
    requests: {
      total: number;
      bySession?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    errors: {
      total: number;
      bySession?: Record<string, number>;
      byDate?: Record<string, number>;
    };
  };
  security: {
    csrf: {
      total: number;
      bySession?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    xss: {
      total: number;
      bySession?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    rateLimit: {
      total: number;
      bySession?: Record<string, number>;
      byDate?: Record<string, number>;
    };
  };
  performance: {
    averageResponseTime: number;
    bySession?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface SessionService {
  createConfig: (config: Omit<SessionConfig, 'id'>) => SessionConfig;
  updateConfig: (id: string, config: Partial<SessionConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => SessionConfig | undefined;
  getConfigs: () => SessionConfig[];
  create: (
    config: Omit<SessionConfig, 'id'>,
    session: Omit<Session, 'id' | 'config'>
  ) => Promise<Session>;
  update: (id: string, session: Partial<Session>) => Promise<Session>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Session | undefined;
  getAll: (options?: {
    type?: SessionType[];
    status?: SessionStatus[];
    provider?: SessionProvider[];
    user?: string;
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Session[];
  search: (
    query: string,
    options?: {
      type?: SessionType[];
      status?: SessionStatus[];
      provider?: SessionProvider[];
      user?: string;
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Session[];
  refresh: (id: string) => Promise<{
    token: string;
    refreshToken?: string;
  }>;
  revoke: (id: string) => Promise<void>;
  validate: (token: string) => Promise<{
    valid: boolean;
    session?: Session;
    error?: {
      code: string;
      message: string;
    };
  }>;
  getLogs: (options?: {
    session?: string;
    action?: ('create' | 'update' | 'delete' | 'refresh' | 'revoke' | 'activity' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SessionLog[];
  getStats: () => SessionStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
