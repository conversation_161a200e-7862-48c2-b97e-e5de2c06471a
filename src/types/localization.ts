export type Locale =
  | 'en'
  | 'es'
  | 'fr'
  | 'de'
  | 'it'
  | 'pt'
  | 'ru'
  | 'zh'
  | 'ja'
  | 'ko'
  | 'ar'
  | 'custom';

export type LocaleDirection = 'ltr' | 'rtl';

export interface LocaleConfig {
  id: string;
  code: Locale;
  name: string;
  direction: LocaleDirection;
  fallback: Locale;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    precision: number;
  };
  metadata: Record<string, any>;
}

export interface Translation {
  id: string;
  config: LocaleConfig;
  key: string;
  value: string;
  context?: string;
  metadata?: Record<string, any>;
}

export interface LocaleStats {
  total: number;
  byLocale: Record<Locale, number>;
  byContext: Record<string, number>;
  missing: number;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface LocalizationService {
  createConfig: (config: Omit<LocaleConfig, 'id'>) => LocaleConfig;
  updateConfig: (id: string, config: Partial<LocaleConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => LocaleConfig | undefined;
  getConfigs: () => LocaleConfig[];
  setTranslation: (
    key: string,
    value: string,
    config: Omit<LocaleConfig, 'id'>,
    context?: string
  ) => Translation;
  getTranslation: (key: string, config: Omit<LocaleConfig, 'id'>) => string | undefined;
  deleteTranslation: (key: string, config: Omit<LocaleConfig, 'id'>) => void;
  getTranslations: (config: Omit<LocaleConfig, 'id'>) => Translation[];
  clear: (config: Omit<LocaleConfig, 'id'>) => void;
  getStats: () => LocaleStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}

export type LocalizationType = 'text' | 'number' | 'date' | 'currency' | 'custom';

export type LocalizationStatus = 'active' | 'inactive' | 'draft' | 'custom';

export interface LocalizationConfig {
  id: string;
  name: string;
  settings: {
    languages: {
      default: string;
      supported: string[];
      fallback?: string;
    };
    formats: {
      date: Record<string, string>;
      time: Record<string, string>;
      number: Record<string, string>;
      currency: Record<string, string>;
    };
    storage: {
      type: 'local' | 'database' | 'cache' | 'custom';
      encryption: boolean;
      compression: boolean;
    };
    caching: {
      enabled: boolean;
      ttl: number;
      strategy: 'memory' | 'redis' | 'custom';
    };
  };
  features: {
    pluralization: boolean;
    interpolation: boolean;
    fallback: boolean;
    export: boolean;
  };
  metadata: Record<string, any>;
}

export interface Localization {
  id: string;
  config: LocalizationConfig;
  type: LocalizationType;
  status: LocalizationStatus;
  key: string;
  namespace: string;
  translations: Record<string, string>;
  description?: string;
  tags?: string[];
  context?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface LocalizationNamespace {
  id: string;
  config: LocalizationConfig;
  name: string;
  description?: string;
  translations: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface LocalizationFormat {
  id: string;
  config: LocalizationConfig;
  type: LocalizationType;
  locale: string;
  format: string;
  example: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface LocalizationExport {
  id: string;
  config: LocalizationConfig;
  format: 'json' | 'yaml' | 'xml' | 'custom';
  locale: string;
  namespace?: string;
  data: any;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface LocalizationImport {
  id: string;
  config: LocalizationConfig;
  format: 'json' | 'yaml' | 'xml' | 'custom';
  locale: string;
  namespace?: string;
  data: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  metadata: Record<string, any>;
}

export interface LocalizationLog {
  id: string;
  config: LocalizationConfig;
  translation?: string;
  namespace?: string;
  format?: string;
  export?: string;
  import?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface LocalizationStats {
  total: number;
  byType: Record<LocalizationType, number>;
  byStatus: Record<LocalizationStatus, number>;
  byLocale: Record<string, number>;
  byNamespace: Record<string, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata: Record<string, any>;
}

export interface LocalizationService {
  createConfig: (config: Omit<LocalizationConfig, 'id'>) => LocalizationConfig;
  updateConfig: (id: string, config: Partial<LocalizationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => LocalizationConfig | undefined;
  getConfigs: () => LocalizationConfig[];
  createTranslation: (
    config: Omit<LocalizationConfig, 'id'>,
    translation: Omit<Localization, 'id' | 'config'>
  ) => Promise<Localization>;
  updateTranslation: (id: string, translation: Partial<Localization>) => Promise<Localization>;
  deleteTranslation: (id: string) => Promise<void>;
  getTranslation: (id: string) => Localization | undefined;
  getTranslations: (options?: {
    type?: LocalizationType[];
    status?: LocalizationStatus[];
    namespace?: string;
    locale?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Localization[];
  createNamespace: (
    config: Omit<LocalizationConfig, 'id'>,
    namespace: Omit<LocalizationNamespace, 'id' | 'config'>
  ) => Promise<LocalizationNamespace>;
  updateNamespace: (
    id: string,
    namespace: Partial<LocalizationNamespace>
  ) => Promise<LocalizationNamespace>;
  deleteNamespace: (id: string) => Promise<void>;
  getNamespace: (id: string) => LocalizationNamespace | undefined;
  getNamespaces: (options?: {
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => LocalizationNamespace[];
  createFormat: (
    config: Omit<LocalizationConfig, 'id'>,
    format: Omit<LocalizationFormat, 'id' | 'config'>
  ) => Promise<LocalizationFormat>;
  updateFormat: (id: string, format: Partial<LocalizationFormat>) => Promise<LocalizationFormat>;
  deleteFormat: (id: string) => Promise<void>;
  getFormat: (id: string) => LocalizationFormat | undefined;
  getFormats: (options?: {
    type?: LocalizationType[];
    locale?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => LocalizationFormat[];
  exportTranslations: (
    config: Omit<LocalizationConfig, 'id'>,
    options: {
      format: 'json' | 'yaml' | 'xml' | 'custom';
      locale: string;
      namespace?: string;
    }
  ) => Promise<LocalizationExport>;
  getExport: (id: string) => LocalizationExport | undefined;
  getExports: (options?: {
    format?: ('json' | 'yaml' | 'xml' | 'custom')[];
    locale?: string;
    namespace?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => LocalizationExport[];
  importTranslations: (
    config: Omit<LocalizationConfig, 'id'>,
    options: {
      format: 'json' | 'yaml' | 'xml' | 'custom';
      locale: string;
      namespace?: string;
      data: any;
    }
  ) => Promise<LocalizationImport>;
  getImport: (id: string) => LocalizationImport | undefined;
  getImports: (options?: {
    format?: ('json' | 'yaml' | 'xml' | 'custom')[];
    locale?: string;
    namespace?: string;
    status?: ('pending' | 'processing' | 'completed' | 'failed')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => LocalizationImport[];
  getLogs: (options?: {
    translation?: string;
    namespace?: string;
    format?: string;
    export?: string;
    import?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => LocalizationLog[];
  getStats: () => LocalizationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
