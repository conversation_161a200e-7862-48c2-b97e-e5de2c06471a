/**
 * Unified Types Module
 * Consolidates all duplicate types and interfaces from across the codebase
 */

import { ReactNode } from 'react';

// ============================================================================
// CORE TYPES
// ============================================================================

export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'ru' | 'zh' | 'es' | 'fr' | 'de' | 'ja' | 'ko' | 'ar';
export type Platform = 'web' | 'desktop' | 'mobile';
export type Environment = 'development' | 'staging' | 'production' | 'test';

export type Status = 'active' | 'inactive' | 'deprecated' | 'deleted' | 'error' | 'loading';
export type Scope = 'global' | 'user' | 'group' | 'application' | 'custom';
export type Type = 'system' | 'user' | 'application' | 'custom';

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

export interface BaseConfig {
  theme: Theme;
  language: Language;
  platform: Platform;
  environment: Environment;
}

export interface StorageConfig {
  type: 'database' | 'file' | 's3' | 'memory' | 'custom';
  path?: string;
  connection?: Record<string, any>;
  encryption?: {
    enabled: boolean;
    algorithm: string;
    key?: string;
  };
  backup?: {
    enabled: boolean;
    schedule: string;
    retention: number;
  };
  versioning?: {
    enabled: boolean;
    maxVersions: number;
    strategy: 'major' | 'minor' | 'patch' | 'custom';
  };
}

export interface ValidationConfig {
  enabled: boolean;
  rules?: ValidationRule[];
}

export interface ValidationRule {
  name: string;
  type?: string;
  pattern?: string;
  message?: string;
  required?: boolean;
  min?: number;
  max?: number;
  enum?: any[];
  custom?: Record<string, any>;
}

export interface MonitoringConfig {
  enabled: boolean;
  metrics?: {
    settings?: boolean;
    usage?: boolean;
    performance?: boolean;
  };
  alerts?: {
    enabled: boolean;
    channels?: AlertChannel[];
  };
}

export interface AlertChannel {
  type: 'email' | 'slack' | 'webhook' | 'custom';
  config: Record<string, any>;
}

// ============================================================================
// SECURITY TYPES
// ============================================================================

export type SecurityLevel = 'low' | 'medium' | 'high' | 'critical';
export type AuthMethod = 'password' | 'oauth' | 'saml' | '2fa' | 'biometric' | 'custom';
export type EncryptionAlgorithm = 'aes-256-gcm' | 'chacha20-poly1305' | 'custom';

export interface SecurityConfig {
  level: SecurityLevel;
  authentication: {
    enabled: boolean;
    methods: AuthMethod[];
    password?: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      maxAttempts: number;
      lockoutDuration: number;
    };
    oauth?: {
      providers: OAuthProvider[];
    };
    saml?: {
      providers: SAMLProvider[];
    };
  };
  authorization: {
    enabled: boolean;
    engine: 'rbac' | 'abac' | 'custom';
    roles: Role[];
    policies: Policy[];
  };
  encryption: {
    algorithm: EncryptionAlgorithm;
    keySize: number;
    iterations: number;
  };
}

export interface OAuthProvider {
  name: string;
  clientId: string;
  clientSecret: string;
  scopes: string[];
}

export interface SAMLProvider {
  name: string;
  entityId: string;
  ssoUrl: string;
  certificate: string;
}

export interface Role {
  name: string;
  permissions: string[];
  inheritance?: string[];
}

export interface Policy {
  name: string;
  effect: 'allow' | 'deny';
  actions: string[];
  resources: string[];
  conditions?: Record<string, any>;
}

// ============================================================================
// PERFORMANCE TYPES
// ============================================================================

export type PerformanceMetric =
  | 'loadTime'
  | 'firstPaint'
  | 'firstContentfulPaint'
  | 'largestContentfulPaint'
  | 'timeToInteractive'
  | 'totalBlockingTime'
  | 'cumulativeLayoutShift'
  | 'firstInputDelay'
  | 'speedIndex'
  | 'timeToFirstByte'
  | 'domContentLoaded'
  | 'domComplete'
  | 'resourceCount'
  | 'resourceSize'
  | 'memoryUsage'
  | 'cpuUsage'
  | 'networkRequests'
  | 'cacheHits'
  | 'cacheMisses'
  | 'errorRate';

export interface PerformanceConfig {
  enabled: boolean;
  metrics: PerformanceMetric[];
  thresholds: Record<PerformanceMetric, number>;
  sampling: number;
  profiling: {
    enabled: boolean;
    interval: number;
    retention: number;
  };
  optimization: {
    enabled: boolean;
    autoOptimize: boolean;
    optimizationLevel: 'low' | 'medium' | 'high';
  };
  reporting: {
    enabled: boolean;
    interval: number;
    endpoint?: string;
  };
}

export interface PerformanceMetrics {
  cpu: {
    usage: number;
    temperature: number;
    frequency: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    available: number;
    cached: number;
    buffers: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    readSpeed: number;
    writeSpeed: number;
  };
  network: {
    downloadSpeed: number;
    uploadSpeed: number;
    latency: number;
    packetsLost: number;
  };
  gpu: {
    usage: number;
    memory: number;
    temperature: number;
  };
  browser: {
    tabCount: number;
    extensionCount: number;
    processCount: number;
    memoryUsage: number;
  };
}

// ============================================================================
// API TYPES
// ============================================================================

export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';
export type ApiStatus = 'success' | 'error' | 'loading' | 'idle';

export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  headers?: Record<string, string>;
  authentication?: {
    type: 'bearer' | 'basic' | 'api-key' | 'custom';
    token?: string;
    credentials?: {
      username: string;
      password: string;
    };
  };
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: ApiConfig;
}

export interface ApiError {
  message: string;
  code: string;
  status: number;
  details?: any;
}

// ============================================================================
// THEME TYPES
// ============================================================================

export type ThemeFormat = 'css' | 'scss' | 'less' | 'custom';

export interface ThemeColor {
  name: string;
  value: string;
  contrast: string;
  variants: Record<string, string>;
}

export interface ThemeSpacing {
  unit: number;
  scale: Record<string, number>;
}

export interface ThemeTypography {
  fontFamily: string;
  fontSize: Record<string, number>;
  fontWeight: Record<string, number>;
  lineHeight: Record<string, number>;
  letterSpacing: Record<string, number>;
}

export interface ThemeBreakpoint {
  name: string;
  value: number;
}

export interface ThemeConfig {
  name: string;
  type: Type;
  status: Status;
  format: ThemeFormat;
  colors: ThemeColor[];
  spacing: ThemeSpacing;
  typography: ThemeTypography;
  breakpoints: ThemeBreakpoint[];
  shadows: Record<string, string>;
  borderRadius: Record<string, string>;
  transitions: Record<string, string>;
}

// ============================================================================
// DATA TYPES
// ============================================================================

export type DataType = 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array' | 'custom';

export interface DataConfig {
  id: string;
  name: string;
  type: DataType;
  validation: ValidationConfig;
  transformation: {
    trim: boolean;
    lowercase: boolean;
    uppercase: boolean;
    format?: string;
  };
  storage: StorageConfig;
  features: {
    versioning: boolean;
    encryption: boolean;
    compression: boolean;
    backup: boolean;
  };
  metadata: Record<string, any>;
}

// ============================================================================
// FEATURE FLAGS
// ============================================================================

export interface FeatureFlags {
  [key: string]: boolean;
}

export interface FeatureFlag {
  name: string;
  enabled: boolean;
  description?: string;
  conditions?: Record<string, any>;
  rollout?: {
    percentage: number;
    userGroups?: string[];
  };
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ErrorInfo {
  message: string;
  source: string;
  lineno: number;
  colno: number;
  error: Error;
  timestamp: number;
  stack?: string;
}

export interface ErrorConfig {
  reporting: {
    enabled: boolean;
    dsn?: string;
    environment: string;
  };
  handling: {
    showErrorBoundary: boolean;
    logToConsole: boolean;
    notifyUser: boolean;
  };
}

// ============================================================================
// EXPORT LEGACY TYPES FOR BACKWARD COMPATIBILITY
// ============================================================================

// Configuration aliases
export type ConfigurationType = Type;
export type ConfigurationStatus = Status;
export type ConfigurationScope = Scope;
export type ConfigurationConfig = {
  management: {
    enabled: boolean;
    types: Type[];
    scopes: Scope[];
    validation?: ValidationConfig;
  };
  storage: StorageConfig;
  monitoring: MonitoringConfig;
};

// Settings aliases
export type SettingsType = Type;
export type SettingsStatus = Status;
export type SettingsFormat = 'json' | 'yaml' | 'xml' | 'custom';

// Security aliases
export type SecurityType = Type;
export type SecurityStatus = Status;

// API aliases
export type ApiType = Type;

// Theme aliases
export type ThemeType = Type;
export type ThemeStatus = Status;
