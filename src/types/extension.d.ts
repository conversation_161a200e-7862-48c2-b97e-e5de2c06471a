declare module 'react-i18next' {
  export function useTranslation(): {
    t: (key: string, options?: any) => string;
  };
}

export interface Extension {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  icon: string;
  category: string;
  tags: string[];
  price: number;
  rating: number;
  reviewCount: number;
  downloads: number;
  verified: boolean;
  featured: boolean;
  promoted: boolean;
  compatibility: {
    isCompatible: boolean;
    minVersion?: string;
    maxVersion?: string;
  };
  trial?: {
    enabled: boolean;
    days: number;
  };
  lastUpdated: string;
  manifest?: {
    name: string;
    version: string;
    description: string;
    permissions: string[];
    host_permissions: string[];
    background?: {
      service_worker: string;
    };
    content_scripts?: Array<{
      matches: string[];
      js: string[];
      css?: string[];
    }>;
    web_accessible_resources?: Array<{
      resources: string[];
      matches: string[];
    }>;
    icons?: {
      [key: string]: string;
    };
    action?: {
      default_icon?: string;
      default_title?: string;
      default_popup?: string;
    };
    options_page?: string;
    update_url?: string;
    homepage_url?: string;
    privacy_policy_url?: string;
  };
}

export interface ExtensionStoreState {
  items: Extension[];
  installed: string[];
  loading: boolean;
  error: string | null;
  store: {
    extensions: Extension[];
    loading: boolean;
    error: string | null;
  };
}

export interface ExtensionStoreProps {
  onInstall?: (extension: Extension) => void;
  onUninstall?: (extension: Extension) => void;
  onUpdate?: (extension: Extension) => void;
}
