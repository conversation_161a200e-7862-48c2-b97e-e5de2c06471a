export type LocaleType = 'system' | 'user' | 'application' | 'custom';
export type LocaleStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type LocaleFormat = 'json' | 'yaml' | 'xml' | 'custom';

export interface LocaleConfig {
  management: {
    enabled: boolean;
    types: LocaleType[];
    formats: LocaleFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  locale: {
    enabled: boolean;
    default: string;
    fallback: string;
    locale: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: LocaleFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      locale?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    locale?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Locale {
  id: string;
  config: LocaleConfig;
  type: LocaleType;
  status: LocaleStatus;
  format: LocaleFormat;
  name: string;
  description?: string;
  locale: {
    locale: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: LocaleFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    locale: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface LocaleLog {
  id: string;
  locale: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface LocaleStats {
  total: number;
  byType: Record<LocaleType, number>;
  byStatus: Record<LocaleStatus, number>;
  byFormat: Record<LocaleFormat, number>;
  locale: {
    total: number;
    byType?: Record<string, number>;
    byLocale?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byLocale?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byLocale?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface LocaleService {
  createConfig: (config: Omit<LocaleConfig, 'id'>) => LocaleConfig;
  updateConfig: (id: string, config: Partial<LocaleConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => LocaleConfig | undefined;
  getConfigs: () => LocaleConfig[];
  create: (
    config: Omit<LocaleConfig, 'id'>,
    locale: Omit<Locale, 'id' | 'config'>
  ) => Promise<Locale>;
  update: (id: string, locale: Partial<Locale>) => Promise<Locale>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Locale | undefined;
  getAll: (options?: {
    type?: LocaleType[];
    status?: LocaleStatus[];
    format?: LocaleFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Locale[];
  search: (
    query: string,
    options?: {
      type?: LocaleType[];
      status?: LocaleStatus[];
      format?: LocaleFormat[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Locale[];
  validate: (id: string, locale: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    locale?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => LocaleLog[];
  getStats: () => LocaleStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
