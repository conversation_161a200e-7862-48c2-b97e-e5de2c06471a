export type TokenType = 'access' | 'refresh' | 'id' | 'custom';
export type TokenStatus = 'active' | 'expired' | 'revoked' | 'archived';
export type TokenAlgorithm =
  | 'HS256'
  | 'HS384'
  | 'HS512'
  | 'RS256'
  | 'RS384'
  | 'RS512'
  | 'ES256'
  | 'ES384'
  | 'ES512'
  | 'custom';

export interface TokenConfig {
  management: {
    enabled: boolean;
    types: TokenType[];
    algorithms: TokenAlgorithm[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'redis' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  generation: {
    enabled: boolean;
    methods?: {
      jwt?: boolean;
      opaque?: boolean;
      custom?: boolean;
    };
    jwt?: {
      enabled: boolean;
      algorithm: TokenAlgorithm;
      secret: string;
      options?: {
        issuer?: string;
        audience?: string;
        expiresIn?: string;
        notBefore?: string;
        jwtid?: string;
        subject?: string;
        noTimestamp?: boolean;
        header?: Record<string, any>;
      };
    };
    opaque?: {
      enabled: boolean;
      length: number;
      charset: string;
      prefix?: string;
    };
  };
  validation: {
    enabled: boolean;
    methods?: {
      signature?: boolean;
      expiration?: boolean;
      audience?: boolean;
      issuer?: boolean;
    };
    signature?: {
      enabled: boolean;
      algorithm: TokenAlgorithm;
      secret: string;
    };
    expiration?: {
      enabled: boolean;
      clockSkew: number;
    };
    audience?: {
      enabled: boolean;
      values: string[];
    };
    issuer?: {
      enabled: boolean;
      values: string[];
    };
  };
  security: {
    enabled: boolean;
    features?: {
      rotation?: boolean;
      revocation?: boolean;
      blacklist?: boolean;
    };
    rotation?: {
      enabled: boolean;
      interval: number;
      overlap: number;
    };
    revocation?: {
      enabled: boolean;
      reason: string;
      timestamp: Date;
    };
    blacklist?: {
      enabled: boolean;
      ttl: number;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      usage?: boolean;
      performance?: boolean;
      security?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    refresh?: boolean;
    revoke?: boolean;
    rotate?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Token {
  id: string;
  config: TokenConfig;
  type: TokenType;
  status: TokenStatus;
  algorithm: TokenAlgorithm;
  value: string;
  payload?: Record<string, any>;
  user?: string;
  session?: string;
  scopes?: string[];
  permissions?: string[];
  security?: {
    rotation?: {
      previous?: string;
      next?: string;
      lastRotated?: Date;
    };
    revocation?: {
      reason?: string;
      timestamp?: Date;
    };
    blacklist?: {
      reason?: string;
      timestamp?: Date;
      expiresAt?: Date;
    };
  };
  stats?: {
    usage: number;
    lastUsed?: Date;
    created: Date;
    expires?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
  metadata?: Record<string, any>;
}

export interface TokenLog {
  id: string;
  token: string;
  action: 'create' | 'update' | 'delete' | 'refresh' | 'revoke' | 'rotate' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface TokenStats {
  total: number;
  byType: Record<TokenType, number>;
  byStatus: Record<TokenStatus, number>;
  byAlgorithm: Record<TokenAlgorithm, number>;
  usage: {
    total: number;
    byToken?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  security: {
    rotation: {
      total: number;
      byToken?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    revocation: {
      total: number;
      byToken?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    blacklist: {
      total: number;
      byToken?: Record<string, number>;
      byDate?: Record<string, number>;
    };
  };
  performance: {
    averageResponseTime: number;
    byToken?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface TokenService {
  createConfig: (config: Omit<TokenConfig, 'id'>) => TokenConfig;
  updateConfig: (id: string, config: Partial<TokenConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => TokenConfig | undefined;
  getConfigs: () => TokenConfig[];
  create: (config: Omit<TokenConfig, 'id'>, token: Omit<Token, 'id' | 'config'>) => Promise<Token>;
  update: (id: string, token: Partial<Token>) => Promise<Token>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Token | undefined;
  getAll: (options?: {
    type?: TokenType[];
    status?: TokenStatus[];
    algorithm?: TokenAlgorithm[];
    user?: string;
    session?: string;
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Token[];
  search: (
    query: string,
    options?: {
      type?: TokenType[];
      status?: TokenStatus[];
      algorithm?: TokenAlgorithm[];
      user?: string;
      session?: string;
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Token[];
  refresh: (id: string) => Promise<{
    token: string;
    refreshToken?: string;
  }>;
  revoke: (id: string, reason?: string) => Promise<void>;
  rotate: (id: string) => Promise<{
    token: string;
    refreshToken?: string;
  }>;
  validate: (token: string) => Promise<{
    valid: boolean;
    token?: Token;
    error?: {
      code: string;
      message: string;
    };
  }>;
  getLogs: (options?: {
    token?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'refresh'
      | 'revoke'
      | 'rotate'
      | 'validate'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TokenLog[];
  getStats: () => TokenStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
