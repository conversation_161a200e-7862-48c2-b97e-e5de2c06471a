export type UserType = 'admin' | 'moderator' | 'user' | 'guest' | 'custom';
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'banned' | 'deleted';
export type UserRole = 'admin' | 'moderator' | 'user' | 'guest' | 'custom';

export interface UserConfig {
  management: {
    enabled: boolean;
    types: UserType[];
    roles: UserRole[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  authentication: {
    enabled: boolean;
    methods: {
      password?: {
        enabled: boolean;
        minLength: number;
        requireUppercase: boolean;
        requireLowercase: boolean;
        requireNumbers: boolean;
        requireSpecial: boolean;
      };
      oauth?: {
        enabled: boolean;
        providers: {
          name: string;
          enabled: boolean;
          config: Record<string, any>;
        }[];
      };
      mfa?: {
        enabled: boolean;
        methods: {
          type: 'totp' | 'sms' | 'email' | 'custom';
          enabled: boolean;
          config: Record<string, any>;
        }[];
      };
    };
    session?: {
      enabled: boolean;
      timeout: number;
      renewal: boolean;
      maxSessions: number;
    };
  };
  authorization: {
    enabled: boolean;
    roles: {
      name: string;
      permissions: string[];
      inheritance?: string[];
    }[];
    policies: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
  };
  profile: {
    enabled: boolean;
    fields: {
      name: string;
      type: 'text' | 'email' | 'phone' | 'date' | 'select' | 'custom';
      required: boolean;
      validation?: {
        pattern: string;
        message: string;
      };
    }[];
    avatar?: {
      enabled: boolean;
      maxSize: number;
      formats: string[];
    };
  };
  notification: {
    enabled: boolean;
    channels?: {
      type: 'email' | 'sms' | 'push' | 'custom';
      config: Record<string, any>;
    }[];
    templates?: {
      name: string;
      content: string;
      variables?: string[];
    }[];
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      storage?: boolean;
      authentication?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    authentication?: boolean;
    authorization?: boolean;
    profile?: boolean;
    notification?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface User {
  id: string;
  config: UserConfig;
  type: UserType;
  status: UserStatus;
  roles: UserRole[];
  profile: {
    username: string;
    email: string;
    phone?: string;
    firstName?: string;
    lastName?: string;
    avatar?: {
      url: string;
      size: number;
      type: string;
    };
    fields: Record<string, any>;
  };
  authentication: {
    password?: {
      hash: string;
      salt: string;
      lastChanged?: Date;
    };
    oauth?: {
      provider: string;
      id: string;
      token?: string;
      refreshToken?: string;
      expiresAt?: Date;
    }[];
    mfa?: {
      type: string;
      enabled: boolean;
      secret?: string;
      backupCodes?: string[];
    }[];
    sessions?: {
      id: string;
      token: string;
      device?: string;
      ip?: string;
      lastActive: Date;
      expiresAt: Date;
    }[];
  };
  authorization: {
    permissions: string[];
    policies: string[];
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  storage?: {
    path: string;
    url?: string;
    provider?: string;
    region?: string;
    bucket?: string;
  };
  notification?: {
    preferences: {
      channel: string;
      enabled: boolean;
      config?: Record<string, any>;
    }[];
    history?: {
      id: string;
      type: string;
      channel: string;
      status: 'pending' | 'sent' | 'failed';
      date: Date;
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    }[];
  };
  stats?: {
    logins: number;
    sessions: number;
    notifications: number;
    activities: number;
  };
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface UserLog {
  id: string;
  user: string;
  action: 'create' | 'update' | 'delete' | 'login' | 'logout' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface UserStats {
  total: number;
  byType: Record<UserType, number>;
  byStatus: Record<UserStatus, number>;
  byRole: Record<UserRole, number>;
  storage: {
    total: number;
    byType?: Record<UserType, number>;
    byRole?: Record<UserRole, number>;
    byDate?: Record<string, number>;
  };
  authentication: {
    total: number;
    success: number;
    failure: number;
    byUser?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageSessions: number;
    averageActivities: number;
    byUser?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface UserService {
  createConfig: (config: Omit<UserConfig, 'id'>) => UserConfig;
  updateConfig: (id: string, config: Partial<UserConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => UserConfig | undefined;
  getConfigs: () => UserConfig[];
  create: (config: Omit<UserConfig, 'id'>, user: Omit<User, 'id' | 'config'>) => Promise<User>;
  update: (id: string, user: Partial<User>) => Promise<User>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => User | undefined;
  getAll: (options?: {
    type?: UserType[];
    status?: UserStatus[];
    role?: UserRole[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => User[];
  search: (
    query: string,
    options?: {
      type?: UserType[];
      status?: UserStatus[];
      role?: UserRole[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => User[];
  login: (username: string, password: string) => Promise<User>;
  logout: (id: string) => Promise<void>;
  getLogs: (options?: {
    user?: string;
    action?: ('create' | 'update' | 'delete' | 'login' | 'logout' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => UserLog[];
  getStats: () => UserStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
