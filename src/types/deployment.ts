export type DeploymentType = 'application' | 'service' | 'database' | 'infrastructure' | 'custom';

export type DeploymentStatus =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'rolledback'
  | 'custom';

export type DeploymentStrategy = 'recreate' | 'rolling' | 'blue-green' | 'canary' | 'custom';

export interface DeploymentConfig {
  id: string;
  name: string;
  settings: {
    deployment: {
      enabled: boolean;
      type?: DeploymentType;
      status?: DeploymentStatus;
      strategy?: DeploymentStrategy;
      timeout?: number;
      retries?: number;
      parallel?: boolean;
    };
    environment: {
      enabled: boolean;
      type?: 'development' | 'staging' | 'production' | 'custom';
      variables?: Record<string, any>;
      secrets?: Record<string, any>;
      resources?: Record<string, any>;
    };
    build: {
      enabled: boolean;
      type?: 'docker' | 'kubernetes' | 'terraform' | 'custom';
      steps?: Record<string, any>;
      artifacts?: Record<string, any>;
    };
    test: {
      enabled: boolean;
      type?: ('unit' | 'integration' | 'e2e' | 'performance' | 'security')[];
      config?: Record<string, any>;
    };
    monitoring: {
      enabled: boolean;
      metrics?: boolean;
      logs?: boolean;
      traces?: boolean;
    };
    rollback: {
      enabled: boolean;
      automatic?: boolean;
      conditions?: Record<string, any>;
    };
  };
  features: {
    versioning: boolean;
    scaling: boolean;
    backup: boolean;
    recovery: boolean;
  };
  metadata: Record<string, any>;
}

export interface Deployment {
  id: string;
  config: DeploymentConfig;
  type: DeploymentType;
  status: DeploymentStatus;
  strategy: DeploymentStrategy;
  name: string;
  description?: string;
  version: string;
  environment: string;
  steps?: {
    name: string;
    action: string;
    status: DeploymentStatus;
    duration?: number;
    error?: {
      message: string;
      stack?: string;
      details?: any;
    };
  }[];
  build?: {
    type: string;
    steps: Record<string, any>;
    artifacts: Record<string, any>;
    status: DeploymentStatus;
    duration?: number;
    error?: {
      message: string;
      stack?: string;
      details?: any;
    };
  };
  test?: {
    type: string[];
    config: Record<string, any>;
    status: DeploymentStatus;
    duration?: number;
    error?: {
      message: string;
      stack?: string;
      details?: any;
    };
  };
  resources?: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  performance?: {
    duration: number;
    memory: number;
    cpu: number;
    network: number;
  };
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface DeploymentLog {
  id: string;
  config: DeploymentConfig;
  deployment: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: Record<string, any>;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface DeploymentStats {
  total: number;
  byType: Record<DeploymentType, number>;
  byStatus: Record<DeploymentStatus, number>;
  byStrategy: Record<DeploymentStrategy, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  performance: {
    duration: number;
    memory: number;
    cpu: number;
    network: number;
  };
  metadata: Record<string, any>;
}

export interface DeploymentService {
  createConfig: (config: Omit<DeploymentConfig, 'id'>) => DeploymentConfig;
  updateConfig: (id: string, config: Partial<DeploymentConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => DeploymentConfig | undefined;
  getConfigs: () => DeploymentConfig[];
  create: (
    config: Omit<DeploymentConfig, 'id'>,
    deployment: Omit<Deployment, 'id' | 'config'>
  ) => Promise<Deployment>;
  update: (id: string, deployment: Partial<Deployment>) => Promise<Deployment>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Deployment | undefined;
  getAll: (options?: {
    type?: DeploymentType[];
    status?: DeploymentStatus[];
    strategy?: DeploymentStrategy[];
    tags?: string[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Deployment[];
  search: (
    query: string,
    options?: {
      type?: DeploymentType[];
      status?: DeploymentStatus[];
      strategy?: DeploymentStrategy[];
      tags?: string[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Deployment[];
  deploy: (
    id: string,
    options?: {
      environment?: string;
      version?: string;
      strategy?: DeploymentStrategy;
      parallel?: boolean;
    }
  ) => Promise<Deployment>;
  rollback: (
    id: string,
    options?: {
      version?: string;
      automatic?: boolean;
    }
  ) => Promise<Deployment>;
  scale: (
    id: string,
    options?: {
      replicas?: number;
      resources?: Record<string, number>;
    }
  ) => Promise<Deployment>;
  getLogs: (options?: {
    deployment?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DeploymentLog[];
  backup: (
    id: string,
    options?: {
      type?: 'full' | 'incremental';
      destination?: string;
      compression?: boolean;
    }
  ) => Promise<string>;
  recover: (
    id: string,
    backup: string,
    options?: {
      validate?: boolean;
    }
  ) => Promise<Deployment>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => DeploymentStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
