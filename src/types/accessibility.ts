export type AccessibilityLevel = 'A' | 'AA' | 'AAA';

export type AccessibilityFeature =
  | 'screenReader'
  | 'keyboardNavigation'
  | 'highContrast'
  | 'reducedMotion'
  | 'textScaling'
  | 'colorBlindness'
  | 'focusVisible'
  | 'skipLinks'
  | 'ariaLabels'
  | 'semanticHTML';

export interface AccessibilityConfig {
  level: AccessibilityLevel;
  features: AccessibilityFeature[];
  skipLinks: boolean;
  focusVisible: boolean;
  highContrast: boolean;
  reducedMotion: boolean;
  textScaling: boolean;
  colorBlindness: boolean;
  metadata: Record<string, any>;
}

export interface AccessibilityViolation {
  id: string;
  type: string;
  level: AccessibilityLevel;
  element: string;
  message: string;
  recommendation: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AccessibilityReport {
  id: string;
  timestamp: Date;
  level: AccessibilityLevel;
  violations: AccessibilityViolation[];
  score: number;
  metadata?: Record<string, any>;
}

export interface AccessibilityStats {
  total: number;
  byLevel: Record<AccessibilityLevel, number>;
  byFeature: Record<AccessibilityFeature, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface AccessibilityService {
  check: (element: HTMLElement) => Promise<AccessibilityReport>;
  fix: (violation: AccessibilityViolation) => Promise<void>;
  getViolations: () => AccessibilityViolation[];
  getReport: () => AccessibilityReport;
  getStats: () => AccessibilityStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
