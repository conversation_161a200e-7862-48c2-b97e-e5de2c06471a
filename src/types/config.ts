export type ConfigType = 'system' | 'application' | 'environment' | 'feature' | 'custom';

export type ConfigFormat = 'json' | 'yaml' | 'toml' | 'ini' | 'custom';

export type ConfigStatus = 'draft' | 'review' | 'approved' | 'deployed' | 'custom';

export interface ConfigConfig {
  id: string;
  name: string;
  settings: {
    management: {
      enabled: boolean;
      type?: ConfigType;
      format?: ConfigFormat;
      status?: ConfigStatus;
      versioning?: boolean;
      validation?: boolean;
    };
    storage: {
      enabled: boolean;
      type?: 'file' | 'database' | 'vault' | 'custom';
      encryption?: {
        enabled: boolean;
        algorithm?: string;
        key?: string;
      };
      backup?: {
        enabled: boolean;
        schedule?: string;
        retention?: number;
      };
    };
    deployment: {
      enabled: boolean;
      strategy?: 'push' | 'pull' | 'custom';
      targets?: string[];
      validation?: {
        enabled: boolean;
        rules?: Record<string, any>;
      };
    };
    monitoring: {
      enabled: boolean;
      metrics?: boolean;
      logs?: boolean;
      traces?: boolean;
    };
  };
  features: {
    templating: boolean;
    inheritance: boolean;
    override: boolean;
    rollback: boolean;
  };
  metadata: Record<string, any>;
}

export interface Config {
  id: string;
  config: ConfigConfig;
  type: ConfigType;
  format: ConfigFormat;
  status: ConfigStatus;
  name: string;
  description?: string;
  version: string;
  content: Record<string, any>;
  schema?: Record<string, any>;
  validation?: {
    rules: Record<string, any>;
    errors?: {
      path: string;
      message: string;
    }[];
  };
  deployment?: {
    strategy: string;
    targets: string[];
    status: ConfigStatus;
    errors?: {
      target: string;
      message: string;
      details?: any;
    }[];
  };
  history?: {
    version: string;
    changes: Record<string, any>;
    author: string;
    timestamp: Date;
  }[];
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface ConfigLog {
  id: string;
  config: ConfigConfig;
  configId: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: Record<string, any>;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface ConfigStats {
  total: number;
  byType: Record<ConfigType, number>;
  byFormat: Record<ConfigFormat, number>;
  byStatus: Record<ConfigStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  performance: {
    loadTime: number;
    saveTime: number;
    validateTime: number;
  };
  metadata: Record<string, any>;
}

export interface ConfigService {
  createConfig: (config: Omit<ConfigConfig, 'id'>) => ConfigConfig;
  updateConfig: (id: string, config: Partial<ConfigConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ConfigConfig | undefined;
  getConfigs: () => ConfigConfig[];
  create: (
    config: Omit<ConfigConfig, 'id'>,
    configData: Omit<Config, 'id' | 'config'>
  ) => Promise<Config>;
  update: (id: string, configData: Partial<Config>) => Promise<Config>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Config | undefined;
  getAll: (options?: {
    type?: ConfigType[];
    format?: ConfigFormat[];
    status?: ConfigStatus[];
    tags?: string[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Config[];
  search: (
    query: string,
    options?: {
      type?: ConfigType[];
      format?: ConfigFormat[];
      status?: ConfigStatus[];
      tags?: string[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Config[];
  deploy: (
    id: string,
    options?: {
      strategy?: string;
      targets?: string[];
      validate?: boolean;
    }
  ) => Promise<Config>;
  rollback: (
    id: string,
    options?: {
      version?: string;
      targets?: string[];
    }
  ) => Promise<Config>;
  getLogs: (options?: {
    config?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ConfigLog[];
  backup: (
    id: string,
    options?: {
      type?: 'full' | 'incremental';
      destination?: string;
      compression?: boolean;
    }
  ) => Promise<string>;
  restore: (
    id: string,
    backup: string,
    options?: {
      validate?: boolean;
    }
  ) => Promise<Config>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => ConfigStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
