export type MediaType = 'system' | 'user' | 'application' | 'custom';
export type MediaStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type MediaFormat = 'image' | 'video' | 'audio' | 'document' | 'custom';

export interface MediaConfig {
  management: {
    enabled: boolean;
    types: MediaType[];
    formats: MediaFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  media: {
    enabled: boolean;
    default: string;
    fallback: string;
    media: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: MediaFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      media?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    media?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Media {
  id: string;
  config: MediaConfig;
  type: MediaType;
  status: MediaStatus;
  format: MediaFormat;
  name: string;
  description?: string;
  media: {
    media: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: MediaFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    media: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface MediaLog {
  id: string;
  media: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface MediaStats {
  total: number;
  byType: Record<MediaType, number>;
  byStatus: Record<MediaStatus, number>;
  byFormat: Record<MediaFormat, number>;
  media: {
    total: number;
    byType?: Record<string, number>;
    byMedia?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byMedia?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byMedia?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface MediaService {
  createConfig: (config: Omit<MediaConfig, 'id'>) => MediaConfig;
  updateConfig: (id: string, config: Partial<MediaConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => MediaConfig | undefined;
  getConfigs: () => MediaConfig[];
  create: (config: Omit<MediaConfig, 'id'>, media: Omit<Media, 'id' | 'config'>) => Promise<Media>;
  update: (id: string, media: Partial<Media>) => Promise<Media>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Media | undefined;
  getAll: (options?: {
    type?: MediaType[];
    status?: MediaStatus[];
    format?: MediaFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Media[];
  search: (
    query: string,
    options?: {
      type?: MediaType[];
      status?: MediaStatus[];
      format?: MediaFormat[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Media[];
  validate: (id: string, media: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    media?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => MediaLog[];
  getStats: () => MediaStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
