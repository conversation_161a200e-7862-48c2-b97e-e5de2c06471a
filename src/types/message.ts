export type MessageType = 'text' | 'image' | 'file' | 'system' | 'custom';

export type MessageStatus = 'sent' | 'delivered' | 'read' | 'deleted' | 'custom';

export interface MessageConfig {
  id: string;
  name: string;
  settings: {
    encryption: boolean;
    retention: number;
    attachments: boolean;
    reactions: boolean;
  };
  features: {
    threads: boolean;
    mentions: boolean;
    editing: boolean;
    forwarding: boolean;
  };
  metadata: Record<string, any>;
}

export interface Message {
  id: string;
  config: MessageConfig;
  type: MessageType;
  status: MessageStatus;
  thread: string;
  sender: string;
  recipients: string[];
  content: string;
  attachments?: {
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
  }[];
  mentions?: string[];
  reactions?: {
    user: string;
    emoji: string;
    timestamp: Date;
  }[];
  replyTo?: string;
  editedAt?: Date;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface MessageThread {
  id: string;
  config: MessageConfig;
  title?: string;
  type: 'direct' | 'group' | 'channel';
  participants: string[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface MessageStats {
  total: number;
  byType: Record<MessageType, number>;
  byStatus: Record<MessageStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface MessageService {
  createConfig: (config: Omit<MessageConfig, 'id'>) => MessageConfig;
  updateConfig: (id: string, config: Partial<MessageConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => MessageConfig | undefined;
  getConfigs: () => MessageConfig[];
  createThread: (
    config: Omit<MessageConfig, 'id'>,
    thread: Omit<MessageThread, 'id' | 'config'>
  ) => Promise<MessageThread>;
  updateThread: (id: string, thread: Partial<MessageThread>) => Promise<MessageThread>;
  deleteThread: (id: string) => Promise<void>;
  getThread: (id: string) => MessageThread | undefined;
  getThreads: (options?: {
    type?: MessageThread['type'];
    participant?: string;
    startDate?: Date;
    endDate?: Date;
  }) => MessageThread[];
  sendMessage: (
    config: Omit<MessageConfig, 'id'>,
    message: Omit<Message, 'id' | 'config'>
  ) => Promise<Message>;
  updateMessage: (id: string, message: Partial<Message>) => Promise<Message>;
  deleteMessage: (id: string) => Promise<void>;
  getMessage: (id: string) => Message | undefined;
  getMessages: (options?: {
    type?: MessageType;
    status?: MessageStatus;
    thread?: string;
    sender?: string;
    recipient?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Message[];
  addReaction: (messageId: string, userId: string, emoji: string) => Promise<Message>;
  removeReaction: (messageId: string, userId: string, emoji: string) => Promise<Message>;
  markAsRead: (threadId: string, userId: string) => Promise<void>;
  clear: () => Promise<void>;
  getStats: () => MessageStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
