export type CertificateType = 'ssl' | 'code' | 'client' | 'custom';
export type CertificateStatus = 'active' | 'expired' | 'revoked' | 'archived';
export type CertificateAlgorithm = 'RSA' | 'ECC' | 'EdDSA' | 'custom';

export interface CertificateConfig {
  management: {
    enabled: boolean;
    types: CertificateType[];
    algorithms: CertificateAlgorithm[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'hsm' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  generation: {
    enabled: boolean;
    methods?: {
      selfSigned?: boolean;
      caSigned?: boolean;
      imported?: boolean;
    };
    selfSigned?: {
      enabled: boolean;
      algorithm: CertificateAlgorithm;
      keySize: number;
      validity: number;
    };
    caSigned?: {
      enabled: boolean;
      ca: string;
      algorithm: CertificateAlgorithm;
      keySize: number;
      validity: number;
    };
    imported?: {
      enabled: boolean;
      format: 'pem' | 'der' | 'p12' | 'custom';
      encoding: 'hex' | 'base64' | 'utf8';
    };
  };
  validation: {
    enabled: boolean;
    methods?: {
      signature?: boolean;
      expiration?: boolean;
      chain?: boolean;
    };
    signature?: {
      enabled: boolean;
      algorithm: CertificateAlgorithm;
      hash: string;
    };
    expiration?: {
      enabled: boolean;
      threshold: number;
    };
    chain?: {
      enabled: boolean;
      ca: string;
    };
  };
  security: {
    enabled: boolean;
    features?: {
      revocation?: boolean;
      renewal?: boolean;
      backup?: boolean;
    };
    revocation?: {
      enabled: boolean;
      reason: string;
      timestamp: Date;
    };
    renewal?: {
      enabled: boolean;
      threshold: number;
      automatic: boolean;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      usage?: boolean;
      performance?: boolean;
      security?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    revoke?: boolean;
    renew?: boolean;
    backup?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Certificate {
  id: string;
  config: CertificateConfig;
  type: CertificateType;
  status: CertificateStatus;
  algorithm: CertificateAlgorithm;
  subject: {
    commonName: string;
    organization?: string;
    organizationalUnit?: string;
    locality?: string;
    state?: string;
    country?: string;
    email?: string;
  };
  issuer: {
    commonName: string;
    organization?: string;
    organizationalUnit?: string;
    locality?: string;
    state?: string;
    country?: string;
    email?: string;
  };
  serialNumber: string;
  value: string;
  publicKey: string;
  privateKey?: string;
  metadata?: {
    name?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
  security?: {
    revocation?: {
      reason?: string;
      timestamp?: Date;
    };
    renewal?: {
      previous?: string;
      next?: string;
      lastRenewed?: Date;
    };
    backup?: {
      location?: string;
      timestamp?: Date;
      checksum?: string;
    };
  };
  stats?: {
    usage: number;
    lastUsed?: Date;
    created: Date;
    expires?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  validFrom: Date;
  validTo: Date;
}

export interface CertificateLog {
  id: string;
  certificate: string;
  action: 'create' | 'update' | 'delete' | 'revoke' | 'renew' | 'backup' | 'restore' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface CertificateStats {
  total: number;
  byType: Record<CertificateType, number>;
  byStatus: Record<CertificateStatus, number>;
  byAlgorithm: Record<CertificateAlgorithm, number>;
  usage: {
    total: number;
    byCertificate?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  security: {
    revocation: {
      total: number;
      byCertificate?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    renewal: {
      total: number;
      byCertificate?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    backup: {
      total: number;
      byCertificate?: Record<string, number>;
      byDate?: Record<string, number>;
    };
  };
  performance: {
    averageResponseTime: number;
    byCertificate?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface CertificateService {
  createConfig: (config: Omit<CertificateConfig, 'id'>) => CertificateConfig;
  updateConfig: (id: string, config: Partial<CertificateConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => CertificateConfig | undefined;
  getConfigs: () => CertificateConfig[];
  create: (
    config: Omit<CertificateConfig, 'id'>,
    certificate: Omit<Certificate, 'id' | 'config'>
  ) => Promise<Certificate>;
  update: (id: string, certificate: Partial<Certificate>) => Promise<Certificate>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Certificate | undefined;
  getAll: (options?: {
    type?: CertificateType[];
    status?: CertificateStatus[];
    algorithm?: CertificateAlgorithm[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Certificate[];
  search: (
    query: string,
    options?: {
      type?: CertificateType[];
      status?: CertificateStatus[];
      algorithm?: CertificateAlgorithm[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Certificate[];
  revoke: (id: string, reason?: string) => Promise<void>;
  renew: (id: string) => Promise<{
    certificate: string;
    publicKey: string;
    privateKey?: string;
  }>;
  backup: (id: string) => Promise<{
    location: string;
    checksum: string;
  }>;
  restore: (
    id: string,
    backup: {
      location: string;
      checksum: string;
    }
  ) => Promise<void>;
  validate: (id: string) => Promise<{
    valid: boolean;
    certificate?: Certificate;
    error?: {
      code: string;
      message: string;
    };
  }>;
  getLogs: (options?: {
    certificate?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'revoke'
      | 'renew'
      | 'backup'
      | 'restore'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => CertificateLog[];
  getStats: () => CertificateStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
