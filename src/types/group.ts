export type GroupType = 'system' | 'user' | 'custom';
export type GroupStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type GroupScope = 'global' | 'user' | 'custom';

export interface GroupConfig {
  management: {
    enabled: boolean;
    types: GroupType[];
    scopes: GroupScope[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  membership: {
    enabled: boolean;
    types: ('user' | 'group' | 'custom')[];
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  authorization: {
    enabled: boolean;
    engine: 'rbac' | 'abac' | 'custom';
    roles: {
      name: string;
      permissions: string[];
      inheritance?: string[];
    }[];
    policies: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      membership?: boolean;
      authorization?: boolean;
      activity?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    membership?: boolean;
    authorization?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Group {
  id: string;
  config: GroupConfig;
  type: GroupType;
  status: GroupStatus;
  scope: GroupScope;
  name: string;
  description?: string;
  membership: {
    members: {
      type: 'user' | 'group' | 'custom';
      id: string;
      role?: string;
      joinedAt: Date;
    }[];
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  authorization: {
    roles: string[];
    permissions: string[];
    policies: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    members: number;
    activities: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface GroupLog {
  id: string;
  group: string;
  action: 'create' | 'update' | 'delete' | 'join' | 'leave' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface GroupStats {
  total: number;
  byType: Record<GroupType, number>;
  byStatus: Record<GroupStatus, number>;
  byScope: Record<GroupScope, number>;
  membership: {
    total: number;
    byType?: Record<string, number>;
    byGroup?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  authorization: {
    total: number;
    allowed: number;
    denied: number;
    byGroup?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  activity: {
    total: number;
    byType?: Record<string, number>;
    byGroup?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface GroupService {
  createConfig: (config: Omit<GroupConfig, 'id'>) => GroupConfig;
  updateConfig: (id: string, config: Partial<GroupConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => GroupConfig | undefined;
  getConfigs: () => GroupConfig[];
  create: (config: Omit<GroupConfig, 'id'>, group: Omit<Group, 'id' | 'config'>) => Promise<Group>;
  update: (id: string, group: Partial<Group>) => Promise<Group>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Group | undefined;
  getAll: (options?: {
    type?: GroupType[];
    status?: GroupStatus[];
    scope?: GroupScope[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Group[];
  search: (
    query: string,
    options?: {
      type?: GroupType[];
      status?: GroupStatus[];
      scope?: GroupScope[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Group[];
  join: (
    id: string,
    member: { type: 'user' | 'group' | 'custom'; id: string; role?: string }
  ) => Promise<void>;
  leave: (id: string, member: { type: 'user' | 'group' | 'custom'; id: string }) => Promise<void>;
  authorize: (id: string, action: string, resource: string) => Promise<boolean>;
  getLogs: (options?: {
    group?: string;
    action?: ('create' | 'update' | 'delete' | 'join' | 'leave' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => GroupLog[];
  getStats: () => GroupStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
