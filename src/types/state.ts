import { Action, AnyAction } from 'redux';
import { ThunkAction, ThunkDispatch } from 'redux-thunk';

export type StateAction = {
  type: string;
  payload?: any;
  meta?: Record<string, any>;
  error?: boolean;
};

export type StateReducer = (state: any, action: StateAction) => any;

export interface StateConfig {
  name: string;
  initialState: any;
  reducers: Record<string, StateReducer>;
  middlewares: StateMiddleware[];
  enhancers: StateEnhancer[];
  devtools: boolean;
  metadata: Record<string, any>;
}

export interface StateStore {
  getState: () => any;
  dispatch: (action: StateAction) => void;
  subscribe: (listener: () => void) => () => void;
  replaceReducer: (reducer: StateReducer) => void;
}

export type StateSelector = (state: any) => any;

export type StateSubscription = () => void;

export interface StateMiddleware {
  (store: StateStore): (next: (action: StateAction) => void) => (action: StateAction) => void;
}

export interface StateEnhancer {
  (
    createStore: (reducer: StateReducer, initialState: any) => StateStore
  ): (reducer: StateReducer, initialState: any) => StateStore;
}

export interface StateStats {
  total: number;
  byType: Record<string, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface StateService {
  dispatch: (action: StateAction) => void;
  getState: () => any;
  subscribe: (selector: StateSelector, listener: () => void) => StateSubscription;
  select: <T>(selector: StateSelector) => T;
  reset: () => void;
  replaceReducer: (reducer: StateReducer) => void;
  addMiddleware: (middleware: StateMiddleware) => void;
  removeMiddleware: (middleware: StateMiddleware) => void;
  addEnhancer: (enhancer: StateEnhancer) => void;
  removeEnhancer: (enhancer: StateEnhancer) => void;
  getStats: () => StateStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
}
