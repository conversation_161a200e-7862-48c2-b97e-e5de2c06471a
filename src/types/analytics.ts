import { PerformanceMetrics } from './performance';
import { SecurityEvent as SecurityEventType } from './security';

export type AnalyticsType = 'pageview' | 'event' | 'conversion' | 'custom';

export type AnalyticsStatus = 'active' | 'inactive' | 'error' | 'custom';

export type AnalyticsMetricType = 'count' | 'sum' | 'average' | 'min' | 'max';

export type AnalyticsDimension =
  | 'user'
  | 'session'
  | 'page'
  | 'device'
  | 'location'
  | 'referrer'
  | 'campaign'
  | 'custom';

export interface AnalyticsConfig {
  id: string;
  name: string;
  settings: {
    tracking: boolean;
    sampling: number;
    retention: number;
    privacy: boolean;
  };
  features: {
    realtime: boolean;
    segmentation: boolean;
    funnels: boolean;
    cohorts: boolean;
  };
  metadata: Record<string, any>;
}

export interface Analytics {
  id: string;
  config: AnalyticsConfig;
  type: AnalyticsType;
  status: AnalyticsStatus;
  name: string;
  description?: string;
  properties: Record<string, any>;
  user: {
    id: string;
    type: string;
    traits?: Record<string, any>;
  };
  context: {
    page?: {
      url: string;
      title?: string;
      referrer?: string;
    };
    device?: {
      type: string;
      os?: string;
      browser?: string;
    };
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
    [key: string]: any;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface AnalyticsSegment {
  id: string;
  config: AnalyticsConfig;
  name: string;
  description?: string;
  conditions: {
    property: string;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'contains' | 'startsWith' | 'endsWith';
    value: any;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface AnalyticsFunnel {
  id: string;
  config: AnalyticsConfig;
  name: string;
  description?: string;
  steps: {
    name: string;
    event: string;
    properties?: Record<string, any>;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface AnalyticsCohort {
  id: string;
  config: AnalyticsConfig;
  name: string;
  description?: string;
  segment: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface AnalyticsEvent {
  id: string;
  config: AnalyticsConfig;
  timestamp: Date;
  data: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface AnalyticsMetric {
  type: AnalyticsMetricType;
  name: string;
  value: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AnalyticsReport {
  id: string;
  config: AnalyticsConfig;
  timestamp: Date;
  summary: {
    total: number;
    byType: Record<AnalyticsType, number>;
    byDimension: Record<AnalyticsDimension, number>;
  };
  metadata?: Record<string, any>;
}

export interface AnalyticsStats {
  total: number;
  byType: Record<AnalyticsType, number>;
  byStatus: Record<AnalyticsStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface AnalyticsService {
  createConfig: (config: Omit<AnalyticsConfig, 'id'>) => AnalyticsConfig;
  updateConfig: (id: string, config: Partial<AnalyticsConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => AnalyticsConfig | undefined;
  getConfigs: () => AnalyticsConfig[];
  track: (
    analytics: Omit<Analytics, 'id' | 'config' | 'timestamp' | 'createdAt' | 'updatedAt'>
  ) => Promise<Analytics>;
  getAnalytics: (id: string) => Analytics | undefined;
  getAnalyticsList: (options?: {
    type?: AnalyticsType[];
    status?: AnalyticsStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Analytics[];
  createSegment: (segment: Omit<AnalyticsSegment, 'id' | 'config'>) => Promise<AnalyticsSegment>;
  updateSegment: (id: string, segment: Partial<AnalyticsSegment>) => Promise<AnalyticsSegment>;
  deleteSegment: (id: string) => Promise<void>;
  getSegment: (id: string) => AnalyticsSegment | undefined;
  getSegments: () => AnalyticsSegment[];
  createFunnel: (funnel: Omit<AnalyticsFunnel, 'id' | 'config'>) => Promise<AnalyticsFunnel>;
  updateFunnel: (id: string, funnel: Partial<AnalyticsFunnel>) => Promise<AnalyticsFunnel>;
  deleteFunnel: (id: string) => Promise<void>;
  getFunnel: (id: string) => AnalyticsFunnel | undefined;
  getFunnels: () => AnalyticsFunnel[];
  createCohort: (cohort: Omit<AnalyticsCohort, 'id' | 'config'>) => Promise<AnalyticsCohort>;
  updateCohort: (id: string, cohort: Partial<AnalyticsCohort>) => Promise<AnalyticsCohort>;
  deleteCohort: (id: string) => Promise<void>;
  getCohort: (id: string) => AnalyticsCohort | undefined;
  getCohorts: () => AnalyticsCohort[];
  query: (options: {
    metrics: string[];
    dimensions?: string[];
    filters?: Record<string, any>;
    startDate: Date;
    endDate: Date;
    interval?: number;
  }) => Promise<Record<string, any[]>>;
  export: (options?: {
    format: 'json' | 'csv' | 'excel';
    query?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Promise<Blob>;
  clear: (options?: {
    before?: Date;
    type?: AnalyticsType[];
    status?: AnalyticsStatus[];
  }) => Promise<void>;
  getStats: () => AnalyticsStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}

export interface PageViewEvent extends AnalyticsEvent {
  category: 'pageView';
  path: string;
  title: string;
  referrer?: string;
  search?: string;
  hash?: string;
}

export interface ClickEvent extends AnalyticsEvent {
  category: 'click';
  element: string;
  text?: string;
  href?: string;
  position?: {
    x: number;
    y: number;
  };
}

export interface ScrollEvent extends AnalyticsEvent {
  category: 'scroll';
  depth: number;
  direction: 'up' | 'down';
  speed: number;
  position: {
    x: number;
    y: number;
  };
}

export interface FormEvent extends AnalyticsEvent {
  category: 'form';
  formId: string;
  formName: string;
  fieldId: string;
  fieldName: string;
  fieldType: string;
  fieldValue: string;
  validation: {
    valid: boolean;
    errors: string[];
  };
}

export interface ErrorEvent extends AnalyticsEvent {
  category: 'error';
  error: Error;
  stack?: string;
  componentStack?: string;
  context?: Record<string, any>;
}

export interface PerformanceEvent extends AnalyticsEvent {
  category: 'performance';
  metrics: PerformanceMetrics;
}

export interface SecurityEvent extends AnalyticsEvent {
  category: 'security';
  event: SecurityEventType;
}

export interface CustomEvent extends AnalyticsEvent {
  category: 'custom';
  name: string;
  data: Record<string, any>;
}

export interface AnalyticsSession {
  id: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  events: AnalyticsEvent[];
  userProperties?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface AnalyticsMetrics {
  events: {
    total: number;
    byType: Record<AnalyticsType, number>;
    byCategory: Record<string, number>;
    byAction: Record<string, number>;
    byLabel: Record<string, number>;
    byTime: {
      lastHour: number;
      lastDay: number;
      lastWeek: number;
      lastMonth: number;
    };
  };
  sessions: {
    total: number;
    active: number;
    averageDuration: number;
    byTime: {
      lastHour: number;
      lastDay: number;
      lastWeek: number;
      lastMonth: number;
    };
  };
  users: {
    total: number;
    active: number;
    new: number;
    returning: number;
    byTime: {
      lastHour: number;
      lastDay: number;
      lastWeek: number;
      lastMonth: number;
    };
  };
  performance: {
    averageLoadTime: number;
    averageResponseTime: number;
    errorRate: number;
    bounceRate: number;
  };
  metadata?: Record<string, any>;
}
