export type BackupType = 'full' | 'incremental' | 'differential' | 'custom';

export type BackupStatus = 'pending' | 'running' | 'completed' | 'failed' | 'restored' | 'custom';

export type BackupStorage = 'local' | 's3' | 'gcs' | 'azure' | 'custom';

export interface BackupConfig {
  id: string;
  name: string;
  type: BackupType;
  storage: BackupStorage;
  schedule: string;
  retention: number;
  compression: boolean;
  encryption: boolean;
  metadata: Record<string, any>;
}

export interface Backup {
  id: string;
  config: BackupConfig;
  status: BackupStatus;
  size: number;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface BackupStats {
  total: number;
  byType: Record<BackupType, number>;
  byStatus: Record<BackupStatus, number>;
  byStorage: Record<BackupStorage, number>;
  totalSize: number;
  completed: number;
  failed: number;
  restored: number;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface BackupService {
  createConfig: (config: Omit<BackupConfig, 'id'>) => BackupConfig;
  updateConfig: (id: string, config: Partial<BackupConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => BackupConfig | undefined;
  getConfigs: () => BackupConfig[];
  create: (config: Omit<BackupConfig, 'id'>) => Promise<Backup>;
  restore: (id: string) => Promise<void>;
  delete: (id: string) => Promise<void>;
  getBackup: (id: string) => Backup | undefined;
  getBackups: () => Backup[];
  getLatest: () => Backup | undefined;
  getOldest: () => Backup | undefined;
  clear: () => Promise<void>;
  getStats: () => BackupStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
