export type ErrorType = 'system' | 'application' | 'validation' | 'security' | 'custom';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export type ErrorStatus = 'new' | 'investigating' | 'resolved' | 'closed' | 'custom';

export interface ErrorConfig {
  id: string;
  name: string;
  settings: {
    collection: {
      enabled: boolean;
      sources?: string[];
      filters?: Record<string, any>;
      sampling?: {
        enabled: boolean;
        rate?: number;
      };
    };
    processing: {
      enabled: boolean;
      grouping?: {
        enabled: boolean;
        strategy?: 'exact' | 'similar' | 'custom';
      };
      enrichment?: {
        enabled: boolean;
        sources?: string[];
      };
      deduplication?: {
        enabled: boolean;
        window?: number;
      };
    };
    notification: {
      enabled: boolean;
      channels?: {
        email?: boolean;
        slack?: boolean;
        webhook?: boolean;
      };
      rules?: {
        severity?: ErrorSeverity[];
        frequency?: number;
        cooldown?: number;
      };
    };
    storage: {
      enabled: boolean;
      ttl?: number;
      encryption?: boolean;
    };
    monitoring: {
      enabled: boolean;
      metrics?: boolean;
      alerts?: boolean;
      logging?: boolean;
    };
  };
  features: {
    tracking: boolean;
    analysis: boolean;
    reporting: boolean;
    analytics: boolean;
  };
  metadata: Record<string, any>;
}

export interface Error {
  id: string;
  config: ErrorConfig;
  type: ErrorType;
  severity: ErrorSeverity;
  status: ErrorStatus;
  name: string;
  message: string;
  stack?: string;
  code?: string;
  details?: {
    source?: string;
    component?: string;
    version?: string;
    environment?: string;
    user?: string;
    session?: string;
    request?: {
      method?: string;
      url?: string;
      headers?: Record<string, string>;
      params?: Record<string, any>;
      body?: any;
    };
    response?: {
      status?: number;
      headers?: Record<string, string>;
      body?: any;
    };
    context?: Record<string, any>;
  };
  group?: string;
  tags: string[];
  stats: {
    occurrences: number;
    users: number;
    sessions: number;
  };
  createdAt: Date;
  updatedAt: Date;
  firstSeen: Date;
  lastSeen: Date;
  resolvedAt?: Date;
  closedAt?: Date;
  metadata: Record<string, any>;
}

export interface ErrorLog {
  id: string;
  config: ErrorConfig;
  error: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: Record<string, any>;
  user?: string;
  session?: string;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface ErrorMetric {
  id: string;
  config: ErrorConfig;
  timestamp: Date;
  metrics: {
    errors: {
      total: number;
      byType: Record<ErrorType, number>;
      bySeverity: Record<ErrorSeverity, number>;
      byStatus: Record<ErrorStatus, number>;
    };
    impact: {
      affectedUsers: number;
      affectedSessions: number;
      mttr: number;
    };
    performance: {
      responseTime: number;
      errorRate: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface ErrorStats {
  total: number;
  byType: Record<ErrorType, number>;
  bySeverity: Record<ErrorSeverity, number>;
  byStatus: Record<ErrorStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  impact: {
    affectedUsers: number;
    affectedSessions: number;
    averageMttr: number;
  };
  metadata: Record<string, any>;
}

export interface ErrorService {
  createConfig: (config: Omit<ErrorConfig, 'id'>) => ErrorConfig;
  updateConfig: (id: string, config: Partial<ErrorConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ErrorConfig | undefined;
  getConfigs: () => ErrorConfig[];
  track: (config: Omit<ErrorConfig, 'id'>, error: Omit<Error, 'id' | 'config'>) => Promise<Error>;
  update: (id: string, error: Partial<Error>) => Promise<Error>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Error | undefined;
  getAll: (options?: {
    type?: ErrorType[];
    severity?: ErrorSeverity[];
    status?: ErrorStatus[];
    tags?: string[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Error[];
  resolve: (id: string, resolution?: string) => Promise<Error>;
  close: (id: string) => Promise<Error>;
  reopen: (id: string) => Promise<Error>;
  getLogs: (options?: {
    error?: string;
    level?: ('info' | 'warning' | 'error')[];
    user?: string;
    session?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ErrorLog[];
  getMetrics: (options?: {
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ErrorMetric[];
  analyze: (id: string) => Promise<{
    patterns?: string[];
    correlations?: string[];
    recommendations?: string[];
  }>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => ErrorStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
