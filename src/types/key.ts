export type KeyType = 'symmetric' | 'asymmetric' | 'hmac' | 'custom';
export type KeyStatus = 'active' | 'expired' | 'revoked' | 'archived';
export type KeyAlgorithm = 'AES' | 'RSA' | 'ECC' | 'HMAC' | 'custom';

export interface KeyConfig {
  management: {
    enabled: boolean;
    types: KeyType[];
    algorithms: KeyAlgorithm[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'hsm' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  generation: {
    enabled: boolean;
    methods?: {
      random?: boolean;
      derived?: boolean;
      imported?: boolean;
    };
    random?: {
      enabled: boolean;
      length: number;
      encoding: 'hex' | 'base64' | 'utf8';
    };
    derived?: {
      enabled: boolean;
      algorithm: string;
      salt: string;
      iterations: number;
      length: number;
    };
    imported?: {
      enabled: boolean;
      format: 'pem' | 'der' | 'jwk' | 'custom';
      encoding: 'hex' | 'base64' | 'utf8';
    };
  };
  rotation: {
    enabled: boolean;
    interval: number;
    overlap: number;
    method: 'manual' | 'automatic' | 'custom';
  };
  security: {
    enabled: boolean;
    features?: {
      encryption?: boolean;
      signing?: boolean;
      verification?: boolean;
    };
    encryption?: {
      enabled: boolean;
      algorithm: string;
      mode: string;
      padding: string;
    };
    signing?: {
      enabled: boolean;
      algorithm: string;
      hash: string;
    };
    verification?: {
      enabled: boolean;
      algorithm: string;
      hash: string;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      usage?: boolean;
      performance?: boolean;
      security?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    rotate?: boolean;
    revoke?: boolean;
    backup?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Key {
  id: string;
  config: KeyConfig;
  type: KeyType;
  status: KeyStatus;
  algorithm: KeyAlgorithm;
  value: string;
  publicKey?: string;
  privateKey?: string;
  metadata?: {
    name?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
  security?: {
    rotation?: {
      previous?: string;
      next?: string;
      lastRotated?: Date;
    };
    revocation?: {
      reason?: string;
      timestamp?: Date;
    };
    backup?: {
      location?: string;
      timestamp?: Date;
      checksum?: string;
    };
  };
  stats?: {
    usage: number;
    lastUsed?: Date;
    created: Date;
    expires?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
}

export interface KeyLog {
  id: string;
  key: string;
  action: 'create' | 'update' | 'delete' | 'rotate' | 'revoke' | 'backup' | 'restore' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface KeyStats {
  total: number;
  byType: Record<KeyType, number>;
  byStatus: Record<KeyStatus, number>;
  byAlgorithm: Record<KeyAlgorithm, number>;
  usage: {
    total: number;
    byKey?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  security: {
    rotation: {
      total: number;
      byKey?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    revocation: {
      total: number;
      byKey?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    backup: {
      total: number;
      byKey?: Record<string, number>;
      byDate?: Record<string, number>;
    };
  };
  performance: {
    averageResponseTime: number;
    byKey?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface KeyService {
  createConfig: (config: Omit<KeyConfig, 'id'>) => KeyConfig;
  updateConfig: (id: string, config: Partial<KeyConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => KeyConfig | undefined;
  getConfigs: () => KeyConfig[];
  create: (config: Omit<KeyConfig, 'id'>, key: Omit<Key, 'id' | 'config'>) => Promise<Key>;
  update: (id: string, key: Partial<Key>) => Promise<Key>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Key | undefined;
  getAll: (options?: {
    type?: KeyType[];
    status?: KeyStatus[];
    algorithm?: KeyAlgorithm[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Key[];
  search: (
    query: string,
    options?: {
      type?: KeyType[];
      status?: KeyStatus[];
      algorithm?: KeyAlgorithm[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Key[];
  rotate: (id: string) => Promise<{
    key: string;
    publicKey?: string;
    privateKey?: string;
  }>;
  revoke: (id: string, reason?: string) => Promise<void>;
  backup: (id: string) => Promise<{
    location: string;
    checksum: string;
  }>;
  restore: (
    id: string,
    backup: {
      location: string;
      checksum: string;
    }
  ) => Promise<void>;
  encrypt: (id: string, data: string) => Promise<string>;
  decrypt: (id: string, data: string) => Promise<string>;
  sign: (id: string, data: string) => Promise<string>;
  verify: (id: string, data: string, signature: string) => Promise<boolean>;
  getLogs: (options?: {
    key?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'rotate'
      | 'revoke'
      | 'backup'
      | 'restore'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => KeyLog[];
  getStats: () => KeyStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
