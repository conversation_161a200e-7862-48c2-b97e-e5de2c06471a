export type DocumentType = 'text' | 'image' | 'video' | 'audio' | 'custom';
export type DocumentStatus = 'draft' | 'published' | 'archived';
export type DocumentFormat =
  | 'pdf'
  | 'doc'
  | 'docx'
  | 'txt'
  | 'jpg'
  | 'png'
  | 'mp4'
  | 'mp3'
  | 'custom';

export interface DocumentConfig {
  management: {
    enabled: boolean;
    types: DocumentType[];
    formats: DocumentFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  processing: {
    enabled: boolean;
    engine: 'local' | 'cloud' | 'custom';
    timeout: number;
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  conversion: {
    enabled: boolean;
    formats: {
      from: DocumentFormat;
      to: DocumentFormat;
      engine: 'local' | 'cloud' | 'custom';
      options?: Record<string, any>;
    }[];
  };
  indexing: {
    enabled: boolean;
    engine: 'elasticsearch' | 'lucene' | 'custom';
    fields: {
      name: string;
      type: 'text' | 'keyword' | 'date' | 'number' | 'boolean' | 'custom';
      analyzer?: string;
      options?: Record<string, any>;
    }[];
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      storage?: boolean;
      processing?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    versioning?: boolean;
    conversion?: boolean;
    indexing?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Document {
  id: string;
  config: DocumentConfig;
  type: DocumentType;
  status: DocumentStatus;
  name: string;
  description?: string;
  format: DocumentFormat;
  content: {
    type: 'text' | 'binary' | 'url' | 'custom';
    value: string | Buffer | URL;
    size: number;
    hash: string;
  };
  metadata?: {
    author?: {
      id: string;
      name?: string;
      email?: string;
    };
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    published?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  storage?: {
    path: string;
    url?: string;
    provider?: string;
    region?: string;
    bucket?: string;
  };
  processing?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  conversion?: {
    formats: {
      from: DocumentFormat;
      to: DocumentFormat;
      status: 'pending' | 'processing' | 'completed' | 'failed';
      result?: {
        path: string;
        url?: string;
        size: number;
        hash: string;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    }[];
  };
  indexing?: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    fields: Record<string, any>;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  stats?: {
    size: number;
    views: number;
    downloads: number;
    conversions: number;
  };
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  archivedAt?: Date;
  expiresAt?: Date;
}

export interface DocumentLog {
  id: string;
  document: string;
  action: 'create' | 'update' | 'delete' | 'publish' | 'archive' | 'convert' | 'index' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface DocumentStats {
  total: number;
  byType: Record<DocumentType, number>;
  byStatus: Record<DocumentStatus, number>;
  byFormat: Record<DocumentFormat, number>;
  storage: {
    total: number;
    byType?: Record<DocumentType, number>;
    byFormat?: Record<DocumentFormat, number>;
    byDate?: Record<string, number>;
  };
  processing: {
    total: number;
    success: number;
    failure: number;
    byDocument?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageSize: number;
    averageProcessingTime: number;
    byDocument?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface DocumentService {
  createConfig: (config: Omit<DocumentConfig, 'id'>) => DocumentConfig;
  updateConfig: (id: string, config: Partial<DocumentConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => DocumentConfig | undefined;
  getConfigs: () => DocumentConfig[];
  create: (
    config: Omit<DocumentConfig, 'id'>,
    document: Omit<Document, 'id' | 'config'>
  ) => Promise<Document>;
  update: (id: string, document: Partial<Document>) => Promise<Document>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Document | undefined;
  getAll: (options?: {
    type?: DocumentType[];
    status?: DocumentStatus[];
    format?: DocumentFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Document[];
  search: (
    query: string,
    options?: {
      type?: DocumentType[];
      status?: DocumentStatus[];
      format?: DocumentFormat[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Document[];
  publish: (id: string, user: Document['metadata']['published']['user']) => Promise<void>;
  archive: (id: string, user: Document['metadata']['modified']['user']) => Promise<void>;
  convert: (id: string, format: DocumentFormat) => Promise<void>;
  index: (id: string) => Promise<void>;
  getLogs: (options?: {
    document?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'publish'
      | 'archive'
      | 'convert'
      | 'index'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DocumentLog[];
  getStats: () => DocumentStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
