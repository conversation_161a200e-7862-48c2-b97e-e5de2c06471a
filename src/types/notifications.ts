import { Transition } from 'framer-motion';
import { ReactNode } from 'react';

import { AppConfig } from './core';

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'custom';
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type NotificationPosition =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'top-center'
  | 'bottom-center';
export type NotificationLayout = 'stack' | 'grid' | 'list' | 'carousel';
export type NotificationBehavior = 'stack' | 'replace' | 'queue' | 'clear';
export type NotificationAccessibility =
  | 'default'
  | 'high-contrast'
  | 'reduced-motion'
  | 'screen-reader';

export interface NotificationAction {
  label: string;
  onClick: () => Promise<void> | void;
  type?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info';
  icon?: ReactNode;
  shortcut?: string;
  disabled?: boolean;
  loading?: boolean;
  success?: boolean;
  error?: boolean;
  theme?: NotificationActionTheme;
  animation?: NotificationActionAnimation;
  sound?: NotificationActionSound;
}

export interface NotificationActionTheme {
  background?: string;
  text?: string;
  icon?: string;
  border?: string;
  shadow?: string;
  borderRadius?: number;
  padding?: number;
  fontSize?: string;
  fontWeight?: number;
  shortcut?: string;
  shortcutSize?: string;
  success?: string;
  error?: string;
  loading?: string;
  hover?: {
    background?: string;
    text?: string;
    icon?: string;
    border?: string;
    shadow?: string;
  };
  active?: {
    background?: string;
    text?: string;
    icon?: string;
    border?: string;
    shadow?: string;
  };
}

export interface NotificationActionAnimation {
  initial?: Record<string, number>;
  animate?: Record<string, number>;
  hover?: Record<string, number>;
  tap?: Record<string, number>;
  loading?: Record<string, number>;
  success?: Record<string, number>;
  error?: Record<string, number>;
  icon?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
  };
}

export interface NotificationActionSound {
  click?: string;
  hover?: string;
  success?: string;
  error?: string;
}

export interface NotificationTheme {
  background?: string;
  text?: string;
  border?: string;
  shadow?: string;
  borderRadius?: number;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: number;
  lineHeight?: number;
  letterSpacing?: number;
  transition?: string;
  zIndex?: number;
  icon?: NotificationIconTheme;
  content?: NotificationContentTheme;
  header?: NotificationHeaderTheme;
  action?: NotificationActionTheme;
}

export interface NotificationIconTheme {
  color?: string;
  background?: string;
  size?: number;
  borderRadius?: number;
  border?: string;
  shadow?: string;
  hoverShadow?: string;
  hoverEffect?: string;
  tapEffect?: string;
  glow?: string;
  pulse?: string;
  rotate?: number;
  scale?: number;
  filter?: string;
  transition?: string;
  animation?: {
    duration?: number;
    easing?: string;
    delay?: number;
  };
}

export interface NotificationContentTheme {
  background?: string;
  text?: string;
  title?: string;
  message?: string;
  icon?: string;
  iconBackground?: string;
  iconSize?: number;
  border?: string;
  borderRadius?: number;
  padding?: number;
  shadow?: string;
  titleWeight?: number;
  progressHeight?: number;
  progressBackground?: string;
  progressFill?: string;
  progressBorderRadius?: number;
  progressGlow?: string;
  animation?: {
    duration?: number;
    easing?: string;
    delay?: number;
  };
}

export interface NotificationHeaderTheme {
  background?: string;
  text?: string;
  icon?: string;
  border?: string;
  shadow?: string;
  animation?: {
    duration?: number;
    easing?: string;
    delay?: number;
  };
}

export interface NotificationAnimation {
  initial?: Record<string, number>;
  animate?: Record<string, number>;
  exit?: Record<string, number>;
  hover?: Record<string, number>;
  tap?: Record<string, number>;
  transition?: Transition;
  expand?: Record<string, number>;
  collapse?: Record<string, number>;
  drag?: {
    start?: Record<string, number>;
    end?: Record<string, number>;
  };
  reorder?: Record<string, number>;
  content?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    exit?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
  };
  icon?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
    pulse?: Record<string, number>;
    rotate?: Record<string, number>;
    glow?: Record<string, number>;
  };
  action?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
    loading?: Record<string, number>;
    success?: Record<string, number>;
    error?: Record<string, number>;
  };
  progress?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    exit?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
    complete?: Record<string, number>;
  };
}

export interface NotificationSound {
  hover?: string;
  tap?: string;
  expand?: string;
  collapse?: string;
  remove?: string;
  actionSound?: string;
  reorder?: string;
  drag?: string;
  content?: {
    show?: string;
    hide?: string;
    hover?: string;
    tap?: string;
    update?: string;
    complete?: string;
  };
  icon?: {
    hover?: string;
    tap?: string;
    pulse?: string;
    rotate?: string;
  };
  action?: {
    click?: string;
    hover?: string;
    success?: string;
    error?: string;
  };
  progress?: {
    start?: string;
    update?: string;
    complete?: string;
    pause?: string;
    resume?: string;
  };
  group?: {
    expand?: string;
    collapse?: string;
    drag?: string;
    reorder?: string;
  };
}

export interface Notification {
  id: string;
  title: string;
  message?: string;
  type: NotificationType;
  priority: NotificationPriority;
  position: NotificationPosition;
  duration?: number;
  icon?: ReactNode;
  actions?: NotificationAction[];
  onAction?: (action: NotificationAction) => void;
  group?: string;
  theme?: NotificationTheme;
  animation?: NotificationAnimation;
  sound?: NotificationSound;
  read?: boolean;
  createdAt?: number;
  vibration?: number[];
  progress?: number;
  customData?: Record<string, any>;
  allowHTML?: boolean;
  accessibility?: NotificationAccessibility;
  behavior?: NotificationBehavior;
  layout?: NotificationLayout;
}

export interface NotificationGroup {
  id: string;
  notifications: Notification[];
  type: NotificationType;
  position: NotificationPosition;
  isExpanded: boolean;
  theme?: NotificationTheme;
  sound?: NotificationSound;
  animation?: NotificationAnimation;
  actions?: NotificationAction[];
  icon?: {
    component: ReactNode;
    theme?: {
      color?: string;
      background?: string;
      size?: number;
    };
    animation?: {
      initial?: Record<string, number>;
      animate?: Record<string, number>;
      hover?: Record<string, number>;
      tap?: Record<string, number>;
    };
  };
  behavior?: NotificationBehavior;
  layout?: NotificationLayout;
  accessibility?: NotificationAccessibility;
}

export interface NotificationState {
  notifications: Notification[];
  groups: NotificationGroup[];
  settings: {
    maxNotifications: number;
    maxGroups: number;
    defaultDuration: number;
    defaultPosition: NotificationPosition;
    defaultType: NotificationType;
    defaultPriority: NotificationPriority;
    defaultTheme: NotificationTheme;
    defaultAnimation: NotificationAnimation;
    defaultSound: NotificationSound;
    defaultBehavior: NotificationBehavior;
    defaultLayout: NotificationLayout;
    defaultAccessibility: NotificationAccessibility;
  };
}

export interface NotificationContextType {
  notifications: Notification[];
  groups: NotificationGroup[];
  settings: NotificationState['settings'];
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => void;
  removeNotification: (id: string) => void;
  updateNotification: (id: string, notification: Partial<Notification>) => void;
  clearNotifications: () => void;
  addGroup: (group: Omit<NotificationGroup, 'id'>) => void;
  removeGroup: (id: string) => void;
  updateGroup: (id: string, group: Partial<NotificationGroup>) => void;
  clearGroups: () => void;
  updateSettings: (settings: Partial<NotificationState['settings']>) => void;
}

export interface NotificationConfig extends AppConfig {
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    position: NotificationPosition;
    duration: number;
    maxVisible: number;
    grouping: boolean;
    priority: NotificationPriority;
    actions: boolean;
    history: boolean;
    historyLimit: number;
    theme: NotificationTheme;
    animation: NotificationAnimation;
    behavior: NotificationBehavior;
    layout: NotificationLayout;
    accessibility: NotificationAccessibility;
  };
}
