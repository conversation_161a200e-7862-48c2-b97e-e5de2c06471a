export type PolicyType = 'rbac' | 'abac' | 'custom';
export type PolicyStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type PolicyScope = 'global' | 'user' | 'group' | 'resource' | 'custom';

export interface PolicyConfig {
  management: {
    enabled: boolean;
    types: PolicyType[];
    scopes: PolicyScope[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  rules: {
    enabled: boolean;
    engine: 'rbac' | 'abac' | 'custom';
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  enforcement: {
    enabled: boolean;
    mode: 'strict' | 'permissive' | 'custom';
    cache?: {
      enabled: boolean;
      ttl: number;
      maxSize: number;
    };
    audit?: {
      enabled: boolean;
      level: 'none' | 'basic' | 'detailed';
      storage: 'database' | 'file' | 'custom';
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      storage?: boolean;
      rules?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    rules?: boolean;
    enforcement?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Policy {
  id: string;
  config: PolicyConfig;
  type: PolicyType;
  status: PolicyStatus;
  scope: PolicyScope;
  name: string;
  description?: string;
  rules: {
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  enforcement: {
    mode: 'strict' | 'permissive' | 'custom';
    cache?: {
      hits: number;
      misses: number;
      lastUpdated: Date;
    };
    audit?: {
      logs: {
        id: string;
        action: string;
        resource: string;
        user: string;
        result: 'allow' | 'deny';
        timestamp: Date;
        details?: Record<string, any>;
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  storage?: {
    path: string;
    url?: string;
    provider?: string;
    region?: string;
    bucket?: string;
  };
  stats?: {
    evaluations: number;
    allows: number;
    denials: number;
    cache: {
      hits: number;
      misses: number;
    };
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface PolicyLog {
  id: string;
  policy: string;
  action: 'create' | 'update' | 'delete' | 'evaluate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface PolicyStats {
  total: number;
  byType: Record<PolicyType, number>;
  byStatus: Record<PolicyStatus, number>;
  byScope: Record<PolicyScope, number>;
  storage: {
    total: number;
    byType?: Record<PolicyType, number>;
    byScope?: Record<PolicyScope, number>;
    byDate?: Record<string, number>;
  };
  rules: {
    total: number;
    allowed: number;
    denied: number;
    byPolicy?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byPolicy?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface PolicyService {
  createConfig: (config: Omit<PolicyConfig, 'id'>) => PolicyConfig;
  updateConfig: (id: string, config: Partial<PolicyConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => PolicyConfig | undefined;
  getConfigs: () => PolicyConfig[];
  create: (
    config: Omit<PolicyConfig, 'id'>,
    policy: Omit<Policy, 'id' | 'config'>
  ) => Promise<Policy>;
  update: (id: string, policy: Partial<Policy>) => Promise<Policy>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Policy | undefined;
  getAll: (options?: {
    type?: PolicyType[];
    status?: PolicyStatus[];
    scope?: PolicyScope[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Policy[];
  search: (
    query: string,
    options?: {
      type?: PolicyType[];
      status?: PolicyStatus[];
      scope?: PolicyScope[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Policy[];
  evaluate: (id: string, user: string, resource: string, action: string) => Promise<boolean>;
  getLogs: (options?: {
    policy?: string;
    action?: ('create' | 'update' | 'delete' | 'evaluate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => PolicyLog[];
  getStats: () => PolicyStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
