export type QueueType = 'system' | 'user' | 'priority' | 'custom';
export type QueueStatus = 'active' | 'paused' | 'drained' | 'archived';
export type QueuePriority = 'low' | 'normal' | 'high' | 'urgent';

export interface QueueConfig {
  management: {
    enabled: boolean;
    types: QueueType[];
    priorities: QueuePriority[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'redis' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  processing: {
    enabled: boolean;
    concurrency?: {
      enabled: boolean;
      max: number;
      perType?: Record<QueueType, number>;
    };
    timeout?: {
      enabled: boolean;
      default: number;
      perType?: Record<QueueType, number>;
    };
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  scheduling: {
    enabled: boolean;
    engine: 'fifo' | 'lifo' | 'priority' | 'custom';
    options?: {
      fifo?: {
        strict: boolean;
        timeout: number;
      };
      lifo?: {
        strict: boolean;
        timeout: number;
      };
      priority?: {
        strict: boolean;
        timeout: number;
        weights?: Record<QueuePriority, number>;
      };
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      processing?: boolean;
      performance?: boolean;
      resource?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    scheduling?: boolean;
    batching?: boolean;
    deadLetter?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Queue {
  id: string;
  config: QueueConfig;
  type: QueueType;
  status: QueueStatus;
  name: string;
  description?: string;
  handler: {
    type: 'function' | 'script' | 'command' | 'custom';
    value: string;
    options?: Record<string, any>;
  };
  processing?: {
    concurrency: number;
    timeout: number;
    retry: {
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit: {
      window: number;
      max: number;
    };
  };
  scheduling?: {
    engine: QueueConfig['scheduling']['engine'];
    options?: QueueConfig['scheduling']['options'];
  };
  deadLetter?: {
    enabled: boolean;
    queue: string;
    maxAttempts: number;
  };
  batching?: {
    enabled: boolean;
    size: number;
    timeout: number;
  };
  resources?: {
    cpu?: number;
    memory?: number;
    disk?: number;
    network?: number;
  };
  stats?: {
    size: number;
    processed: number;
    failed: number;
    retried: number;
    deadLetter: number;
    duration: number;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface QueueItem {
  id: string;
  queue: string;
  type: QueueType;
  priority: QueuePriority;
  data: Record<string, any>;
  processing?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  stats?: {
    attempts: number;
    success: number;
    failure: number;
    duration: number;
  };
  createdAt: Date;
  updatedAt: Date;
  scheduledFor?: Date;
  processedAt?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface QueueLog {
  id: string;
  queue: string;
  item?: string;
  action:
    | 'create'
    | 'update'
    | 'delete'
    | 'process'
    | 'complete'
    | 'fail'
    | 'retry'
    | 'deadLetter'
    | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface QueueStats {
  total: number;
  byType: Record<QueueType, number>;
  byStatus: Record<QueueStatus, number>;
  processing: {
    total: number;
    success: number;
    failure: number;
    byQueue?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageDuration: number;
    byQueue?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    byQueue?: Record<
      string,
      {
        cpu: number;
        memory: number;
        disk: number;
        network: number;
      }
    >;
    byDate?: Record<
      string,
      {
        cpu: number;
        memory: number;
        disk: number;
        network: number;
      }
    >;
  };
}

export interface QueueService {
  createConfig: (config: Omit<QueueConfig, 'id'>) => QueueConfig;
  updateConfig: (id: string, config: Partial<QueueConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => QueueConfig | undefined;
  getConfigs: () => QueueConfig[];
  create: (config: Omit<QueueConfig, 'id'>, queue: Omit<Queue, 'id' | 'config'>) => Promise<Queue>;
  update: (id: string, queue: Partial<Queue>) => Promise<Queue>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Queue | undefined;
  getAll: (options?: {
    type?: QueueType[];
    status?: QueueStatus[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Queue[];
  search: (
    query: string,
    options?: {
      type?: QueueType[];
      status?: QueueStatus[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Queue[];
  pause: (id: string) => Promise<void>;
  resume: (id: string) => Promise<void>;
  drain: (id: string) => Promise<void>;
  push: (id: string, item: Omit<QueueItem, 'id' | 'queue'>) => Promise<QueueItem>;
  pop: (id: string) => Promise<QueueItem | undefined>;
  peek: (id: string) => Promise<QueueItem | undefined>;
  getItems: (
    id: string,
    options?: {
      type?: QueueType[];
      priority?: QueuePriority[];
      search?: string;
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => QueueItem[];
  searchItems: (
    id: string,
    query: string,
    options?: {
      type?: QueueType[];
      priority?: QueuePriority[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => QueueItem[];
  getLogs: (options?: {
    queue?: string;
    item?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'process'
      | 'complete'
      | 'fail'
      | 'retry'
      | 'deadLetter'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => QueueLog[];
  getStats: () => QueueStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
