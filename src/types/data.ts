export type DataType = 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array' | 'custom';

export type DataStatus = 'active' | 'inactive' | 'error' | 'custom';

export interface DataConfig {
  id: string;
  name: string;
  settings: {
    type: DataType;
    validation: {
      required: boolean;
      unique: boolean;
      min?: number;
      max?: number;
      pattern?: string;
      enum?: any[];
    };
    transformation: {
      trim: boolean;
      lowercase: boolean;
      uppercase: boolean;
      format?: string;
    };
    storage: {
      type: 'memory' | 'file' | 'database';
      path?: string;
      connection?: Record<string, any>;
    };
  };
  features: {
    versioning: boolean;
    encryption: boolean;
    compression: boolean;
    backup: boolean;
  };
  metadata: Record<string, any>;
}

export interface DataSchema {
  id: string;
  config: DataConfig;
  type: DataType;
  status: DataStatus;
  name: string;
  description?: string;
  fields: DataField[];
  indexes: DataIndex[];
  constraints: DataConstraint[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataField {
  id: string;
  schema: string;
  name: string;
  type: DataType;
  description?: string;
  validation?: {
    required: boolean;
    unique: boolean;
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
  transformation?: {
    trim: boolean;
    lowercase: boolean;
    uppercase: boolean;
    format?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataIndex {
  id: string;
  schema: string;
  name: string;
  type: string;
  description?: string;
  fields: string[];
  unique: boolean;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataConstraint {
  id: string;
  schema: string;
  name: string;
  type: string;
  description?: string;
  fields: string[];
  condition: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataRecord {
  id: string;
  schema: string;
  fields: Record<string, any>;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataQuery {
  id: string;
  schema: string;
  type: string;
  fields?: string[];
  filters?: Record<string, any>;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  }[];
  page?: {
    number: number;
    size: number;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataLog {
  id: string;
  schema: string;
  record?: string;
  query?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface DataStats {
  total: number;
  byType: Record<DataType, number>;
  byStatus: Record<DataStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface DataService {
  createConfig: (config: Omit<DataConfig, 'id'>) => DataConfig;
  updateConfig: (id: string, config: Partial<DataConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => DataConfig | undefined;
  getConfigs: () => DataConfig[];
  createSchema: (
    config: Omit<DataConfig, 'id'>,
    schema: Omit<DataSchema, 'id' | 'config'>
  ) => Promise<DataSchema>;
  updateSchema: (id: string, schema: Partial<DataSchema>) => Promise<DataSchema>;
  deleteSchema: (id: string) => Promise<void>;
  getSchema: (id: string) => DataSchema | undefined;
  getSchemas: (options?: {
    type?: DataType[];
    status?: DataStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DataSchema[];
  createField: (schema: string, field: Omit<DataField, 'id' | 'schema'>) => Promise<DataField>;
  updateField: (id: string, field: Partial<DataField>) => Promise<DataField>;
  deleteField: (id: string) => Promise<void>;
  getField: (id: string) => DataField | undefined;
  getFields: (options?: {
    schema?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DataField[];
  createIndex: (schema: string, index: Omit<DataIndex, 'id' | 'schema'>) => Promise<DataIndex>;
  updateIndex: (id: string, index: Partial<DataIndex>) => Promise<DataIndex>;
  deleteIndex: (id: string) => Promise<void>;
  getIndex: (id: string) => DataIndex | undefined;
  getIndexes: (options?: {
    schema?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DataIndex[];
  createConstraint: (
    schema: string,
    constraint: Omit<DataConstraint, 'id' | 'schema'>
  ) => Promise<DataConstraint>;
  updateConstraint: (id: string, constraint: Partial<DataConstraint>) => Promise<DataConstraint>;
  deleteConstraint: (id: string) => Promise<void>;
  getConstraint: (id: string) => DataConstraint | undefined;
  getConstraints: (options?: {
    schema?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DataConstraint[];
  createRecord: (schema: string, record: Omit<DataRecord, 'id' | 'schema'>) => Promise<DataRecord>;
  updateRecord: (id: string, record: Partial<DataRecord>) => Promise<DataRecord>;
  deleteRecord: (id: string) => Promise<void>;
  getRecord: (id: string) => DataRecord | undefined;
  getRecords: (options?: {
    schema?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DataRecord[];
  query: (schema: string, query: Omit<DataQuery, 'id' | 'schema'>) => Promise<DataRecord[]>;
  getLogs: (options?: {
    schema?: string;
    record?: string;
    query?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DataLog[];
  getStats: () => DataStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
