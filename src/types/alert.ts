export type AlertType = 'system' | 'security' | 'performance' | 'custom';
export type AlertStatus = 'active' | 'acknowledged' | 'resolved' | 'archived';
export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface AlertConfig {
  management: {
    enabled: boolean;
    types: AlertType[];
    severities: AlertSeverity[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'timeseries' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  detection: {
    enabled: boolean;
    rules: {
      name: string;
      type: AlertType;
      severity: AlertSeverity;
      condition: {
        field: string;
        operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'custom';
        value: any;
      };
      window: number;
      threshold: number;
      actions: {
        type: 'notification' | 'webhook' | 'command' | 'custom';
        config: Record<string, any>;
      }[];
    }[];
  };
  notification: {
    enabled: boolean;
    channels: {
      email?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      sms?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      push?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      webhook?: {
        enabled: boolean;
        endpoints: {
          url: string;
          method: string;
          headers?: Record<string, string>;
          timeout?: number;
          retry?: number;
        }[];
      };
    };
    templates: {
      name: string;
      type: AlertType;
      severity: AlertSeverity;
      subject?: string;
      body: string;
      variables?: string[];
    }[];
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      detection?: boolean;
      notification?: boolean;
      resolution?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    correlation?: boolean;
    suppression?: boolean;
    escalation?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Alert {
  id: string;
  config: AlertConfig;
  type: AlertType;
  status: AlertStatus;
  severity: AlertSeverity;
  name: string;
  description?: string;
  source: {
    type: 'system' | 'application' | 'service' | 'custom';
    id: string;
    name?: string;
  };
  condition: {
    field: string;
    operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'custom';
    value: any;
    window: number;
    threshold: number;
  };
  detection?: {
    value: number;
    timestamp: Date;
    details?: Record<string, any>;
  };
  notification?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  resolution?: {
    timestamp: Date;
    user?: {
      id: string;
      name?: string;
      email?: string;
    };
    comment?: string;
  };
  correlation?: {
    alerts: string[];
    pattern: string;
  };
  suppression?: {
    enabled: boolean;
    start: Date;
    end: Date;
    reason?: string;
  };
  escalation?: {
    level: number;
    threshold: number;
    actions: {
      type: 'notification' | 'webhook' | 'command' | 'custom';
      config: Record<string, any>;
    }[];
  };
  stats?: {
    detections: number;
    notifications: number;
    resolutions: number;
  };
  createdAt: Date;
  updatedAt: Date;
  detectedAt?: Date;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
}

export interface AlertLog {
  id: string;
  alert: string;
  action:
    | 'create'
    | 'update'
    | 'delete'
    | 'detect'
    | 'notify'
    | 'acknowledge'
    | 'resolve'
    | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface AlertStats {
  total: number;
  byType: Record<AlertType, number>;
  byStatus: Record<AlertStatus, number>;
  bySeverity: Record<AlertSeverity, number>;
  detection: {
    total: number;
    success: number;
    failure: number;
    byAlert?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  notification: {
    total: number;
    success: number;
    failure: number;
    byAlert?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  resolution: {
    total: number;
    average: number;
    byAlert?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface AlertService {
  createConfig: (config: Omit<AlertConfig, 'id'>) => AlertConfig;
  updateConfig: (id: string, config: Partial<AlertConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => AlertConfig | undefined;
  getConfigs: () => AlertConfig[];
  create: (config: Omit<AlertConfig, 'id'>, alert: Omit<Alert, 'id' | 'config'>) => Promise<Alert>;
  update: (id: string, alert: Partial<Alert>) => Promise<Alert>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Alert | undefined;
  getAll: (options?: {
    type?: AlertType[];
    status?: AlertStatus[];
    severity?: AlertSeverity[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Alert[];
  search: (
    query: string,
    options?: {
      type?: AlertType[];
      status?: AlertStatus[];
      severity?: AlertSeverity[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Alert[];
  detect: (id: string) => Promise<void>;
  notify: (id: string) => Promise<void>;
  acknowledge: (id: string, user: Alert['resolution']['user'], comment?: string) => Promise<void>;
  resolve: (id: string, user: Alert['resolution']['user'], comment?: string) => Promise<void>;
  suppress: (id: string, start: Date, end: Date, reason?: string) => Promise<void>;
  escalate: (id: string) => Promise<void>;
  getLogs: (options?: {
    alert?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'detect'
      | 'notify'
      | 'acknowledge'
      | 'resolve'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => AlertLog[];
  getStats: () => AlertStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
