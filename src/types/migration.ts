export type MigrationType = 'schema' | 'data' | 'config' | 'plugin' | 'custom';

export type MigrationStatus =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'rolledback'
  | 'custom';

export interface MigrationConfig {
  id: string;
  name: string;
  type: MigrationType;
  version: string;
  description: string;
  dependencies: string[];
  metadata: Record<string, any>;
}

export interface Migration {
  id: string;
  config: MigrationConfig;
  status: MigrationStatus;
  startedAt?: Date;
  completedAt?: Date;
  rolledbackAt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface MigrationStats {
  total: number;
  byType: Record<MigrationType, number>;
  byStatus: Record<MigrationStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface MigrationService {
  createConfig: (config: Omit<MigrationConfig, 'id'>) => MigrationConfig;
  updateConfig: (id: string, config: Partial<MigrationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => MigrationConfig | undefined;
  getConfigs: () => MigrationConfig[];
  run: (config: Omit<MigrationConfig, 'id'>) => Promise<Migration>;
  rollback: (id: string) => Promise<void>;
  getMigration: (id: string) => Migration | undefined;
  getMigrations: () => Migration[];
  clear: () => Promise<void>;
  getStats: () => MigrationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
