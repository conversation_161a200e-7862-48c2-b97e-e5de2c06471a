import { <PERSON><PERSON><PERSON>Window } from 'electron';
import { z } from 'zod';

import { AppConfig } from '../config/app.config';
import {
  CACHE_KEYS,
  ERROR_TYPES,
  EVENT_TYPES,
  EXTENSION_CATEGORIES,
  EXTENSION_PERMISSIONS,
  FEATURES,
  FILE_TYPES,
  HTTP_STATUS,
  LANGUAGES,
  LIMITS,
  MIME_TYPES,
  REGEX,
  SHORTCUTS,
  STORAGE_KEYS,
  THEME_TYPES,
  TIMEOUTS,
} from '../constants';

// Application Types
export type AppEnvironment = 'development' | 'production' | 'test';
export type Platform = 'win32' | 'darwin' | 'linux';
export type Architecture = 'x64' | 'arm64';

// Extension Types
export type ExtensionCategory = (typeof EXTENSION_CATEGORIES)[keyof typeof EXTENSION_CATEGORIES];
export type ExtensionPermission =
  (typeof EXTENSION_PERMISSIONS)[keyof typeof EXTENSION_PERMISSIONS];
export type ExtensionStatus = 'enabled' | 'disabled' | 'error';
export type ExtensionSource = 'store' | 'local' | 'url';

export interface ExtensionManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  repository?: string;
  license?: string;
  icon?: string;
  permissions: ExtensionPermission[];
  hostPermissions?: string[];
  background?: {
    serviceWorker?: string;
    persistent?: boolean;
  };
  contentScripts?: {
    matches: string[];
    js?: string[];
    css?: string[];
    runAt?: 'document_start' | 'document_end' | 'document_idle';
  }[];
  webAccessibleResources?: string[];
  action?: {
    defaultIcon?: string;
    defaultTitle?: string;
    defaultPopup?: string;
  };
  optionsPage?: string;
  minimumBrowserVersion?: string;
  maximumBrowserVersion?: string;
  updateUrl?: string;
  privacyPolicy?: string;
}

export interface Extension {
  id: string;
  manifest: ExtensionManifest;
  status: ExtensionStatus;
  enabled: boolean;
  installed: boolean;
  updateAvailable: boolean;
  lastUpdated: string;
  size: number;
  source: ExtensionSource;
  error?: string;
  metrics?: ExtensionMetrics;
  settings?: ExtensionSettings;
}

export interface ExtensionMetrics {
  memoryUsage: number;
  cpuUsage: number;
  networkUsage: number;
  lastActive: string;
  crashCount: number;
  errorCount: number;
}

export interface ExtensionSettings {
  enabled: boolean;
  permissions: ExtensionPermission[];
  options?: Record<string, unknown>;
}

// Feature Types
export type Feature = (typeof FEATURES)[keyof typeof FEATURES];
export type FeatureStatus = 'enabled' | 'disabled' | 'experimental';

export interface FeatureConfig {
  name: Feature;
  status: FeatureStatus;
  enabled: boolean;
  experimental: boolean;
  settings?: Record<string, unknown>;
}

// Theme Types
export type ThemeType = (typeof THEME_TYPES)[keyof typeof THEME_TYPES];

export interface Theme {
  id: string;
  name: string;
  type: ThemeType;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    error: string;
    text: string;
    disabled: string;
    placeholder: string;
    backdrop: string;
    notification: string;
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    fontWeight: number;
    lineHeight: number;
    letterSpacing: number;
  };
  spacing: {
    unit: number;
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  shape: {
    borderRadius: number;
  };
  transitions: {
    duration: number;
    easing: string;
  };
  shadows: string[];
}

// Language Types
export type Language = (typeof LANGUAGES)[keyof typeof LANGUAGES];

export interface LanguageConfig {
  code: Language;
  name: string;
  nativeName: string;
  rtl: boolean;
  fallback: Language;
}

// Error Types
export type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

export interface AppError {
  type: ErrorType;
  code: string;
  message: string;
  details?: unknown;
  stack?: string;
  timestamp: string;
}

// Event Types
export type EventType = (typeof EVENT_TYPES)[keyof typeof EVENT_TYPES];

export interface AppEvent {
  type: EventType;
  payload: unknown;
  timestamp: string;
}

// Storage Types
export type StorageKey = (typeof STORAGE_KEYS)[keyof typeof STORAGE_KEYS];

export interface StorageItem<T> {
  key: StorageKey;
  value: T;
  timestamp: string;
  version: string;
}

// Cache Types
export type CacheKey = (typeof CACHE_KEYS)[keyof typeof CACHE_KEYS];

export interface CacheItem<T> {
  key: CacheKey;
  value: T;
  timestamp: string;
  expires: string;
}

// Timeout Types
export type Timeout = (typeof TIMEOUTS)[keyof typeof TIMEOUTS];

// Limit Types
export type Limit = (typeof LIMITS)[keyof typeof LIMITS];

// Regex Types
export type Regex = (typeof REGEX)[keyof typeof REGEX];

// File Types
export type FileType = (typeof FILE_TYPES)[keyof typeof FILE_TYPES][number];

// MIME Types
export type MimeType = (typeof MIME_TYPES)[keyof typeof MIME_TYPES];

// HTTP Status Types
export type HttpStatus = (typeof HTTP_STATUS)[keyof typeof HTTP_STATUS];

// Shortcut Types
export type Shortcut = (typeof SHORTCUTS)[keyof typeof SHORTCUTS];

// Window Types
export interface WindowConfig {
  id: string;
  bounds: Electron.Rectangle;
  isMaximized: boolean;
  isFullScreen: boolean;
  isVisible: boolean;
  isFocused: boolean;
  isDevToolsOpened: boolean;
  tabs: Tab[];
  activeTab: string;
}

// Tab Types
export interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isLoading: boolean;
  isActive: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isPrivate: boolean;
  isSleeping: boolean;
  lastActive: string;
  createdAt: string;
  updatedAt: string;
}

// Bookmark Types
export interface Bookmark {
  id: string;
  url: string;
  title: string;
  description?: string;
  favicon?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// History Types
export interface HistoryItem {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  visitCount: number;
  lastVisitTime: string;
  createdAt: string;
}

// Download Types
export interface Download {
  id: string;
  url: string;
  filename: string;
  path: string;
  size: number;
  received: number;
  status: 'pending' | 'downloading' | 'completed' | 'error';
  error?: string;
  createdAt: string;
  updatedAt: string;
}

// Profile Types
export interface Profile {
  id: string;
  name: string;
  avatar?: string;
  isDefault: boolean;
  isPrivate: boolean;
  settings: ProfileSettings;
  createdAt: string;
  updatedAt: string;
}

export interface ProfileSettings {
  theme: Theme;
  language: Language;
  features: FeatureConfig[];
  extensions: ExtensionSettings[];
  privacy: PrivacySettings;
  security: SecuritySettings;
  performance: PerformanceSettings;
  sync: SyncSettings;
}

// Privacy Types
export interface PrivacySettings {
  doNotTrack: boolean;
  blockAds: boolean;
  blockTrackers: boolean;
  blockFingerprinting: boolean;
  blockWebRTC: boolean;
  clearOnExit: boolean;
  cookies: 'allow' | 'block' | 'blockThirdParty';
  location: 'allow' | 'block' | 'ask';
  notifications: 'allow' | 'block' | 'ask';
  camera: 'allow' | 'block' | 'ask';
  microphone: 'allow' | 'block' | 'ask';
}

// Security Types
export interface SecuritySettings {
  sandbox: boolean;
  contextIsolation: boolean;
  nodeIntegration: boolean;
  webSecurity: boolean;
  allowRunningInsecureContent: boolean;
  enableRemoteModule: boolean;
  autoUpdate: boolean;
  updateChannel: 'stable' | 'beta' | 'nightly';
  passwordManager: boolean;
  phishingProtection: boolean;
  malwareProtection: boolean;
}

// Performance Types
export interface PerformanceSettings {
  maxMemory: number;
  maxCpu: number;
  maxTabs: number;
  tabSleepTimeout: number;
  cacheSize: number;
  hardwareAcceleration: boolean;
  lowPowerMode: boolean;
  backgroundThrottling: boolean;
}

// Sync Types
export interface SyncSettings {
  enabled: boolean;
  interval: number;
  maxRetries: number;
  timeout: number;
  encryptData: boolean;
  syncBookmarks: boolean;
  syncHistory: boolean;
  syncPasswords: boolean;
  syncSettings: boolean;
  syncExtensions: boolean;
}

// API Types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown(),
  error: z
    .object({
      code: z.string(),
      message: z.string(),
      details: z.unknown().optional(),
    })
    .optional(),
});

export type ApiResponse = z.infer<typeof ApiResponseSchema>;

// User Types
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  username: z.string(),
  role: z.enum(['admin', 'user', 'guest']),
  permissions: z.array(z.string()),
  settings: z.record(z.unknown()),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

// Security Types
export const SecurityConfigSchema = z.object({
  encryption: z.object({
    algorithm: z.string(),
    keySize: z.number(),
    iterations: z.number(),
  }),
  jwt: z.object({
    secret: z.string(),
    expiresIn: z.string(),
    refreshExpiresIn: z.string(),
  }),
});

export type SecurityConfig = z.infer<typeof SecurityConfigSchema>;

// Performance Types
export const PerformanceMetricsSchema = z.object({
  timestamp: z.date(),
  cpu: z.number(),
  memory: z.number(),
  responseTime: z.number(),
  requestsPerSecond: z.number(),
  errorRate: z.number(),
});

export type PerformanceMetrics = z.infer<typeof PerformanceMetricsSchema>;

// Analytics Types
export const AnalyticsEventSchema = z.object({
  name: z.string(),
  properties: z.record(z.unknown()),
  timestamp: z.date(),
  userId: z.string().uuid().optional(),
  sessionId: z.string().uuid(),
});

export type AnalyticsEvent = z.infer<typeof AnalyticsEventSchema>;

// Accessibility Types
export const AccessibilityConfigSchema = z.object({
  enabled: z.boolean(),
  features: z.array(z.string()),
  compliance: z.array(z.string()),
  preferences: z.record(z.unknown()),
});

export type AccessibilityConfig = z.infer<typeof AccessibilityConfigSchema>;

// Error Types
export const ErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  stack: z.string().optional(),
  details: z.unknown().optional(),
  timestamp: z.date(),
});

export type Error = z.infer<typeof ErrorSchema>;

// Validation Types
export const ValidationResultSchema = z.object({
  isValid: z.boolean(),
  errors: z.array(z.string()),
  warnings: z.array(z.string()),
});

export type ValidationResult = z.infer<typeof ValidationResultSchema>;

// Monitoring Types
export const MonitoringConfigSchema = z.object({
  enabled: z.boolean(),
  metrics: z.object({
    enabled: z.boolean(),
    interval: z.number(),
  }),
  tracing: z.object({
    enabled: z.boolean(),
    sampling: z.number(),
  }),
});

export type MonitoringConfig = z.infer<typeof MonitoringConfigSchema>;

// Worker Types
export interface WorkerMessage<T> {
  type: string;
  payload: T;
  id: string;
  timestamp: string;
}

// Test Types
export interface TestConfig {
  name: string;
  description: string;
  setup: () => Promise<void>;
  teardown: () => Promise<void>;
  timeout: number;
  retries: number;
  parallel: boolean;
}

// Documentation Types
export interface DocConfig {
  title: string;
  description: string;
  version: string;
  author: string;
  license: string;
  repository: string;
  homepage: string;
  bugs: string;
  funding: string;
  keywords: string[];
  categories: string[];
  tags: string[];
  languages: Language[];
  features: Feature[];
  extensions: Extension[];
  themes: Theme[];
  profiles: Profile[];
  settings: ProfileSettings;
  api: {
    endpoints: string[];
    authentication: string;
    rateLimiting: string;
    errors: string;
  };
  development: {
    setup: string;
    building: string;
    testing: string;
    contributing: string;
  };
  deployment: {
    requirements: string;
    installation: string;
    configuration: string;
    updating: string;
  };
  support: {
    faq: string;
    troubleshooting: string;
    contact: string;
  };
  legal: {
    privacy: string;
    terms: string;
    license: string;
    security: string;
  };
}

// Export all types
export * from './api.types';
export * from './user.types';
export * from './security.types';
export * from './performance.types';
export * from './analytics.types';
export * from './accessibility.types';
export * from './error.types';
export * from './validation.types';
export * from './monitoring.types';
