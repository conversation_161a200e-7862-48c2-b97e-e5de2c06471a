export type SearchType = 'fulltext' | 'fuzzy' | 'semantic' | 'custom';

export type SearchStatus = 'active' | 'inactive' | 'error' | 'custom';

export interface SearchConfig {
  id: string;
  name: string;
  settings: {
    type: SearchType;
    index: {
      name: string;
      settings: Record<string, any>;
      mappings: Record<string, any>;
    };
    query: {
      timeout: number;
      maxResults: number;
      minScore: number;
    };
    highlight: {
      enabled: boolean;
      fields: string[];
      tags: {
        pre: string;
        post: string;
      };
    };
  };
  features: {
    suggestions: boolean;
    facets: boolean;
    sorting: boolean;
    filtering: boolean;
  };
  metadata: Record<string, any>;
}

export interface SearchIndex {
  id: string;
  config: SearchConfig;
  type: SearchType;
  status: SearchStatus;
  name: string;
  description?: string;
  fields: SearchField[];
  stats: {
    documents: number;
    size: number;
    lastIndexed: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SearchField {
  id: string;
  index: string;
  name: string;
  type: string;
  description?: string;
  analyzer?: string;
  searchable: boolean;
  filterable: boolean;
  sortable: boolean;
  facetable: boolean;
  highlightable: boolean;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SearchDocument {
  id: string;
  index: string;
  fields: Record<string, any>;
  score?: number;
  highlights?: Record<string, string[]>;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SearchQuery {
  id: string;
  index: string;
  text: string;
  type: SearchType;
  fields?: string[];
  filters?: Record<string, any>;
  facets?: string[];
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  }[];
  page?: {
    number: number;
    size: number;
  };
  highlights?: {
    fields: string[];
    tags: {
      pre: string;
      post: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SearchResult {
  id: string;
  query: string;
  index: string;
  total: number;
  documents: SearchDocument[];
  facets?: Record<string, any>;
  suggestions?: string[];
  duration: number;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SearchLog {
  id: string;
  index: string;
  query?: string;
  result?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SearchStats {
  total: number;
  byType: Record<SearchType, number>;
  byStatus: Record<SearchStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface SearchService {
  createConfig: (config: Omit<SearchConfig, 'id'>) => SearchConfig;
  updateConfig: (id: string, config: Partial<SearchConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => SearchConfig | undefined;
  getConfigs: () => SearchConfig[];
  createIndex: (
    config: Omit<SearchConfig, 'id'>,
    index: Omit<SearchIndex, 'id' | 'config'>
  ) => Promise<SearchIndex>;
  updateIndex: (id: string, index: Partial<SearchIndex>) => Promise<SearchIndex>;
  deleteIndex: (id: string) => Promise<void>;
  getIndex: (id: string) => SearchIndex | undefined;
  getIndexes: (options?: {
    type?: SearchType[];
    status?: SearchStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SearchIndex[];
  createField: (index: string, field: Omit<SearchField, 'id' | 'index'>) => Promise<SearchField>;
  updateField: (id: string, field: Partial<SearchField>) => Promise<SearchField>;
  deleteField: (id: string) => Promise<void>;
  getField: (id: string) => SearchField | undefined;
  getFields: (options?: {
    index?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SearchField[];
  index: (
    index: string,
    documents: Omit<SearchDocument, 'id' | 'index'>[]
  ) => Promise<SearchDocument[]>;
  search: (index: string, query: Omit<SearchQuery, 'id' | 'index'>) => Promise<SearchResult>;
  getLogs: (options?: {
    index?: string;
    query?: string;
    result?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SearchLog[];
  getStats: () => SearchStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
