export type VersionType = 'major' | 'minor' | 'patch' | 'custom';

export type VersionStatus = 'draft' | 'review' | 'approved' | 'released' | 'custom';

export interface VersionConfig {
  id: string;
  name: string;
  settings: {
    numbering: 'semantic' | 'numeric' | 'custom';
    branches: boolean;
    tags: boolean;
    releases: boolean;
  };
  features: {
    changelog: boolean;
    diff: boolean;
    rollback: boolean;
    merge: boolean;
  };
  metadata: Record<string, any>;
}

export interface Version {
  id: string;
  config: VersionConfig;
  type: VersionType;
  status: VersionStatus;
  number: string;
  name: string;
  description?: string;
  entity: {
    type: string;
    id: string;
  };
  changes: {
    type: 'add' | 'update' | 'delete';
    path: string;
    value?: any;
  }[];
  parent?: string;
  branches: string[];
  tags: string[];
  releasedAt?: Date;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface VersionBranch {
  id: string;
  config: VersionConfig;
  name: string;
  description?: string;
  base: string;
  head: string;
  versions: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface VersionStats {
  total: number;
  byType: Record<VersionType, number>;
  byStatus: Record<VersionStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface VersionService {
  createConfig: (config: Omit<VersionConfig, 'id'>) => VersionConfig;
  updateConfig: (id: string, config: Partial<VersionConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => VersionConfig | undefined;
  getConfigs: () => VersionConfig[];
  create: (
    config: Omit<VersionConfig, 'id'>,
    version: Omit<Version, 'id' | 'config'>
  ) => Promise<Version>;
  update: (id: string, version: Partial<Version>) => Promise<Version>;
  delete: (id: string) => Promise<void>;
  getVersion: (id: string) => Version | undefined;
  getVersions: (options?: {
    type?: VersionType;
    status?: VersionStatus;
    entityType?: string;
    entityId?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Version[];
  createBranch: (
    config: Omit<VersionConfig, 'id'>,
    branch: Omit<VersionBranch, 'id' | 'config'>
  ) => Promise<VersionBranch>;
  updateBranch: (id: string, branch: Partial<VersionBranch>) => Promise<VersionBranch>;
  deleteBranch: (id: string) => Promise<void>;
  getBranch: (id: string) => VersionBranch | undefined;
  getBranches: (options?: { startDate?: Date; endDate?: Date }) => VersionBranch[];
  merge: (
    sourceId: string,
    targetId: string,
    options?: {
      strategy?: 'fast-forward' | 'recursive' | 'octopus';
      message?: string;
    }
  ) => Promise<Version>;
  diff: (
    sourceId: string,
    targetId: string
  ) => Promise<{
    changes: Version['changes'];
    conflicts?: {
      path: string;
      source: any;
      target: any;
    }[];
  }>;
  rollback: (id: string, targetId: string) => Promise<Version>;
  clear: () => Promise<void>;
  getStats: () => VersionStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
