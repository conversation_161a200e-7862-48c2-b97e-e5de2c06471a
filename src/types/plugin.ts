export type PluginType = 'extension' | 'theme' | 'widget' | 'integration' | 'custom';

export type PluginStatus = 'installed' | 'enabled' | 'disabled' | 'error' | 'custom';

export interface PluginConfig {
  id: string;
  name: string;
  settings: {
    autoUpdate: boolean;
    sandbox: boolean;
    permissions: string[];
    dependencies: string[];
  };
  features: {
    hotReload: boolean;
    versioning: boolean;
    logging: boolean;
    debugging: boolean;
  };
  metadata: Record<string, any>;
}

export interface Plugin {
  id: string;
  config: PluginConfig;
  name: string;
  type: PluginType;
  status: PluginStatus;
  version: string;
  description?: string;
  author?: string;
  homepage?: string;
  repository?: string;
  license?: string;
  dependencies: {
    name: string;
    version: string;
    type: PluginType;
  }[];
  hooks: {
    name: string;
    handler: (...args: any[]) => any;
  }[];
  events: {
    name: string;
    handler: (...args: any[]) => void;
  }[];
  commands: {
    name: string;
    handler: (...args: any[]) => any;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface PluginManifest {
  id: string;
  name: string;
  type: PluginType;
  version: string;
  description?: string;
  author?: string;
  homepage?: string;
  repository?: string;
  license?: string;
  dependencies: {
    name: string;
    version: string;
    type: PluginType;
  }[];
  entry: string;
  files: string[];
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface PluginStats {
  total: number;
  byType: Record<PluginType, number>;
  byStatus: Record<PluginStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface PluginService {
  createConfig: (config: Omit<PluginConfig, 'id'>) => PluginConfig;
  updateConfig: (id: string, config: Partial<PluginConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => PluginConfig | undefined;
  getConfigs: () => PluginConfig[];
  install: (
    manifest: Omit<PluginManifest, 'id'>,
    options?: {
      force?: boolean;
      validate?: boolean;
    }
  ) => Promise<Plugin>;
  uninstall: (id: string) => Promise<void>;
  enable: (id: string) => Promise<Plugin>;
  disable: (id: string) => Promise<Plugin>;
  update: (
    id: string,
    options?: {
      force?: boolean;
      validate?: boolean;
    }
  ) => Promise<Plugin>;
  getPlugin: (id: string) => Plugin | undefined;
  getPlugins: (options?: {
    type?: PluginType;
    status?: PluginStatus;
    startDate?: Date;
    endDate?: Date;
  }) => Plugin[];
  registerHook: (id: string, name: string, handler: (...args: any[]) => any) => Promise<void>;
  unregisterHook: (id: string, name: string) => Promise<void>;
  registerEvent: (id: string, name: string, handler: (...args: any[]) => void) => Promise<void>;
  unregisterEvent: (id: string, name: string) => Promise<void>;
  registerCommand: (id: string, name: string, handler: (...args: any[]) => any) => Promise<void>;
  unregisterCommand: (id: string, name: string) => Promise<void>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  clear: () => Promise<void>;
  getStats: () => PluginStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
