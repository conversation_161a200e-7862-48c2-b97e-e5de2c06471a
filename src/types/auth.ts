export type AuthProvider =
  | 'local'
  | 'google'
  | 'facebook'
  | 'twitter'
  | 'github'
  | 'microsoft'
  | 'apple';

export type AuthStrategy = 'jwt' | 'session' | 'oauth' | 'saml';

export interface AuthConfig {
  provider: AuthProvider;
  strategy: AuthStrategy;
  tokenKey: string;
  refreshTokenKey: string;
  tokenExpiration: number;
  refreshTokenExpiration: number;
  autoRefresh: boolean;
  autoLogout: boolean;
  redirectUrl: string;
  metadata: Record<string, any>;
}

export interface AuthUser {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatar?: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  isVerified: boolean;
  lastLogin?: number;
  metadata?: Record<string, any>;
}

export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
  scope?: string;
  metadata?: Record<string, any>;
}

export interface AuthState {
  user: AuthUser | null;
  token: AuthToken | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
  metadata?: Record<string, any>;
}

export interface AuthOptions {
  provider?: AuthProvider;
  strategy?: AuthStrategy;
  redirectUrl?: string;
  metadata?: Record<string, any>;
}

export interface AuthService {
  login: (
    credentials: { username: string; password: string },
    options?: AuthOptions
  ) => Promise<AuthUser>;
  logout: () => Promise<void>;
  register: (user: Partial<AuthUser>, options?: AuthOptions) => Promise<AuthUser>;
  verify: (token: string) => Promise<AuthUser>;
  resetPassword: (email: string) => Promise<void>;
  changePassword: (oldPassword: string, newPassword: string) => Promise<void>;
  refreshToken: () => Promise<AuthToken>;
  getUser: () => Promise<AuthUser>;
  getToken: () => Promise<AuthToken>;
  isAuthenticated: () => boolean;
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
  updateUser: (user: Partial<AuthUser>) => Promise<AuthUser>;
  updateToken: (token: Partial<AuthToken>) => Promise<AuthToken>;
  clearAuth: () => Promise<void>;
}
