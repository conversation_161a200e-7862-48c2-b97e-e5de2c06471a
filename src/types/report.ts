export type ReportType = 'system' | 'user' | 'analytics' | 'custom';
export type ReportStatus =
  | 'draft'
  | 'scheduled'
  | 'generating'
  | 'completed'
  | 'failed'
  | 'archived';
export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'json' | 'custom';

export interface ReportConfig {
  management: {
    enabled: boolean;
    types: ReportType[];
    formats: ReportFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  generation: {
    enabled: boolean;
    engine: 'template' | 'query' | 'custom';
    timeout: number;
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  scheduling: {
    enabled: boolean;
    engine: 'cron' | 'interval' | 'custom';
    timezone: string;
    schedules: {
      name: string;
      type: ReportType;
      pattern: string;
      options?: Record<string, any>;
    }[];
  };
  delivery: {
    enabled: boolean;
    channels: {
      email?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      sms?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      push?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      webhook?: {
        enabled: boolean;
        endpoints: {
          url: string;
          method: string;
          headers?: Record<string, string>;
          timeout?: number;
          retry?: number;
        }[];
      };
    };
    templates: {
      name: string;
      type: ReportType;
      format: ReportFormat;
      subject?: string;
      body: string;
      variables?: string[];
    }[];
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      generation?: boolean;
      delivery?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    scheduling?: boolean;
    delivery?: boolean;
    analytics?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Report {
  id: string;
  config: ReportConfig;
  type: ReportType;
  status: ReportStatus;
  name: string;
  description?: string;
  format: ReportFormat;
  template: {
    name: string;
    type: ReportType;
    format: ReportFormat;
    content: string;
    variables?: string[];
  };
  data?: {
    source: 'database' | 'file' | 'api' | 'custom';
    query?: string;
    parameters?: Record<string, any>;
    filters?: {
      field: string;
      operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'custom';
      value: any;
    }[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    }[];
    limit?: number;
    offset?: number;
  };
  schedule?: {
    pattern: string;
    timezone: string;
    startDate?: Date;
    endDate?: Date;
    options?: Record<string, any>;
  };
  delivery?: {
    channels: {
      type: 'email' | 'sms' | 'push' | 'webhook' | 'custom';
      config: Record<string, any>;
    }[];
    recipients: {
      type: 'user' | 'group' | 'system' | 'custom';
      id: string;
      name?: string;
      email?: string;
      phone?: string;
      device?: string;
      webhook?: string;
    }[];
  };
  generation?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  stats?: {
    size: number;
    duration: number;
    recipients: number;
  };
  createdAt: Date;
  updatedAt: Date;
  generatedAt?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface ReportLog {
  id: string;
  report: string;
  action: 'create' | 'update' | 'delete' | 'generate' | 'deliver' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface ReportStats {
  total: number;
  byType: Record<ReportType, number>;
  byStatus: Record<ReportStatus, number>;
  byFormat: Record<ReportFormat, number>;
  generation: {
    total: number;
    success: number;
    failure: number;
    byReport?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  delivery: {
    total: number;
    success: number;
    failure: number;
    byReport?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageSize: number;
    averageDuration: number;
    byReport?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface ReportService {
  createConfig: (config: Omit<ReportConfig, 'id'>) => ReportConfig;
  updateConfig: (id: string, config: Partial<ReportConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ReportConfig | undefined;
  getConfigs: () => ReportConfig[];
  create: (
    config: Omit<ReportConfig, 'id'>,
    report: Omit<Report, 'id' | 'config'>
  ) => Promise<Report>;
  update: (id: string, report: Partial<Report>) => Promise<Report>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Report | undefined;
  getAll: (options?: {
    type?: ReportType[];
    status?: ReportStatus[];
    format?: ReportFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Report[];
  search: (
    query: string,
    options?: {
      type?: ReportType[];
      status?: ReportStatus[];
      format?: ReportFormat[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Report[];
  generate: (id: string) => Promise<void>;
  schedule: (id: string, schedule: Report['schedule']) => Promise<void>;
  deliver: (id: string) => Promise<void>;
  getLogs: (options?: {
    report?: string;
    action?: ('create' | 'update' | 'delete' | 'generate' | 'deliver' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ReportLog[];
  getStats: () => ReportStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
