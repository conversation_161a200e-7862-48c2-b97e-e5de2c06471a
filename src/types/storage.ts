export type StorageType = 'local' | 's3' | 'gcs' | 'azure' | 'custom';

export type StorageStatus = 'active' | 'inactive' | 'error' | 'custom';

export interface StorageConfig {
  id: string;
  name: string;
  settings: {
    type: StorageType;
    connection: {
      endpoint?: string;
      region?: string;
      credentials?: Record<string, any>;
      options?: Record<string, any>;
    };
    bucket: {
      name: string;
      prefix?: string;
      public?: boolean;
    };
    upload: {
      maxSize: number;
      allowedTypes: string[];
      chunkSize: number;
    };
  };
  features: {
    versioning: boolean;
    encryption: boolean;
    compression: boolean;
    cdn: boolean;
  };
  metadata: Record<string, any>;
}

export interface StorageBucket {
  id: string;
  config: StorageConfig;
  type: StorageType;
  status: StorageStatus;
  name: string;
  description?: string;
  path: string;
  public: boolean;
  stats: {
    objects: number;
    size: number;
    lastModified: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface StorageObject {
  id: string;
  bucket: string;
  key: string;
  name: string;
  type: string;
  size: number;
  url: string;
  version?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
}

export interface StorageUpload {
  id: string;
  bucket: string;
  object: string;
  status: StorageStatus;
  progress: number;
  chunks: {
    number: number;
    status: StorageStatus;
    size: number;
    uploaded: number;
  }[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

export interface StorageDownload {
  id: string;
  bucket: string;
  object: string;
  status: StorageStatus;
  progress: number;
  chunks: {
    number: number;
    status: StorageStatus;
    size: number;
    downloaded: number;
  }[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

export interface StorageLog {
  id: string;
  bucket: string;
  object?: string;
  upload?: string;
  download?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface StorageStats {
  total: number;
  byType: Record<StorageType, number>;
  byStatus: Record<StorageStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface StorageService {
  createConfig: (config: Omit<StorageConfig, 'id'>) => StorageConfig;
  updateConfig: (id: string, config: Partial<StorageConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => StorageConfig | undefined;
  getConfigs: () => StorageConfig[];
  createBucket: (
    config: Omit<StorageConfig, 'id'>,
    bucket: Omit<StorageBucket, 'id' | 'config'>
  ) => Promise<StorageBucket>;
  updateBucket: (id: string, bucket: Partial<StorageBucket>) => Promise<StorageBucket>;
  deleteBucket: (id: string) => Promise<void>;
  getBucket: (id: string) => StorageBucket | undefined;
  getBuckets: (options?: {
    type?: StorageType[];
    status?: StorageStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => StorageBucket[];
  upload: (
    bucket: string,
    file: File,
    options?: {
      key?: string;
      metadata?: Record<string, any>;
      onProgress?: (progress: number) => void;
    }
  ) => Promise<StorageObject>;
  download: (
    bucket: string,
    key: string,
    options?: {
      version?: string;
      onProgress?: (progress: number) => void;
    }
  ) => Promise<Blob>;
  delete: (
    bucket: string,
    key: string,
    options?: {
      version?: string;
    }
  ) => Promise<void>;
  getObject: (
    bucket: string,
    key: string,
    options?: {
      version?: string;
    }
  ) => Promise<StorageObject | undefined>;
  getObjects: (options?: {
    bucket?: string;
    prefix?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Promise<StorageObject[]>;
  getUpload: (id: string) => StorageUpload | undefined;
  getUploads: (options?: {
    bucket?: string;
    object?: string;
    status?: StorageStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => StorageUpload[];
  getDownload: (id: string) => StorageDownload | undefined;
  getDownloads: (options?: {
    bucket?: string;
    object?: string;
    status?: StorageStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => StorageDownload[];
  getLogs: (options?: {
    bucket?: string;
    object?: string;
    upload?: string;
    download?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => StorageLog[];
  getStats: () => StorageStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
