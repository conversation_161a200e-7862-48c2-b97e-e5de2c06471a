export type TimeEntryType = 'development' | 'meeting' | 'research' | 'planning' | 'custom';

export type TimeEntryStatus = 'active' | 'paused' | 'completed' | 'cancelled' | 'custom';

export interface TimeConfig {
  id: string;
  name: string;
  settings: {
    rounding: number;
    timezone: string;
    workHours: {
      start: string;
      end: string;
      days: number[];
    };
  };
  features: {
    timer: boolean;
    manual: boolean;
    breaks: boolean;
    overtime: boolean;
  };
  metadata: Record<string, any>;
}

export interface TimeEntry {
  id: string;
  config: TimeConfig;
  type: TimeEntryType;
  status: TimeEntryStatus;
  project: string;
  task?: string;
  user: string;
  description?: string;
  startTime: Date;
  endTime?: Date;
  duration: number;
  breaks: {
    startTime: Date;
    endTime: Date;
    duration: number;
    reason?: string;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TimeReport {
  id: string;
  user: string;
  project?: string;
  task?: string;
  startDate: Date;
  endDate: Date;
  totalTime: number;
  billableTime: number;
  nonBillableTime: number;
  overtime: number;
  breaks: number;
  entries: TimeEntry[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TimeStats {
  total: number;
  byType: Record<TimeEntryType, number>;
  byStatus: Record<TimeEntryStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface TimeService {
  createConfig: (config: Omit<TimeConfig, 'id'>) => TimeConfig;
  updateConfig: (id: string, config: Partial<TimeConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => TimeConfig | undefined;
  getConfigs: () => TimeConfig[];
  startTimer: (
    config: Omit<TimeConfig, 'id'>,
    entry: Omit<TimeEntry, 'id' | 'config' | 'endTime' | 'duration'>
  ) => Promise<TimeEntry>;
  stopTimer: (id: string) => Promise<TimeEntry>;
  pauseTimer: (id: string) => Promise<TimeEntry>;
  resumeTimer: (id: string) => Promise<TimeEntry>;
  createEntry: (
    config: Omit<TimeConfig, 'id'>,
    entry: Omit<TimeEntry, 'id' | 'config'>
  ) => Promise<TimeEntry>;
  updateEntry: (id: string, entry: Partial<TimeEntry>) => Promise<TimeEntry>;
  deleteEntry: (id: string) => Promise<void>;
  getEntry: (id: string) => TimeEntry | undefined;
  getEntries: (options?: {
    type?: TimeEntryType;
    status?: TimeEntryStatus;
    project?: string;
    task?: string;
    user?: string;
    startDate?: Date;
    endDate?: Date;
  }) => TimeEntry[];
  createReport: (report: Omit<TimeReport, 'id'>) => Promise<TimeReport>;
  updateReport: (id: string, report: Partial<TimeReport>) => Promise<TimeReport>;
  deleteReport: (id: string) => Promise<void>;
  getReport: (id: string) => TimeReport | undefined;
  getReports: (options?: {
    user?: string;
    project?: string;
    task?: string;
    startDate?: Date;
    endDate?: Date;
  }) => TimeReport[];
  clear: () => Promise<void>;
  getStats: () => TimeStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
