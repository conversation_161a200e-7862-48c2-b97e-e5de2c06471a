export type SecurityLevel = 'low' | 'medium' | 'high' | 'critical';

export type SecurityStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';

export type SecurityType = 'authentication' | 'authorization' | 'encryption' | 'custom';

export interface SecurityConfig {
  management: {
    enabled: boolean;
    types: SecurityType[];
    levels: SecurityLevel[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  authentication: {
    enabled: boolean;
    methods: ('password' | 'oauth' | 'saml' | 'custom')[];
    password?: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      maxAttempts: number;
      lockoutDuration: number;
    };
    oauth?: {
      providers: {
        name: string;
        clientId: string;
        clientSecret: string;
        scopes: string[];
      }[];
    };
    saml?: {
      providers: {
        name: string;
        entityId: string;
        ssoUrl: string;
        certificate: string;
      }[];
    };
  };
  authorization: {
    enabled: boolean;
    engine: 'rbac' | 'abac' | 'custom';
    roles: {
      name: string;
      permissions: string[];
      inheritance?: string[];
    }[];
    policies: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
  };
  encryption: {
    enabled: boolean;
    algorithms: {
      symmetric?: {
        name: string;
        keySize: number;
        mode: string;
        padding: string;
      }[];
      asymmetric?: {
        name: string;
        keySize: number;
        padding: string;
      }[];
      hash?: {
        name: string;
        saltLength: number;
        iterations: number;
      }[];
    };
    keys: {
      rotation: {
        enabled: boolean;
        interval: string;
        overlap: string;
      };
      backup: {
        enabled: boolean;
        location: string;
        encryption: boolean;
      };
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      authentication?: boolean;
      authorization?: boolean;
      encryption?: boolean;
      activity?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    authentication?: boolean;
    authorization?: boolean;
    encryption?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface SecurityViolation {
  id: string;
  type: SecurityType;
  level: SecurityLevel;
  message: string;
  context: Record<string, any>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface SecurityReport {
  id: string;
  timestamp: Date;
  violations: SecurityViolation[];
  summary: {
    total: number;
    byType: Record<SecurityType, number>;
    byLevel: Record<SecurityLevel, number>;
  };
  metadata?: Record<string, any>;
}

export interface SecurityPolicy {
  id: string;
  config: SecurityConfig;
  name: string;
  description?: string;
  type: 'authentication' | 'authorization' | 'encryption' | 'compliance' | 'custom';
  level: SecurityLevel;
  status: SecurityStatus;
  rules: {
    condition: string;
    action: string;
    priority: number;
  }[];
  exceptions?: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface SecurityRule {
  id: string;
  policy: string;
  name: string;
  description?: string;
  condition: string;
  action: string;
  priority: number;
  status: SecurityStatus;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface SecurityAlert {
  id: string;
  config: SecurityConfig;
  type: 'threat' | 'vulnerability' | 'compliance' | 'custom';
  level: SecurityLevel;
  status: 'new' | 'investigating' | 'resolved' | 'false_positive';
  source: string;
  description: string;
  details: {
    affected?: string[];
    impact?: string;
    recommendation?: string;
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  metadata: Record<string, any>;
}

export interface SecurityAudit {
  id: string;
  config: SecurityConfig;
  type: 'authentication' | 'authorization' | 'data' | 'system' | 'custom';
  level: SecurityLevel;
  user?: string;
  resource?: string;
  action?: string;
  status: 'success' | 'failure' | 'warning';
  details: {
    ip?: string;
    userAgent?: string;
    location?: string;
    timestamp: Date;
    duration?: number;
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface SecurityScan {
  id: string;
  config: SecurityConfig;
  type: 'vulnerability' | 'compliance' | 'performance' | 'custom';
  status: 'pending' | 'running' | 'completed' | 'failed';
  target: string;
  results: {
    vulnerabilities?: {
      severity: SecurityLevel;
      type: string;
      description: string;
      recommendation: string;
    }[];
    compliance?: {
      standard: string;
      status: 'compliant' | 'non_compliant' | 'partial';
      issues: string[];
    }[];
    performance?: {
      metric: string;
      value: number;
      threshold: number;
      status: 'ok' | 'warning' | 'critical';
    }[];
  };
  startedAt: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface SecurityStats {
  total: number;
  byType: Record<string, number>;
  byLevel: Record<SecurityLevel, number>;
  byStatus: Record<SecurityStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata: Record<string, any>;
}

export interface SecurityService {
  createConfig: (config: Omit<SecurityConfig, 'id'>) => SecurityConfig;
  updateConfig: (id: string, config: Partial<SecurityConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => SecurityConfig | undefined;
  getConfigs: () => SecurityConfig[];
  createPolicy: (
    config: Omit<SecurityConfig, 'id'>,
    policy: Omit<SecurityPolicy, 'id' | 'config'>
  ) => Promise<SecurityPolicy>;
  updatePolicy: (id: string, policy: Partial<SecurityPolicy>) => Promise<SecurityPolicy>;
  deletePolicy: (id: string) => Promise<void>;
  getPolicy: (id: string) => SecurityPolicy | undefined;
  getPolicies: (options?: {
    type?: ('authentication' | 'authorization' | 'encryption' | 'compliance' | 'custom')[];
    level?: SecurityLevel[];
    status?: SecurityStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityPolicy[];
  createRole: (
    config: Omit<SecurityConfig, 'id'>,
    role: Omit<SecurityRole, 'id' | 'config'>
  ) => Promise<SecurityRole>;
  updateRole: (id: string, role: Partial<SecurityRole>) => Promise<SecurityRole>;
  deleteRole: (id: string) => Promise<void>;
  getRole: (id: string) => SecurityRole | undefined;
  getRoles: (options?: {
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityRole[];
  createPermission: (
    config: Omit<SecurityConfig, 'id'>,
    permission: Omit<SecurityPermission, 'id' | 'config'>
  ) => Promise<SecurityPermission>;
  updatePermission: (
    id: string,
    permission: Partial<SecurityPermission>
  ) => Promise<SecurityPermission>;
  deletePermission: (id: string) => Promise<void>;
  getPermission: (id: string) => SecurityPermission | undefined;
  getPermissions: (options?: {
    resource?: string;
    action?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityPermission[];
  createAudit: (
    config: Omit<SecurityConfig, 'id'>,
    audit: Omit<SecurityAudit, 'id' | 'config'>
  ) => Promise<SecurityAudit>;
  getAudit: (id: string) => SecurityAudit | undefined;
  getAudits: (options?: {
    type?: ('authentication' | 'authorization' | 'data' | 'system' | 'custom')[];
    level?: SecurityLevel[];
    user?: string;
    resource?: string;
    action?: string;
    status?: ('success' | 'failure' | 'warning')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityAudit[];
  createAlert: (
    config: Omit<SecurityConfig, 'id'>,
    alert: Omit<SecurityAlert, 'id' | 'config'>
  ) => Promise<SecurityAlert>;
  updateAlert: (id: string, alert: Partial<SecurityAlert>) => Promise<SecurityAlert>;
  deleteAlert: (id: string) => Promise<void>;
  getAlert: (id: string) => SecurityAlert | undefined;
  getAlerts: (options?: {
    type?: ('threat' | 'vulnerability' | 'compliance' | 'custom')[];
    level?: SecurityLevel[];
    status?: ('new' | 'investigating' | 'resolved' | 'false_positive')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityAlert[];
  startScan: (
    config: Omit<SecurityConfig, 'id'>,
    options: {
      type: 'vulnerability' | 'compliance' | 'performance' | 'custom';
      target: string;
    }
  ) => Promise<SecurityScan>;
  getScan: (id: string) => SecurityScan | undefined;
  getScans: (options?: {
    type?: ('vulnerability' | 'compliance' | 'performance' | 'custom')[];
    status?: ('pending' | 'running' | 'completed' | 'failed')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityScan[];
  authenticate: (credentials: {
    username: string;
    password: string;
    method?: 'password' | '2fa' | 'biometric' | 'oauth' | 'saml' | 'custom';
  }) => Promise<{
    token: string;
    user: any;
    permissions: string[];
  }>;
  authorize: (options: {
    user: string;
    resource: string;
    action: string;
    context?: Record<string, any>;
  }) => Promise<boolean>;
  encrypt: (
    data: any,
    options?: {
      algorithm?: 'aes-256-gcm' | 'chacha20-poly1305' | 'custom';
      key?: string;
    }
  ) => Promise<string>;
  decrypt: (
    data: string,
    options?: {
      algorithm?: 'aes-256-gcm' | 'chacha20-poly1305' | 'custom';
      key?: string;
    }
  ) => Promise<any>;
  getStats: () => SecurityStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}

export interface SecurityEvent {
  type: SecurityType;
  level: SecurityLevel;
  user: {
    id: string;
    email: string;
    roles: string[];
  };
  action: string;
  resource: string;
  details: Record<string, any>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface SecurityPolicy {
  name: string;
  description: string;
  rules: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface SecurityRole {
  id: string;
  config: SecurityConfig;
  name: string;
  description?: string;
  permissions: string[];
  inherits?: string[];
  metadata: {
    scope?: string;
    restrictions?: string[];
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityPermission {
  id: string;
  config: SecurityConfig;
  name: string;
  description?: string;
  resource: string;
  action: string;
  conditions?: string[];
  metadata: {
    scope?: string;
    restrictions?: string[];
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityContext {
  user: {
    id: string;
    roles: string[];
    permissions: string[];
    sessionId: string;
    ip: string;
    userAgent: string;
    lastActivity: number;
  };
  request: {
    method: string;
    url: string;
    headers: Record<string, string>;
    body: any;
    query: Record<string, string>;
    params: Record<string, string>;
    ip: string;
    userAgent: string;
    timestamp: number;
  };
  response: {
    statusCode: number;
    headers: Record<string, string>;
    body: any;
    timestamp: number;
  };
}

export interface SecurityAudit {
  events: SecurityEvent[];
  summary: {
    total: number;
    byType: Record<string, number>;
    byLevel: Record<string, number>;
    byUser: Record<string, number>;
    byIp: Record<string, number>;
  };
  recommendations: {
    level: 'low' | 'medium' | 'high';
    message: string;
    impact: string;
    remediation: string;
  }[];
}

export interface SecurityMiddleware {
  name: string;
  handler: (context: SecurityContext) => Promise<void>;
  priority: number;
}

export interface SecurityError extends Error {
  code: string;
  status: number;
  context?: SecurityContext;
  metadata?: Record<string, any>;
}

export interface SecurityValidationError extends SecurityError {
  code: 'VALIDATION_ERROR';
  details: {
    field: string;
    message: string;
    value: any;
  }[];
}

export interface SecurityAuthError extends SecurityError {
  code: 'AUTH_ERROR';
  reason: 'invalid_credentials' | 'expired_token' | 'invalid_token' | 'missing_token';
}

export interface SecurityRateLimitError extends SecurityError {
  code: 'RATE_LIMIT_ERROR';
  retryAfter: number;
}

export interface SecurityCorsError extends SecurityError {
  code: 'CORS_ERROR';
  origin: string;
  method: string;
}

export interface SecurityPolicyError extends SecurityError {
  code: 'POLICY_ERROR';
  policy: string;
  rule: string;
}

export interface Security {
  id: string;
  config: SecurityConfig;
  type: SecurityType;
  status: SecurityStatus;
  level: SecurityLevel;
  name: string;
  description?: string;
  authentication: {
    methods: ('password' | 'oauth' | 'saml' | 'custom')[];
    lastLogin?: Date;
    lastPasswordChange?: Date;
    failedAttempts: number;
    lockedUntil?: Date;
    oauth?: {
      provider: string;
      id: string;
      token: string;
      refreshToken?: string;
      expiresAt?: Date;
    }[];
    saml?: {
      provider: string;
      id: string;
      attributes: Record<string, any>;
    }[];
  };
  authorization: {
    roles: string[];
    permissions: string[];
    policies: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
  };
  encryption: {
    algorithms: {
      symmetric?: {
        name: string;
        keySize: number;
        mode: string;
        padding: string;
      }[];
      asymmetric?: {
        name: string;
        keySize: number;
        padding: string;
      }[];
      hash?: {
        name: string;
        saltLength: number;
        iterations: number;
      }[];
    };
    keys: {
      current: {
        id: string;
        type: 'symmetric' | 'asymmetric' | 'hash';
        algorithm: string;
        created: Date;
        expires?: Date;
      };
      previous?: {
        id: string;
        type: 'symmetric' | 'asymmetric' | 'hash';
        algorithm: string;
        created: Date;
        expires?: Date;
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    authentications: number;
    authorizations: number;
    encryptions: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface SecurityLog {
  id: string;
  security: string;
  action: 'create' | 'update' | 'delete' | 'authenticate' | 'authorize' | 'encrypt' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface SecurityStats {
  total: number;
  byType: Record<SecurityType, number>;
  byStatus: Record<SecurityStatus, number>;
  byLevel: Record<SecurityLevel, number>;
  authentication: {
    total: number;
    success: number;
    failure: number;
    byMethod?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  authorization: {
    total: number;
    allowed: number;
    denied: number;
    bySecurity?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  encryption: {
    total: number;
    success: number;
    failure: number;
    byAlgorithm?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface SecurityService {
  createConfig: (config: Omit<SecurityConfig, 'id'>) => SecurityConfig;
  updateConfig: (id: string, config: Partial<SecurityConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => SecurityConfig | undefined;
  getConfigs: () => SecurityConfig[];
  create: (
    config: Omit<SecurityConfig, 'id'>,
    security: Omit<Security, 'id' | 'config'>
  ) => Promise<Security>;
  update: (id: string, security: Partial<Security>) => Promise<Security>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Security | undefined;
  getAll: (options?: {
    type?: SecurityType[];
    status?: SecurityStatus[];
    level?: SecurityLevel[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Security[];
  search: (
    query: string,
    options?: {
      type?: SecurityType[];
      status?: SecurityStatus[];
      level?: SecurityLevel[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Security[];
  authenticate: (id: string, credentials: Record<string, any>) => Promise<boolean>;
  authorize: (id: string, action: string, resource: string) => Promise<boolean>;
  encrypt: (id: string, data: string | Buffer) => Promise<string | Buffer>;
  decrypt: (id: string, data: string | Buffer) => Promise<string | Buffer>;
  getLogs: (options?: {
    security?: string;
    action?: (
      | 'create'
      | 'update'
      | 'delete'
      | 'authenticate'
      | 'authorize'
      | 'encrypt'
      | 'error'
    )[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SecurityLog[];
  getStats: () => SecurityStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
