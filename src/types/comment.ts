export type CommentStatus = 'active' | 'hidden' | 'deleted' | 'custom';

export type CommentType = 'text' | 'review' | 'feedback' | 'question' | 'custom';

export interface CommentConfig {
  id: string;
  name: string;
  settings: {
    moderation: boolean;
    threading: boolean;
    voting: boolean;
    reporting: boolean;
  };
  features: {
    mentions: boolean;
    attachments: boolean;
    formatting: boolean;
    reactions: boolean;
  };
  metadata: Record<string, any>;
}

export interface Comment {
  id: string;
  config: CommentConfig;
  type: CommentType;
  status: CommentStatus;
  parent?: string;
  entity: {
    type: string;
    id: string;
  };
  author: string;
  content: string;
  attachments?: {
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
  }[];
  mentions?: string[];
  reactions?: {
    user: string;
    emoji: string;
    timestamp: Date;
  }[];
  votes: {
    up: number;
    down: number;
    users: {
      id: string;
      vote: 'up' | 'down';
      timestamp: Date;
    }[];
  };
  reports: {
    user: string;
    reason: string;
    timestamp: Date;
  }[];
  editedAt?: Date;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface CommentThread {
  id: string;
  config: CommentConfig;
  entity: {
    type: string;
    id: string;
  };
  rootComment: string;
  comments: string[];
  totalComments: number;
  lastComment?: Comment;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface CommentReport {
  id: string;
  timestamp: Date;
  comments: Comment[];
  threads: CommentThread[];
  summary: {
    total: number;
    byType: Record<CommentType, number>;
    byStatus: Record<CommentStatus, number>;
  };
  metadata?: Record<string, any>;
}

export interface CommentStats {
  total: number;
  byType: Record<CommentType, number>;
  byStatus: Record<CommentStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface CommentService {
  createConfig: (config: Omit<CommentConfig, 'id'>) => CommentConfig;
  updateConfig: (id: string, config: Partial<CommentConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => CommentConfig | undefined;
  getConfigs: () => CommentConfig[];
  createThread: (
    config: Omit<CommentConfig, 'id'>,
    thread: Omit<CommentThread, 'id' | 'config'>
  ) => Promise<CommentThread>;
  updateThread: (id: string, thread: Partial<CommentThread>) => Promise<CommentThread>;
  deleteThread: (id: string) => Promise<void>;
  getThread: (id: string) => CommentThread | undefined;
  getThreads: (options?: {
    entityType?: string;
    entityId?: string;
    startDate?: Date;
    endDate?: Date;
  }) => CommentThread[];
  createComment: (
    config: Omit<CommentConfig, 'id'>,
    comment: Omit<Comment, 'id' | 'config'>
  ) => Promise<Comment>;
  updateComment: (id: string, comment: Partial<Comment>) => Promise<Comment>;
  deleteComment: (id: string) => Promise<void>;
  getComment: (id: string) => Comment | undefined;
  getComments: (options?: {
    type?: CommentType;
    status?: CommentStatus;
    parent?: string;
    entityType?: string;
    entityId?: string;
    author?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Comment[];
  addReaction: (commentId: string, userId: string, emoji: string) => Promise<Comment>;
  removeReaction: (commentId: string, userId: string, emoji: string) => Promise<Comment>;
  vote: (commentId: string, userId: string, vote: 'up' | 'down') => Promise<Comment>;
  report: (commentId: string, userId: string, reason: string) => Promise<Comment>;
  clear: () => Promise<void>;
  getStats: () => CommentStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
