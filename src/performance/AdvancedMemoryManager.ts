/**
 * Advanced Memory Manager
 * Intelligent memory management with predictive cleanup and optimization
 */

import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';

export interface MemoryPool {
  id: string;
  name: string;
  type: 'heap' | 'buffer' | 'cache' | 'temporary' | 'persistent';
  size: number; // bytes
  used: number; // bytes
  maxSize: number; // bytes
  priority: 'low' | 'medium' | 'high' | 'critical';
  autoCleanup: boolean;
  cleanupThreshold: number; // 0-1 (percentage)
  lastCleanup: Date;
  created: Date;
}

export interface MemoryAllocation {
  id: string;
  poolId: string;
  size: number; // bytes
  type: 'object' | 'array' | 'buffer' | 'string' | 'function';
  tag?: string;
  stackTrace?: string;
  timestamp: Date;
  lastAccessed: Date;
  accessCount: number;
  persistent: boolean;
}

export interface MemoryPressure {
  level: 'none' | 'low' | 'medium' | 'high' | 'critical';
  totalUsed: number; // bytes
  totalAvailable: number; // bytes
  heapUsed: number; // bytes
  heapTotal: number; // bytes
  external: number; // bytes
  rss: number; // bytes
  timestamp: Date;
  predictions: {
    nextGC: Date;
    memoryExhaustion?: Date;
    recommendedCleanup: string[];
  };
}

export interface GarbageCollectionStats {
  type: 'minor' | 'major' | 'incremental';
  duration: number; // ms
  freedMemory: number; // bytes
  beforeSize: number; // bytes
  afterSize: number; // bytes
  timestamp: Date;
  triggered: 'automatic' | 'manual' | 'pressure';
}

export interface MemoryLeak {
  id: string;
  type: 'growing-object' | 'retained-closure' | 'event-listener' | 'timer' | 'dom-reference';
  severity: 'low' | 'medium' | 'high' | 'critical';
  growthRate: number; // bytes/sec
  currentSize: number; // bytes
  location: string;
  stackTrace?: string;
  firstDetected: Date;
  lastGrowth: Date;
  mitigation?: string[];
}

export class AdvancedMemoryManager extends EventEmitter {
  private pools = new Map<string, MemoryPool>();
  private allocations = new Map<string, MemoryAllocation>();
  private gcStats: GarbageCollectionStats[] = [];
  private pressureHistory: MemoryPressure[] = [];
  private detectedLeaks = new Map<string, MemoryLeak>();
  
  private monitoringInterval = 5000; // 5 seconds
  private cleanupInterval = 30000; // 30 seconds
  private leakDetectionInterval = 60000; // 1 minute
  private maxHistorySize = 1000;
  
  private monitoringTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  private leakDetectionTimer?: NodeJS.Timeout;
  
  private predictiveCleanupEnabled = true;
  private aggressiveCleanupEnabled = false;
  private leakDetectionEnabled = true;

  constructor() {
    super();
    this.initializeDefaultPools();
    this.setupGCMonitoring();
    this.startMonitoring();
    this.startCleanupLoop();
    this.startLeakDetection();
  }

  /**
   * Create a new memory pool
   */
  createPool(
    name: string,
    type: MemoryPool['type'],
    maxSize: number,
    options: {
      priority?: MemoryPool['priority'];
      autoCleanup?: boolean;
      cleanupThreshold?: number;
    } = {}
  ): MemoryPool {
    const pool: MemoryPool = {
      id: this.generatePoolId(),
      name,
      type,
      size: 0,
      used: 0,
      maxSize,
      priority: options.priority || 'medium',
      autoCleanup: options.autoCleanup !== false,
      cleanupThreshold: options.cleanupThreshold || 0.8,
      lastCleanup: new Date(),
      created: new Date(),
    };

    this.pools.set(pool.id, pool);
    
    logger.info(`Created memory pool`, {
      id: pool.id,
      name,
      type,
      maxSize: this.formatBytes(maxSize),
    });

    this.emit('pool-created', pool);
    return pool;
  }

  /**
   * Allocate memory from a pool
   */
  allocate(
    poolId: string,
    size: number,
    type: MemoryAllocation['type'],
    options: {
      tag?: string;
      persistent?: boolean;
      stackTrace?: boolean;
    } = {}
  ): MemoryAllocation | null {
    const pool = this.pools.get(poolId);
    if (!pool) {
      throw new Error(`Pool ${poolId} not found`);
    }

    if (pool.used + size > pool.maxSize) {
      logger.warn(`Pool ${pool.name} would exceed max size`, {
        current: this.formatBytes(pool.used),
        requested: this.formatBytes(size),
        max: this.formatBytes(pool.maxSize),
      });

      // Try cleanup first
      if (pool.autoCleanup) {
        this.cleanupPool(poolId);
        
        // Check again after cleanup
        if (pool.used + size > pool.maxSize) {
          this.emit('allocation-failed', { poolId, size, reason: 'pool-full' });
          return null;
        }
      } else {
        this.emit('allocation-failed', { poolId, size, reason: 'pool-full' });
        return null;
      }
    }

    const allocation: MemoryAllocation = {
      id: this.generateAllocationId(),
      poolId,
      size,
      type,
      tag: options.tag,
      stackTrace: options.stackTrace ? this.captureStackTrace() : undefined,
      timestamp: new Date(),
      lastAccessed: new Date(),
      accessCount: 1,
      persistent: options.persistent || false,
    };

    this.allocations.set(allocation.id, allocation);
    pool.used += size;
    pool.size = Math.max(pool.size, pool.used);

    logger.debug(`Memory allocated`, {
      id: allocation.id,
      pool: pool.name,
      size: this.formatBytes(size),
      type,
    });

    this.emit('memory-allocated', allocation);
    return allocation;
  }

  /**
   * Deallocate memory
   */
  deallocate(allocationId: string): boolean {
    const allocation = this.allocations.get(allocationId);
    if (!allocation) {
      return false;
    }

    const pool = this.pools.get(allocation.poolId);
    if (pool) {
      pool.used -= allocation.size;
    }

    this.allocations.delete(allocationId);

    logger.debug(`Memory deallocated`, {
      id: allocationId,
      size: this.formatBytes(allocation.size),
      pool: pool?.name,
    });

    this.emit('memory-deallocated', allocation);
    return true;
  }

  /**
   * Get current memory pressure
   */
  getCurrentPressure(): MemoryPressure {
    const memUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();

    const pressure: MemoryPressure = {
      level: this.calculatePressureLevel(memUsage, totalMemory, freeMemory),
      totalUsed: totalMemory - freeMemory,
      totalAvailable: freeMemory,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      timestamp: new Date(),
      predictions: this.generateMemoryPredictions(memUsage),
    };

    return pressure;
  }

  /**
   * Force garbage collection
   */
  forceGarbageCollection(): GarbageCollectionStats | null {
    if (!global.gc) {
      logger.warn('Garbage collection not available (run with --expose-gc)');
      return null;
    }

    const beforeMemory = process.memoryUsage();
    const startTime = Date.now();

    global.gc();

    const afterMemory = process.memoryUsage();
    const duration = Date.now() - startTime;

    const stats: GarbageCollectionStats = {
      type: 'manual',
      duration,
      freedMemory: beforeMemory.heapUsed - afterMemory.heapUsed,
      beforeSize: beforeMemory.heapUsed,
      afterSize: afterMemory.heapUsed,
      timestamp: new Date(),
      triggered: 'manual',
    };

    this.gcStats.push(stats);
    this.maintainHistorySize();

    logger.info(`Manual GC completed`, {
      duration: `${duration}ms`,
      freed: this.formatBytes(stats.freedMemory),
      before: this.formatBytes(stats.beforeSize),
      after: this.formatBytes(stats.afterSize),
    });

    this.emit('gc-completed', stats);
    return stats;
  }

  /**
   * Cleanup a specific pool
   */
  cleanupPool(poolId: string): number {
    const pool = this.pools.get(poolId);
    if (!pool) {
      return 0;
    }

    let freedBytes = 0;
    const now = new Date();
    const allocationsToRemove: string[] = [];

    // Find allocations to cleanup based on age and access patterns
    for (const [id, allocation] of this.allocations) {
      if (allocation.poolId !== poolId || allocation.persistent) {
        continue;
      }

      const age = now.getTime() - allocation.lastAccessed.getTime();
      const shouldCleanup = this.shouldCleanupAllocation(allocation, age);

      if (shouldCleanup) {
        allocationsToRemove.push(id);
        freedBytes += allocation.size;
      }
    }

    // Remove selected allocations
    allocationsToRemove.forEach(id => {
      this.deallocate(id);
    });

    pool.lastCleanup = now;

    logger.info(`Pool cleanup completed`, {
      pool: pool.name,
      removed: allocationsToRemove.length,
      freed: this.formatBytes(freedBytes),
    });

    this.emit('pool-cleaned', { poolId, freedBytes, removedCount: allocationsToRemove.length });
    return freedBytes;
  }

  /**
   * Detect memory leaks
   */
  detectMemoryLeaks(): MemoryLeak[] {
    const newLeaks: MemoryLeak[] = [];
    const now = new Date();

    // Analyze allocation patterns
    const allocationsByType = new Map<string, MemoryAllocation[]>();
    
    for (const allocation of this.allocations.values()) {
      const key = `${allocation.type}_${allocation.tag || 'untagged'}`;
      if (!allocationsByType.has(key)) {
        allocationsByType.set(key, []);
      }
      allocationsByType.get(key)!.push(allocation);
    }

    // Look for growing patterns
    for (const [key, allocations] of allocationsByType) {
      if (allocations.length < 10) continue; // Need enough data

      const recentAllocations = allocations
        .filter(a => now.getTime() - a.timestamp.getTime() < 300000) // Last 5 minutes
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      if (recentAllocations.length < 5) continue;

      const growthRate = this.calculateGrowthRate(recentAllocations);
      
      if (growthRate > 1024 * 1024) { // 1MB/sec growth rate threshold
        const leak: MemoryLeak = {
          id: this.generateLeakId(),
          type: this.classifyLeakType(key, allocations),
          severity: this.calculateLeakSeverity(growthRate, allocations.length),
          growthRate,
          currentSize: allocations.reduce((sum, a) => sum + a.size, 0),
          location: key,
          stackTrace: allocations[0].stackTrace,
          firstDetected: now,
          lastGrowth: recentAllocations[recentAllocations.length - 1].timestamp,
          mitigation: this.generateLeakMitigation(key, allocations),
        };

        this.detectedLeaks.set(leak.id, leak);
        newLeaks.push(leak);

        logger.warn(`Memory leak detected`, {
          id: leak.id,
          type: leak.type,
          severity: leak.severity,
          growthRate: this.formatBytes(leak.growthRate) + '/sec',
          currentSize: this.formatBytes(leak.currentSize),
        });
      }
    }

    if (newLeaks.length > 0) {
      this.emit('leaks-detected', newLeaks);
    }

    return newLeaks;
  }

  /**
   * Initialize default memory pools
   */
  private initializeDefaultPools(): void {
    // Create default pools
    this.createPool('heap', 'heap', 2 * 1024 * 1024 * 1024, { priority: 'critical' }); // 2GB
    this.createPool('cache', 'cache', 512 * 1024 * 1024, { priority: 'medium' }); // 512MB
    this.createPool('temporary', 'temporary', 256 * 1024 * 1024, { priority: 'low' }); // 256MB
    this.createPool('buffers', 'buffer', 1024 * 1024 * 1024, { priority: 'high' }); // 1GB

    logger.info('Default memory pools initialized');
  }

  /**
   * Setup garbage collection monitoring
   */
  private setupGCMonitoring(): void {
    // Monitor GC events if available
    if (process.versions && process.versions.v8) {
      // This would require additional setup for V8 GC monitoring
      logger.info('GC monitoring setup completed');
    }
  }

  /**
   * Start memory monitoring
   */
  private startMonitoring(): void {
    this.monitoringTimer = setInterval(() => {
      const pressure = this.getCurrentPressure();
      this.pressureHistory.push(pressure);
      this.maintainHistorySize();

      this.emit('pressure-update', pressure);

      // Trigger cleanup if pressure is high
      if (pressure.level === 'high' || pressure.level === 'critical') {
        logger.warn(`High memory pressure detected: ${pressure.level}`, {
          heapUsed: this.formatBytes(pressure.heapUsed),
          heapTotal: this.formatBytes(pressure.heapTotal),
          rss: this.formatBytes(pressure.rss),
        });

        if (this.predictiveCleanupEnabled) {
          this.performPredictiveCleanup(pressure);
        }
      }
    }, this.monitoringInterval);

    logger.info('Memory monitoring started');
  }

  /**
   * Start cleanup loop
   */
  private startCleanupLoop(): void {
    this.cleanupTimer = setInterval(() => {
      this.performScheduledCleanup();
    }, this.cleanupInterval);

    logger.info('Memory cleanup loop started');
  }

  /**
   * Start leak detection
   */
  private startLeakDetection(): void {
    if (!this.leakDetectionEnabled) return;

    this.leakDetectionTimer = setInterval(() => {
      this.detectMemoryLeaks();
    }, this.leakDetectionInterval);

    logger.info('Memory leak detection started');
  }

  /**
   * Calculate memory pressure level
   */
  private calculatePressureLevel(
    memUsage: NodeJS.MemoryUsage,
    totalMemory: number,
    freeMemory: number
  ): MemoryPressure['level'] {
    const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
    const systemUsageRatio = (totalMemory - freeMemory) / totalMemory;

    if (heapUsageRatio > 0.9 || systemUsageRatio > 0.95) return 'critical';
    if (heapUsageRatio > 0.8 || systemUsageRatio > 0.85) return 'high';
    if (heapUsageRatio > 0.6 || systemUsageRatio > 0.7) return 'medium';
    if (heapUsageRatio > 0.4 || systemUsageRatio > 0.5) return 'low';
    return 'none';
  }

  /**
   * Generate memory predictions
   */
  private generateMemoryPredictions(memUsage: NodeJS.MemoryUsage): MemoryPressure['predictions'] {
    const predictions: MemoryPressure['predictions'] = {
      nextGC: new Date(Date.now() + 30000), // Estimate 30 seconds
      recommendedCleanup: [],
    };

    // Analyze trends from history
    if (this.pressureHistory.length > 5) {
      const recentHistory = this.pressureHistory.slice(-5);
      const growthRate = this.calculateMemoryGrowthRate(recentHistory);

      if (growthRate > 0) {
        const timeToExhaustion = (memUsage.heapTotal - memUsage.heapUsed) / growthRate;
        if (timeToExhaustion < 300000) { // Less than 5 minutes
          predictions.memoryExhaustion = new Date(Date.now() + timeToExhaustion);
        }
      }
    }

    // Generate cleanup recommendations
    for (const [id, pool] of this.pools) {
      if (pool.used / pool.maxSize > pool.cleanupThreshold) {
        predictions.recommendedCleanup.push(pool.name);
      }
    }

    return predictions;
  }

  /**
   * Calculate memory growth rate
   */
  private calculateMemoryGrowthRate(history: MemoryPressure[]): number {
    if (history.length < 2) return 0;

    const first = history[0];
    const last = history[history.length - 1];
    const timeDiff = last.timestamp.getTime() - first.timestamp.getTime();
    const memoryDiff = last.heapUsed - first.heapUsed;

    return timeDiff > 0 ? (memoryDiff / timeDiff) * 1000 : 0; // bytes per second
  }

  /**
   * Perform predictive cleanup
   */
  private performPredictiveCleanup(pressure: MemoryPressure): void {
    logger.info('Performing predictive cleanup due to memory pressure');

    let totalFreed = 0;

    // Cleanup pools in priority order
    const poolsByPriority = Array.from(this.pools.values())
      .filter(pool => pool.autoCleanup)
      .sort((a, b) => {
        const priorityOrder = { low: 0, medium: 1, high: 2, critical: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

    for (const pool of poolsByPriority) {
      if (pressure.level === 'critical' || pool.used / pool.maxSize > pool.cleanupThreshold) {
        const freed = this.cleanupPool(pool.id);
        totalFreed += freed;

        // Check if we've freed enough memory
        if (totalFreed > 100 * 1024 * 1024) { // 100MB
          break;
        }
      }
    }

    // Force GC if still under pressure
    if (pressure.level === 'critical' && global.gc) {
      this.forceGarbageCollection();
    }

    this.emit('predictive-cleanup-completed', { totalFreed, pressure });
  }

  /**
   * Perform scheduled cleanup
   */
  private performScheduledCleanup(): void {
    let totalFreed = 0;

    for (const [id, pool] of this.pools) {
      if (pool.autoCleanup && pool.used / pool.maxSize > pool.cleanupThreshold) {
        const freed = this.cleanupPool(id);
        totalFreed += freed;
      }
    }

    if (totalFreed > 0) {
      logger.debug(`Scheduled cleanup completed, freed ${this.formatBytes(totalFreed)}`);
    }
  }

  /**
   * Should cleanup allocation
   */
  private shouldCleanupAllocation(allocation: MemoryAllocation, age: number): boolean {
    // Don't cleanup persistent allocations
    if (allocation.persistent) return false;

    // Cleanup based on type and age
    switch (allocation.type) {
      case 'buffer':
        return age > 300000; // 5 minutes
      case 'string':
        return age > 600000 && allocation.accessCount < 5; // 10 minutes, low access
      case 'object':
        return age > 900000 && allocation.accessCount < 3; // 15 minutes, very low access
      case 'array':
        return age > 1200000 && allocation.accessCount < 2; // 20 minutes, minimal access
      default:
        return age > 1800000; // 30 minutes default
    }
  }

  /**
   * Calculate allocation growth rate
   */
  private calculateGrowthRate(allocations: MemoryAllocation[]): number {
    if (allocations.length < 2) return 0;

    const first = allocations[0];
    const last = allocations[allocations.length - 1];
    const timeDiff = last.timestamp.getTime() - first.timestamp.getTime();
    const sizeDiff = allocations.reduce((sum, a) => sum + a.size, 0);

    return timeDiff > 0 ? (sizeDiff / timeDiff) * 1000 : 0; // bytes per second
  }

  /**
   * Classify leak type
   */
  private classifyLeakType(key: string, allocations: MemoryAllocation[]): MemoryLeak['type'] {
    if (key.includes('function')) return 'retained-closure';
    if (key.includes('array') && allocations.length > 1000) return 'growing-object';
    if (key.includes('timer')) return 'timer';
    if (key.includes('event')) return 'event-listener';
    return 'growing-object';
  }

  /**
   * Calculate leak severity
   */
  private calculateLeakSeverity(growthRate: number, allocationCount: number): MemoryLeak['severity'] {
    if (growthRate > 10 * 1024 * 1024 || allocationCount > 10000) return 'critical'; // 10MB/sec
    if (growthRate > 5 * 1024 * 1024 || allocationCount > 5000) return 'high'; // 5MB/sec
    if (growthRate > 1024 * 1024 || allocationCount > 1000) return 'medium'; // 1MB/sec
    return 'low';
  }

  /**
   * Generate leak mitigation suggestions
   */
  private generateLeakMitigation(key: string, allocations: MemoryAllocation[]): string[] {
    const mitigation: string[] = [];

    if (key.includes('function')) {
      mitigation.push('Review closure usage and variable references');
      mitigation.push('Consider using WeakMap for object references');
    }

    if (key.includes('event')) {
      mitigation.push('Ensure event listeners are properly removed');
      mitigation.push('Use AbortController for automatic cleanup');
    }

    if (key.includes('timer')) {
      mitigation.push('Clear timers when no longer needed');
      mitigation.push('Use WeakRef for timer callbacks');
    }

    mitigation.push('Review object lifecycle management');
    mitigation.push('Consider implementing object pooling');

    return mitigation;
  }

  /**
   * Maintain history size limits
   */
  private maintainHistorySize(): void {
    if (this.pressureHistory.length > this.maxHistorySize) {
      this.pressureHistory.splice(0, this.pressureHistory.length - this.maxHistorySize);
    }

    if (this.gcStats.length > this.maxHistorySize) {
      this.gcStats.splice(0, this.gcStats.length - this.maxHistorySize);
    }
  }

  /**
   * Capture stack trace
   */
  private captureStackTrace(): string {
    const stack = new Error().stack;
    return stack ? stack.split('\n').slice(2, 8).join('\n') : 'Stack trace not available';
  }

  /**
   * Format bytes for display
   */
  private formatBytes(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * Generate pool ID
   */
  private generatePoolId(): string {
    return `pool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate allocation ID
   */
  private generateAllocationId(): string {
    return `alloc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate leak ID
   */
  private generateLeakId(): string {
    return `leak_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get manager statistics
   */
  getStats() {
    return {
      pools: this.pools.size,
      allocations: this.allocations.size,
      gcStats: this.gcStats.length,
      pressureHistory: this.pressureHistory.length,
      detectedLeaks: this.detectedLeaks.size,
      totalAllocatedMemory: Array.from(this.allocations.values()).reduce((sum, a) => sum + a.size, 0),
      predictiveCleanupEnabled: this.predictiveCleanupEnabled,
      aggressiveCleanupEnabled: this.aggressiveCleanupEnabled,
      leakDetectionEnabled: this.leakDetectionEnabled,
    };
  }

  /**
   * Get memory pools
   */
  getPools(): MemoryPool[] {
    return Array.from(this.pools.values());
  }

  /**
   * Get detected leaks
   */
  getDetectedLeaks(): MemoryLeak[] {
    return Array.from(this.detectedLeaks.values());
  }

  /**
   * Get recent GC stats
   */
  getRecentGCStats(limit = 10): GarbageCollectionStats[] {
    return this.gcStats
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Enable/disable predictive cleanup
   */
  setPredictiveCleanup(enabled: boolean): void {
    this.predictiveCleanupEnabled = enabled;
    logger.info(`Predictive cleanup ${enabled ? 'enabled' : 'disabled'}`);
    this.emit('predictive-cleanup-changed', enabled);
  }

  /**
   * Enable/disable aggressive cleanup
   */
  setAggressiveCleanup(enabled: boolean): void {
    this.aggressiveCleanupEnabled = enabled;
    logger.info(`Aggressive cleanup ${enabled ? 'enabled' : 'disabled'}`);
    this.emit('aggressive-cleanup-changed', enabled);
  }

  /**
   * Enable/disable leak detection
   */
  setLeakDetection(enabled: boolean): void {
    this.leakDetectionEnabled = enabled;
    
    if (enabled && !this.leakDetectionTimer) {
      this.startLeakDetection();
    } else if (!enabled && this.leakDetectionTimer) {
      clearInterval(this.leakDetectionTimer);
      this.leakDetectionTimer = undefined;
    }
    
    logger.info(`Leak detection ${enabled ? 'enabled' : 'disabled'}`);
    this.emit('leak-detection-changed', enabled);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    if (this.leakDetectionTimer) {
      clearInterval(this.leakDetectionTimer);
    }

    this.pools.clear();
    this.allocations.clear();
    this.gcStats.length = 0;
    this.pressureHistory.length = 0;
    this.detectedLeaks.clear();
    this.removeAllListeners();
  }
}

// Global advanced memory manager instance
export const advancedMemoryManager = new AdvancedMemoryManager();

export default advancedMemoryManager;
