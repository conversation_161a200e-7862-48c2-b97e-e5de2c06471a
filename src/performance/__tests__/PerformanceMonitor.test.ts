import { PerformanceMonitor } from '../PerformanceMonitor';

describe('PerformanceMonitor', () => {
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = PerformanceMonitor.getInstance();
  });

  afterEach(() => {
    performanceMonitor.cleanup();
  });

  describe('Initialization', () => {
    it('should create a singleton instance', () => {
      const instance1 = PerformanceMonitor.getInstance();
      const instance2 = PerformanceMonitor.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with default configuration', () => {
      const config = performanceMonitor.getConfig();
      expect(config.samplingInterval).toBe(1000);
      expect(config.retentionPeriod).toBe(24 * 60 * 60 * 1000);
      expect(config.maxMetrics).toBe(10000);
      expect(config.enabledMetrics).toContain('memory_usage');
      expect(config.enabledMetrics).toContain('cpu_usage');
    });

    it('should initialize default thresholds', () => {
      const config = performanceMonitor.getConfig();
      expect(config.thresholds).toHaveLength(8);
      expect(config.thresholds.find(t => t.metric === 'memory_usage')).toBeDefined();
      expect(config.thresholds.find(t => t.metric === 'cpu_usage')).toBeDefined();
    });
  });

  describe('Metric Recording', () => {
    it('should record metrics', () => {
      const timestamp = Date.now();
      performanceMonitor.recordMetric('test_metric', 42, timestamp);

      const metrics = performanceMonitor.getMetrics('test_metric');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(42);
      expect(metrics[0].timestamp).toBe(timestamp);
    });

    it('should not record disabled metrics', () => {
      performanceMonitor.updateConfig({
        enabledMetrics: ['enabled_metric'],
      });

      performanceMonitor.recordMetric('disabled_metric', 42);
      const metrics = performanceMonitor.getMetrics('disabled_metric');
      expect(metrics).toHaveLength(0);
    });

    it('should record metrics with tags', () => {
      const tags = { component: 'test', type: 'performance' };
      performanceMonitor.recordMetric('test_metric', 42, Date.now(), tags);

      const metrics = performanceMonitor.getMetrics('test_metric');
      expect(metrics[0].tags).toEqual(tags);
    });
  });

  describe('Metric Retrieval', () => {
    beforeEach(() => {
      const now = Date.now();
      performanceMonitor.recordMetric('test_metric', 10, now - 2000);
      performanceMonitor.recordMetric('test_metric', 20, now - 1000);
      performanceMonitor.recordMetric('test_metric', 30, now);
    });

    it('should retrieve all metrics for a given name', () => {
      const metrics = performanceMonitor.getMetrics('test_metric');
      expect(metrics).toHaveLength(3);
      expect(metrics.map(m => m.value)).toEqual([10, 20, 30]);
    });

    it('should filter metrics by time range', () => {
      const now = Date.now();
      const metrics = performanceMonitor.getMetrics('test_metric', now - 1500, now - 500);
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(20);
    });

    it('should return empty array for non-existent metric', () => {
      const metrics = performanceMonitor.getMetrics('non_existent');
      expect(metrics).toHaveLength(0);
    });
  });

  describe('Metric Summary', () => {
    beforeEach(() => {
      const now = Date.now();
      performanceMonitor.recordMetric('test_metric', 10, now - 2000);
      performanceMonitor.recordMetric('test_metric', 20, now - 1000);
      performanceMonitor.recordMetric('test_metric', 30, now);
    });

    it('should calculate metric summary', () => {
      const summary = performanceMonitor.getMetricSummary('test_metric');
      expect(summary).toEqual({
        min: 10,
        max: 30,
        avg: 20,
        count: 3,
      });
    });

    it('should calculate summary for time range', () => {
      const now = Date.now();
      const summary = performanceMonitor.getMetricSummary('test_metric', now - 1500, now - 500);
      expect(summary).toEqual({
        min: 20,
        max: 20,
        avg: 20,
        count: 1,
      });
    });

    it('should return zero values for non-existent metric', () => {
      const summary = performanceMonitor.getMetricSummary('non_existent');
      expect(summary).toEqual({
        min: 0,
        max: 0,
        avg: 0,
        count: 0,
      });
    });
  });

  describe('Threshold Monitoring', () => {
    it('should emit threshold exceeded events', () => {
      const eventHandler = jest.fn();
      performanceMonitor.on('threshold_exceeded', eventHandler);

      performanceMonitor.recordMetric('memory_usage', 95); // Above critical threshold
      expect(eventHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          metric: 'memory_usage',
          value: 95,
          level: 'critical',
        })
      );

      performanceMonitor.recordMetric('cpu_usage', 75); // Above warning threshold
      expect(eventHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          metric: 'cpu_usage',
          value: 75,
          level: 'warning',
        })
      );
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration', () => {
      const newConfig = {
        samplingInterval: 2000,
        maxMetrics: 5000,
      };

      performanceMonitor.updateConfig(newConfig);
      const config = performanceMonitor.getConfig();

      expect(config.samplingInterval).toBe(2000);
      expect(config.maxMetrics).toBe(5000);
      expect(config.retentionPeriod).toBe(24 * 60 * 60 * 1000); // Unchanged
    });

    it('should update thresholds', () => {
      const newThresholds = [
        {
          metric: 'test_metric',
          warning: 50,
          critical: 75,
        },
      ];

      performanceMonitor.updateConfig({ thresholds: newThresholds });
      const config = performanceMonitor.getConfig();

      expect(config.thresholds).toHaveLength(1);
      expect(config.thresholds[0]).toEqual(newThresholds[0]);
    });

    it('should emit config update event', () => {
      const eventHandler = jest.fn();
      performanceMonitor.on('config_updated', eventHandler);

      performanceMonitor.updateConfig({ samplingInterval: 2000 });
      expect(eventHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          samplingInterval: 2000,
        })
      );
    });
  });

  describe('Monitoring Control', () => {
    it('should start and stop monitoring', () => {
      const startHandler = jest.fn();
      const stopHandler = jest.fn();
      performanceMonitor.on('monitoring_started', startHandler);
      performanceMonitor.on('monitoring_stopped', stopHandler);

      performanceMonitor.startMonitoring();
      expect(startHandler).toHaveBeenCalled();

      performanceMonitor.stopMonitoring();
      expect(stopHandler).toHaveBeenCalled();
    });

    it('should not start monitoring multiple times', () => {
      const startHandler = jest.fn();
      performanceMonitor.on('monitoring_started', startHandler);

      performanceMonitor.startMonitoring();
      performanceMonitor.startMonitoring();
      expect(startHandler).toHaveBeenCalledTimes(1);
    });
  });

  describe('Cleanup', () => {
    it('should clean up old metrics', () => {
      const now = Date.now();
      performanceMonitor.updateConfig({
        retentionPeriod: 1000,
      });

      performanceMonitor.recordMetric('test_metric', 10, now - 2000);
      performanceMonitor.recordMetric('test_metric', 20, now - 500);
      performanceMonitor.recordMetric('test_metric', 30, now);

      performanceMonitor.cleanup();
      const metrics = performanceMonitor.getMetrics('test_metric');
      expect(metrics).toHaveLength(2);
      expect(metrics.map(m => m.value)).toEqual([20, 30]);
    });

    it('should limit number of metrics', () => {
      performanceMonitor.updateConfig({
        maxMetrics: 2,
      });

      performanceMonitor.recordMetric('test_metric', 10);
      performanceMonitor.recordMetric('test_metric', 20);
      performanceMonitor.recordMetric('test_metric', 30);

      const metrics = performanceMonitor.getMetrics('test_metric');
      expect(metrics).toHaveLength(2);
      expect(metrics.map(m => m.value)).toEqual([20, 30]);
    });

    it('should emit cleanup event', () => {
      const eventHandler = jest.fn();
      performanceMonitor.on('cleanup_completed', eventHandler);

      performanceMonitor.cleanup();
      expect(eventHandler).toHaveBeenCalled();
    });
  });
});
