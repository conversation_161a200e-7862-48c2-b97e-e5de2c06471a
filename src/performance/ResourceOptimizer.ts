import { EventEmitter } from 'events';

import { cacheManager } from '../core/CacheManager';
import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface ResourceOptimizationConfig {
  enableImageOptimization: boolean;
  enableScriptOptimization: boolean;
  enableStyleOptimization: boolean;
  enableFontOptimization: boolean;
  enablePreloading: boolean;
  enableLazyLoading: boolean;
  enableResourceHints: boolean;
  enableCompression: boolean;
  enableMinification: boolean;
  enableBundling: boolean;
  enableTreeShaking: boolean;
  enableCodeSplitting: boolean;
  maxResourceSize: number;
  compressionThreshold: number;
  preloadCriticalResources: boolean;
  optimizationLevel: 'conservative' | 'balanced' | 'aggressive';
}

export interface ResourceInfo {
  url: string;
  type: 'script' | 'stylesheet' | 'image' | 'font' | 'document' | 'other';
  size: number;
  compressed: boolean;
  cached: boolean;
  critical: boolean;
  loadTime: number;
  priority: 'high' | 'medium' | 'low';
  optimizations: string[];
}

export interface OptimizationResult {
  id: string;
  timestamp: number;
  resource: ResourceInfo;
  originalSize: number;
  optimizedSize: number;
  savings: number;
  savingsPercentage: number;
  optimizations: Array<{
    type: string;
    description: string;
    savings: number;
  }>;
  loadTimeImprovement: number;
}

export interface ResourceBundle {
  id: string;
  name: string;
  type: 'script' | 'stylesheet';
  resources: string[];
  size: number;
  compressed: boolean;
  critical: boolean;
  loadOrder: number;
}

export interface PreloadStrategy {
  resource: string;
  type: 'preload' | 'prefetch' | 'preconnect' | 'dns-prefetch';
  priority: 'high' | 'medium' | 'low';
  condition?: string;
}

export class ResourceOptimizer extends EventEmitter {
  private static instance: ResourceOptimizer;
  private config: ResourceOptimizationConfig;
  private resourceCache: Map<string, ResourceInfo> = new Map();
  private optimizationResults: Map<string, OptimizationResult> = new Map();
  private resourceBundles: Map<string, ResourceBundle> = new Map();
  private preloadStrategies: Map<string, PreloadStrategy> = new Map();
  private criticalResources: Set<string> = new Set();

  private constructor() {
    super();
    this.config = {
      enableImageOptimization: true,
      enableScriptOptimization: true,
      enableStyleOptimization: true,
      enableFontOptimization: true,
      enablePreloading: true,
      enableLazyLoading: true,
      enableResourceHints: true,
      enableCompression: true,
      enableMinification: true,
      enableBundling: true,
      enableTreeShaking: true,
      enableCodeSplitting: true,
      maxResourceSize: 10 * 1024 * 1024, // 10MB
      compressionThreshold: 1024, // 1KB
      preloadCriticalResources: true,
      optimizationLevel: 'balanced',
    };

    this.initializeResourceOptimizer();
  }

  public static getInstance(): ResourceOptimizer {
    if (!ResourceOptimizer.instance) {
      ResourceOptimizer.instance = new ResourceOptimizer();
    }
    return ResourceOptimizer.instance;
  }

  private async initializeResourceOptimizer(): Promise<void> {
    // Load configuration
    const optimizerConfig = configManager.get('resourceOptimizer', {});
    this.config = { ...this.config, ...optimizerConfig };

    // Setup resource monitoring
    this.setupResourceMonitoring();

    // Identify critical resources
    await this.identifyCriticalResources();

    // Setup optimization strategies
    this.setupOptimizationStrategies();

    logger.info('Resource optimizer initialized', {
      optimizationLevel: this.config.optimizationLevel,
      enabledOptimizations: this.getEnabledOptimizations(),
    });
  }

  public async optimizeResource(
    url: string,
    type: string,
    content?: ArrayBuffer
  ): Promise<OptimizationResult> {
    const startTime = performance.now();
    const resourceId = `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Get or create resource info
      let resourceInfo = this.resourceCache.get(url);
      if (!resourceInfo) {
        resourceInfo = await this.analyzeResource(url, type, content);
        this.resourceCache.set(url, resourceInfo);
      }

      const originalSize = resourceInfo.size;
      const optimizations: Array<{ type: string; description: string; savings: number }> = [];
      let optimizedSize = originalSize;
      let loadTimeImprovement = 0;

      // Apply optimizations based on resource type
      switch (resourceInfo.type) {
        case 'image':
          if (this.config.enableImageOptimization) {
            const imageOpt = await this.optimizeImage(resourceInfo);
            optimizations.push(...imageOpt.optimizations);
            optimizedSize = imageOpt.size;
            loadTimeImprovement += imageOpt.loadTimeImprovement;
          }
          break;

        case 'script':
          if (this.config.enableScriptOptimization) {
            const scriptOpt = await this.optimizeScript(resourceInfo);
            optimizations.push(...scriptOpt.optimizations);
            optimizedSize = scriptOpt.size;
            loadTimeImprovement += scriptOpt.loadTimeImprovement;
          }
          break;

        case 'stylesheet':
          if (this.config.enableStyleOptimization) {
            const styleOpt = await this.optimizeStylesheet(resourceInfo);
            optimizations.push(...styleOpt.optimizations);
            optimizedSize = styleOpt.size;
            loadTimeImprovement += styleOpt.loadTimeImprovement;
          }
          break;

        case 'font':
          if (this.config.enableFontOptimization) {
            const fontOpt = await this.optimizeFont(resourceInfo);
            optimizations.push(...fontOpt.optimizations);
            optimizedSize = fontOpt.size;
            loadTimeImprovement += fontOpt.loadTimeImprovement;
          }
          break;
      }

      // Apply general optimizations
      if (this.config.enableCompression && originalSize > this.config.compressionThreshold) {
        const compressionSavings = this.estimateCompressionSavings(originalSize, resourceInfo.type);
        optimizations.push({
          type: 'compression',
          description: 'Gzip/Brotli compression applied',
          savings: compressionSavings,
        });
        optimizedSize -= compressionSavings;
      }

      const savings = originalSize - optimizedSize;
      const savingsPercentage = originalSize > 0 ? (savings / originalSize) * 100 : 0;

      const result: OptimizationResult = {
        id: resourceId,
        timestamp: Date.now(),
        resource: resourceInfo,
        originalSize,
        optimizedSize,
        savings,
        savingsPercentage,
        optimizations,
        loadTimeImprovement,
      };

      this.optimizationResults.set(resourceId, result);

      // Update resource info with optimizations
      resourceInfo.optimizations = optimizations.map(opt => opt.type);
      resourceInfo.size = optimizedSize;

      this.emit('resource_optimized', result);
      logger.debug('Resource optimized', {
        url,
        originalSize,
        optimizedSize,
        savings,
        savingsPercentage,
        loadTimeImprovement,
      });

      return result;
    } catch (error) {
      logger.error('Resource optimization failed', error, { url, type });
      throw error;
    }
  }

  private async analyzeResource(
    url: string,
    type: string,
    content?: ArrayBuffer
  ): Promise<ResourceInfo> {
    // Estimate resource size and properties
    const size = content ? content.byteLength : this.estimateResourceSize(url, type);
    const critical = this.criticalResources.has(url);
    const priority = critical ? 'high' : this.determinePriority(url, type);

    return {
      url,
      type: type as ResourceInfo['type'],
      size,
      compressed: false,
      cached: await cacheManager.has(url),
      critical,
      loadTime: 0, // Would be measured during actual loading
      priority,
      optimizations: [],
    };
  }

  private async optimizeImage(resource: ResourceInfo): Promise<{
    size: number;
    optimizations: Array<{ type: string; description: string; savings: number }>;
    loadTimeImprovement: number;
  }> {
    const optimizations = [];
    let size = resource.size;
    let loadTimeImprovement = 0;

    // Format optimization
    const formatSavings = size * 0.3; // Assume 30% savings from format optimization
    optimizations.push({
      type: 'format_optimization',
      description: 'Converted to optimal format (WebP/AVIF)',
      savings: formatSavings,
    });
    size -= formatSavings;
    loadTimeImprovement += 200; // 200ms improvement

    // Quality optimization
    const qualitySavings = size * 0.2; // Assume 20% savings from quality optimization
    optimizations.push({
      type: 'quality_optimization',
      description: 'Optimized quality settings',
      savings: qualitySavings,
    });
    size -= qualitySavings;

    // Responsive images
    if (resource.size > 500 * 1024) {
      // Large images
      const responsiveSavings = size * 0.4; // 40% savings for responsive images
      optimizations.push({
        type: 'responsive_images',
        description: 'Generated responsive image variants',
        savings: responsiveSavings,
      });
      size -= responsiveSavings;
      loadTimeImprovement += 300;
    }

    return { size, optimizations, loadTimeImprovement };
  }

  private async optimizeScript(resource: ResourceInfo): Promise<{
    size: number;
    optimizations: Array<{ type: string; description: string; savings: number }>;
    loadTimeImprovement: number;
  }> {
    const optimizations = [];
    let size = resource.size;
    let loadTimeImprovement = 0;

    // Minification
    if (this.config.enableMinification) {
      const minificationSavings = size * 0.25; // 25% savings from minification
      optimizations.push({
        type: 'minification',
        description: 'JavaScript minification applied',
        savings: minificationSavings,
      });
      size -= minificationSavings;
      loadTimeImprovement += 100;
    }

    // Tree shaking
    if (this.config.enableTreeShaking) {
      const treeShakingSavings = size * 0.15; // 15% savings from tree shaking
      optimizations.push({
        type: 'tree_shaking',
        description: 'Removed unused code',
        savings: treeShakingSavings,
      });
      size -= treeShakingSavings;
      loadTimeImprovement += 150;
    }

    // Code splitting
    if (this.config.enableCodeSplitting && resource.size > 100 * 1024) {
      optimizations.push({
        type: 'code_splitting',
        description: 'Split into smaller chunks',
        savings: 0, // No size savings, but better loading
      });
      loadTimeImprovement += 300;
    }

    return { size, optimizations, loadTimeImprovement };
  }

  private async optimizeStylesheet(resource: ResourceInfo): Promise<{
    size: number;
    optimizations: Array<{ type: string; description: string; savings: number }>;
    loadTimeImprovement: number;
  }> {
    const optimizations = [];
    let size = resource.size;
    let loadTimeImprovement = 0;

    // CSS minification
    if (this.config.enableMinification) {
      const minificationSavings = size * 0.2; // 20% savings from CSS minification
      optimizations.push({
        type: 'css_minification',
        description: 'CSS minification applied',
        savings: minificationSavings,
      });
      size -= minificationSavings;
      loadTimeImprovement += 50;
    }

    // Unused CSS removal
    const unusedCSSSavings = size * 0.3; // 30% savings from removing unused CSS
    optimizations.push({
      type: 'unused_css_removal',
      description: 'Removed unused CSS rules',
      savings: unusedCSSSavings,
    });
    size -= unusedCSSSavings;
    loadTimeImprovement += 100;

    // Critical CSS extraction
    if (resource.critical) {
      optimizations.push({
        type: 'critical_css',
        description: 'Extracted critical CSS for inline loading',
        savings: 0, // No size savings, but better loading
      });
      loadTimeImprovement += 200;
    }

    return { size, optimizations, loadTimeImprovement };
  }

  private async optimizeFont(resource: ResourceInfo): Promise<{
    size: number;
    optimizations: Array<{ type: string; description: string; savings: number }>;
    loadTimeImprovement: number;
  }> {
    const optimizations = [];
    let size = resource.size;
    let loadTimeImprovement = 0;

    // Font subsetting
    const subsettingSavings = size * 0.4; // 40% savings from font subsetting
    optimizations.push({
      type: 'font_subsetting',
      description: 'Removed unused glyphs',
      savings: subsettingSavings,
    });
    size -= subsettingSavings;
    loadTimeImprovement += 150;

    // Format optimization
    const formatSavings = size * 0.2; // 20% savings from WOFF2 format
    optimizations.push({
      type: 'font_format',
      description: 'Converted to WOFF2 format',
      savings: formatSavings,
    });
    size -= formatSavings;

    // Font display optimization
    optimizations.push({
      type: 'font_display',
      description: 'Optimized font-display property',
      savings: 0, // No size savings, but better loading
    });
    loadTimeImprovement += 100;

    return { size, optimizations, loadTimeImprovement };
  }

  public async createResourceBundle(bundleData: {
    name: string;
    type: 'script' | 'stylesheet';
    resources: string[];
    critical?: boolean;
  }): Promise<ResourceBundle> {
    const bundleId = `bundle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Calculate total size
    let totalSize = 0;
    for (const resourceUrl of bundleData.resources) {
      const resource = this.resourceCache.get(resourceUrl);
      if (resource) {
        totalSize += resource.size;
      }
    }

    // Apply bundling optimizations
    const bundlingOverhead = totalSize * 0.05; // 5% overhead for bundling
    const compressionBonus = totalSize * 0.1; // 10% additional compression for bundles
    const optimizedSize = totalSize + bundlingOverhead - compressionBonus;

    const bundle: ResourceBundle = {
      id: bundleId,
      name: bundleData.name,
      type: bundleData.type,
      resources: bundleData.resources,
      size: optimizedSize,
      compressed: true,
      critical: bundleData.critical || false,
      loadOrder: this.calculateLoadOrder(bundleData.critical || false),
    };

    this.resourceBundles.set(bundleId, bundle);

    this.emit('resource_bundle_created', bundle);
    logger.info('Resource bundle created', {
      bundleId,
      name: bundle.name,
      type: bundle.type,
      resourceCount: bundle.resources.length,
      size: bundle.size,
    });

    return bundle;
  }

  public generatePreloadStrategies(): PreloadStrategy[] {
    const strategies: PreloadStrategy[] = [];

    // Preload critical resources
    if (this.config.preloadCriticalResources) {
      for (const url of this.criticalResources) {
        const resource = this.resourceCache.get(url);
        if (resource) {
          strategies.push({
            resource: url,
            type: 'preload',
            priority: 'high',
          });
        }
      }
    }

    // Prefetch likely next resources
    for (const [url, resource] of this.resourceCache.entries()) {
      if (!resource.critical && resource.priority === 'medium') {
        strategies.push({
          resource: url,
          type: 'prefetch',
          priority: 'medium',
        });
      }
    }

    // DNS prefetch for external domains
    const externalDomains = this.getExternalDomains();
    for (const domain of externalDomains) {
      strategies.push({
        resource: domain,
        type: 'dns-prefetch',
        priority: 'low',
      });
    }

    return strategies;
  }

  private estimateResourceSize(url: string, type: string): number {
    // Estimate based on resource type and URL
    const sizeEstimates = {
      script: 50 * 1024, // 50KB
      stylesheet: 20 * 1024, // 20KB
      image: 100 * 1024, // 100KB
      font: 30 * 1024, // 30KB
      document: 10 * 1024, // 10KB
      other: 5 * 1024, // 5KB
    };

    return sizeEstimates[type as keyof typeof sizeEstimates] || sizeEstimates.other;
  }

  private estimateCompressionSavings(size: number, type: string): number {
    // Compression ratios by resource type
    const compressionRatios = {
      script: 0.7, // 70% compression
      stylesheet: 0.6, // 60% compression
      document: 0.8, // 80% compression
      other: 0.5, // 50% compression
    };

    const ratio =
      compressionRatios[type as keyof typeof compressionRatios] || compressionRatios.other;
    return size * ratio;
  }

  private determinePriority(url: string, type: string): 'high' | 'medium' | 'low' {
    if (this.criticalResources.has(url)) return 'high';
    if (type === 'stylesheet' || type === 'script') return 'medium';
    return 'low';
  }

  private calculateLoadOrder(critical: boolean): number {
    return critical ? 1 : 2;
  }

  private async identifyCriticalResources(): Promise<void> {
    // In a real implementation, this would analyze the critical rendering path
    // For now, add some common critical resources
    this.criticalResources.add('/css/critical.css');
    this.criticalResources.add('/js/critical.js');
    this.criticalResources.add('/fonts/main.woff2');
  }

  private setupResourceMonitoring(): void {
    // Monitor resource loading performance
    if (typeof window !== 'undefined') {
      const observer = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'resource') {
            this.analyzeResourcePerformance(entry as PerformanceResourceTiming);
          }
        });
      });

      try {
        observer.observe({ entryTypes: ['resource'] });
      } catch (error) {
        logger.warn('Failed to setup resource monitoring', { error });
      }
    }
  }

  private analyzeResourcePerformance(entry: PerformanceResourceTiming): void {
    const resource = this.resourceCache.get(entry.name);
    if (resource) {
      resource.loadTime = entry.duration;

      // Update cache status
      resource.cached = entry.transferSize === 0 && entry.decodedBodySize > 0;

      this.emit('resource_performance_analyzed', { resource, entry });
    }
  }

  private setupOptimizationStrategies(): void {
    // Setup automatic optimization based on configuration level
    switch (this.config.optimizationLevel) {
      case 'aggressive':
        this.config.enableTreeShaking = true;
        this.config.enableCodeSplitting = true;
        this.config.compressionThreshold = 512; // 512 bytes
        break;
      case 'balanced':
        this.config.enableTreeShaking = true;
        this.config.enableCodeSplitting = false;
        this.config.compressionThreshold = 1024; // 1KB
        break;
      case 'conservative':
        this.config.enableTreeShaking = false;
        this.config.enableCodeSplitting = false;
        this.config.compressionThreshold = 2048; // 2KB
        break;
    }
  }

  private getEnabledOptimizations(): string[] {
    const enabled = [];
    if (this.config.enableImageOptimization) enabled.push('image');
    if (this.config.enableScriptOptimization) enabled.push('script');
    if (this.config.enableStyleOptimization) enabled.push('style');
    if (this.config.enableFontOptimization) enabled.push('font');
    if (this.config.enableCompression) enabled.push('compression');
    if (this.config.enableMinification) enabled.push('minification');
    if (this.config.enableBundling) enabled.push('bundling');
    return enabled;
  }

  private getExternalDomains(): string[] {
    const domains = new Set<string>();

    for (const url of this.resourceCache.keys()) {
      try {
        const urlObj = new URL(url);
        if (urlObj.hostname !== window.location.hostname) {
          domains.add(urlObj.hostname);
        }
      } catch {
        // Invalid URL, skip
      }
    }

    return Array.from(domains);
  }

  // Getters
  public getOptimizationResults(): OptimizationResult[] {
    return Array.from(this.optimizationResults.values());
  }

  public getResourceBundles(): ResourceBundle[] {
    return Array.from(this.resourceBundles.values());
  }

  public getResourceCache(): ResourceInfo[] {
    return Array.from(this.resourceCache.values());
  }

  public getCriticalResources(): string[] {
    return Array.from(this.criticalResources);
  }

  public updateConfig(config: Partial<ResourceOptimizationConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('resourceOptimizer', this.config);
    this.setupOptimizationStrategies();
    this.emit('config_updated', this.config);
  }

  public getConfig(): ResourceOptimizationConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const resourceOptimizer = ResourceOptimizer.getInstance();
