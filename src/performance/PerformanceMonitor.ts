import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  correlationId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface PerformanceAlert {
  id: string;
  metric: string;
  value: number;
  threshold: number;
  severity: 'warning' | 'critical';
  timestamp: number;
  resolved: boolean;
}

export interface PerformanceThreshold {
  metric: string;
  warning: number;
  critical: number;
  tags?: Record<string, string>;
}

export interface PerformanceConfig {
  samplingInterval: number;
  retentionPeriod: number;
  maxMetrics: number;
  thresholds: PerformanceThreshold[];
  enabledMetrics: string[];
  enableRealTimeMonitoring: boolean;
  enablePerformanceBudgets: boolean;
  enableAutomaticOptimization: boolean;
  alertingEnabled: boolean;
  remoteReportingEnabled: boolean;
  remoteReportingUrl?: string;
}

export class PerformanceMonitor extends EventEmitter {
  private static instance: PerformanceMonitor;
  private config: PerformanceConfig;
  private metrics: Map<string, PerformanceMetric[]>;
  private thresholds: Map<string, PerformanceThreshold>;
  private samplingInterval: NodeJS.Timeout | null;
  private alerts: PerformanceAlert[];
  private performanceObserver: PerformanceObserver | null;
  private sessionId: string;
  private correlationId: string | null;

  private constructor() {
    super();
    this.config = {
      samplingInterval: 1000, // 1 second
      retentionPeriod: 24 * 60 * 60 * 1000, // 24 hours
      maxMetrics: 10000,
      thresholds: [],
      enabledMetrics: [
        'memory_usage',
        'cpu_usage',
        'response_time',
        'frame_rate',
        'network_latency',
        'dom_operations',
        'js_heap_size',
        'event_loop_lag',
        'first_contentful_paint',
        'largest_contentful_paint',
        'cumulative_layout_shift',
        'first_input_delay',
      ],
      enableRealTimeMonitoring: true,
      enablePerformanceBudgets: true,
      enableAutomaticOptimization: false,
      alertingEnabled: true,
      remoteReportingEnabled: false,
    };
    this.metrics = new Map();
    this.thresholds = new Map();
    this.samplingInterval = null;
    this.alerts = [];
    this.performanceObserver = null;
    this.sessionId = this.generateSessionId();
    this.correlationId = null;
    this.initializeThresholds();
    this.initializePerformanceObserver();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private initializeThresholds(): void {
    const defaultThresholds: PerformanceThreshold[] = [
      {
        metric: 'memory_usage',
        warning: 80, // 80% of available memory
        critical: 90,
      },
      {
        metric: 'cpu_usage',
        warning: 70, // 70% CPU usage
        critical: 85,
      },
      {
        metric: 'response_time',
        warning: 100, // 100ms
        critical: 200,
      },
      {
        metric: 'frame_rate',
        warning: 30, // 30 FPS
        critical: 20,
      },
      {
        metric: 'network_latency',
        warning: 100, // 100ms
        critical: 200,
      },
      {
        metric: 'dom_operations',
        warning: 1000, // 1000 operations per second
        critical: 2000,
      },
      {
        metric: 'js_heap_size',
        warning: 80, // 80% of max heap size
        critical: 90,
      },
      {
        metric: 'event_loop_lag',
        warning: 50, // 50ms
        critical: 100,
      },
    ];

    defaultThresholds.forEach(threshold => {
      this.thresholds.set(threshold.metric, threshold);
    });
  }

  public startMonitoring(): void {
    if (this.samplingInterval) {
      return;
    }

    this.samplingInterval = setInterval(() => {
      this.collectMetrics();
    }, this.config.samplingInterval);

    this.emit('monitoring_started');
  }

  public stopMonitoring(): void {
    if (this.samplingInterval) {
      clearInterval(this.samplingInterval);
      this.samplingInterval = null;
      this.emit('monitoring_stopped');
    }
  }

  private async collectMetrics(): Promise<void> {
    const timestamp = Date.now();

    // Collect memory metrics
    if (this.config.enabledMetrics.includes('memory_usage')) {
      const memoryUsage = process.memoryUsage();
      this.recordMetric(
        'memory_usage',
        (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
        timestamp
      );
    }

    // Collect CPU metrics
    if (this.config.enabledMetrics.includes('cpu_usage')) {
      const cpuUsage = await this.getCPUUsage();
      this.recordMetric('cpu_usage', cpuUsage, timestamp);
    }

    // Collect frame rate
    if (this.config.enabledMetrics.includes('frame_rate')) {
      const frameRate = await this.getFrameRate();
      this.recordMetric('frame_rate', frameRate, timestamp);
    }

    // Collect network latency
    if (this.config.enabledMetrics.includes('network_latency')) {
      const latency = await this.getNetworkLatency();
      this.recordMetric('network_latency', latency, timestamp);
    }

    // Collect DOM operations
    if (this.config.enabledMetrics.includes('dom_operations')) {
      const domOps = await this.getDOMOperations();
      this.recordMetric('dom_operations', domOps, timestamp);
    }

    // Collect JS heap size
    if (this.config.enabledMetrics.includes('js_heap_size')) {
      const heapSize = await this.getJSHeapSize();
      this.recordMetric('js_heap_size', heapSize, timestamp);
    }

    // Collect event loop lag
    if (this.config.enabledMetrics.includes('event_loop_lag')) {
      const lag = await this.getEventLoopLag();
      this.recordMetric('event_loop_lag', lag, timestamp);
    }

    this.cleanupOldMetrics();
  }

  private async getCPUUsage(): Promise<number> {
    // Implementation would use system-specific APIs
    return 0;
  }

  private async getFrameRate(): Promise<number> {
    // Implementation would use requestAnimationFrame
    return 60;
  }

  private async getNetworkLatency(): Promise<number> {
    // Implementation would measure network requests
    return 0;
  }

  private async getDOMOperations(): Promise<number> {
    // Implementation would track DOM mutations
    return 0;
  }

  private async getJSHeapSize(): Promise<number> {
    const memoryUsage = process.memoryUsage();
    return (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
  }

  private async getEventLoopLag(): Promise<number> {
    // Implementation would measure event loop delay
    return 0;
  }

  public recordMetric(
    name: string,
    value: number,
    timestamp: number = Date.now(),
    tags?: Record<string, string>
  ): void {
    if (!this.config.enabledMetrics.includes(name)) {
      return;
    }

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp,
      tags,
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metrics = this.metrics.get(name)!;
    metrics.push(metric);

    // Check thresholds
    const threshold = this.thresholds.get(name);
    if (threshold) {
      if (value >= threshold.critical) {
        this.emit('threshold_exceeded', {
          metric: name,
          value,
          threshold: threshold.critical,
          level: 'critical',
          timestamp,
        });
      } else if (value >= threshold.warning) {
        this.emit('threshold_exceeded', {
          metric: name,
          value,
          threshold: threshold.warning,
          level: 'warning',
          timestamp,
        });
      }
    }

    this.emit('metric_recorded', metric);
  }

  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - this.config.retentionPeriod;

    for (const [name, metrics] of this.metrics.entries()) {
      const filteredMetrics = metrics.filter(metric => metric.timestamp > cutoffTime);

      if (filteredMetrics.length > this.config.maxMetrics) {
        filteredMetrics.splice(0, filteredMetrics.length - this.config.maxMetrics);
      }

      this.metrics.set(name, filteredMetrics);
    }
  }

  public getMetrics(name: string, startTime?: number, endTime?: number): PerformanceMetric[] {
    const metrics = this.metrics.get(name) || [];

    if (!startTime && !endTime) {
      return metrics;
    }

    return metrics.filter(metric => {
      if (startTime && metric.timestamp < startTime) {
        return false;
      }
      if (endTime && metric.timestamp > endTime) {
        return false;
      }
      return true;
    });
  }

  public getMetricSummary(
    name: string,
    startTime?: number,
    endTime?: number
  ): {
    min: number;
    max: number;
    avg: number;
    count: number;
  } {
    const metrics = this.getMetrics(name, startTime, endTime);

    if (metrics.length === 0) {
      return {
        min: 0,
        max: 0,
        avg: 0,
        count: 0,
      };
    }

    const values = metrics.map(m => m.value);
    return {
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      count: values.length,
    };
  }

  public updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig,
    };

    if (newConfig.thresholds) {
      this.thresholds.clear();
      newConfig.thresholds.forEach(threshold => {
        this.thresholds.set(threshold.metric, threshold);
      });
    }

    this.emit('config_updated', this.config);
  }

  public getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  private initializePerformanceObserver(): void {
    if (typeof PerformanceObserver !== 'undefined') {
      this.performanceObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.recordMetric(entry.name, entry.duration || entry.value, {
            entryType: entry.entryType,
            startTime: entry.startTime.toString(),
          });
        });
      });

      try {
        this.performanceObserver.observe({
          entryTypes: ['measure', 'navigation', 'paint', 'largest-contentful-paint'],
        });
      } catch (error) {
        logger.warn('Failed to initialize PerformanceObserver', { error });
      }
    }
  }

  private generateSessionId(): string {
    return `perf_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public setCorrelationId(id: string): void {
    this.correlationId = id;
  }

  public getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  public resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.emit('alert_resolved', alert);
      logger.info('Performance alert resolved', { alertId, metric: alert.metric });
    }
  }

  public clearResolvedAlerts(): void {
    const unresolvedCount = this.alerts.filter(a => !a.resolved).length;
    this.alerts = this.alerts.filter(a => !a.resolved);
    logger.info('Cleared resolved performance alerts', {
      clearedCount: this.alerts.length - unresolvedCount,
    });
  }

  private createAlert(metric: string, value: number, threshold: PerformanceThreshold): void {
    const severity = value >= threshold.critical ? 'critical' : 'warning';
    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      metric,
      value,
      threshold: severity === 'critical' ? threshold.critical : threshold.warning,
      severity,
      timestamp: Date.now(),
      resolved: false,
    };

    this.alerts.push(alert);
    this.emit('alert_created', alert);

    logger.warn('Performance alert created', {
      alertId: alert.id,
      metric,
      value,
      threshold: alert.threshold,
      severity,
    });
  }

  public measureOperation<T>(name: string, operation: () => T): T {
    const startTime = performance.now();
    const result = operation();
    const duration = performance.now() - startTime;

    this.recordMetric(`operation_${name}`, duration, {
      operationType: 'sync',
      correlationId: this.correlationId,
    });

    return result;
  }

  public async measureAsyncOperation<T>(name: string, operation: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    const result = await operation();
    const duration = performance.now() - startTime;

    this.recordMetric(`operation_${name}`, duration, {
      operationType: 'async',
      correlationId: this.correlationId,
    });

    return result;
  }

  public cleanup(): void {
    this.stopMonitoring();

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    this.metrics.clear();
    this.alerts = [];
    this.emit('cleanup_completed');
  }
}
